
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/lib/services</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> src/lib/services</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">25.11% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>227/904</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">22% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>171/777</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">50.76% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>33/65</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">24.86% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>222/893</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="freeAccountService.ts"><a href="freeAccountService.ts.html">freeAccountService.ts</a></td>
	<td data-value="3.36" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 3%"></div><div class="cover-empty" style="width: 97%"></div></div>
	</td>
	<td data-value="3.36" class="pct low">3.36%</td>
	<td data-value="119" class="abs low">4/119</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="112" class="abs low">0/112</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="6" class="abs low">0/6</td>
	<td data-value="3.36" class="pct low">3.36%</td>
	<td data-value="119" class="abs low">4/119</td>
	</tr>

<tr>
	<td class="file low" data-value="limitHandler.ts"><a href="limitHandler.ts.html">limitHandler.ts</a></td>
	<td data-value="45.5" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 45%"></div><div class="cover-empty" style="width: 55%"></div></div>
	</td>
	<td data-value="45.5" class="pct low">45.5%</td>
	<td data-value="200" class="abs low">91/200</td>
	<td data-value="40.12" class="pct low">40.12%</td>
	<td data-value="157" class="abs low">63/157</td>
	<td data-value="71.42" class="pct high">71.42%</td>
	<td data-value="21" class="abs high">15/21</td>
	<td data-value="45.07" class="pct low">45.07%</td>
	<td data-value="193" class="abs low">87/193</td>
	</tr>

<tr>
	<td class="file medium" data-value="permissionService.ts"><a href="permissionService.ts.html">permissionService.ts</a></td>
	<td data-value="59.82" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 59%"></div><div class="cover-empty" style="width: 41%"></div></div>
	</td>
	<td data-value="59.82" class="pct medium">59.82%</td>
	<td data-value="112" class="abs medium">67/112</td>
	<td data-value="52.08" class="pct medium">52.08%</td>
	<td data-value="144" class="abs medium">75/144</td>
	<td data-value="84.61" class="pct high">84.61%</td>
	<td data-value="13" class="abs high">11/13</td>
	<td data-value="60" class="pct medium">60%</td>
	<td data-value="110" class="abs medium">66/110</td>
	</tr>

<tr>
	<td class="file medium" data-value="planValidation.ts"><a href="planValidation.ts.html">planValidation.ts</a></td>
	<td data-value="51.58" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 51%"></div><div class="cover-empty" style="width: 49%"></div></div>
	</td>
	<td data-value="51.58" class="pct medium">51.58%</td>
	<td data-value="126" class="abs medium">65/126</td>
	<td data-value="33.33" class="pct low">33.33%</td>
	<td data-value="99" class="abs low">33/99</td>
	<td data-value="63.63" class="pct medium">63.63%</td>
	<td data-value="11" class="abs medium">7/11</td>
	<td data-value="52.41" class="pct medium">52.41%</td>
	<td data-value="124" class="abs medium">65/124</td>
	</tr>

<tr>
	<td class="file low" data-value="stripeWebhookHandlers.ts"><a href="stripeWebhookHandlers.ts.html">stripeWebhookHandlers.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="203" class="abs low">0/203</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="183" class="abs low">0/183</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="7" class="abs low">0/7</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="203" class="abs low">0/203</td>
	</tr>

<tr>
	<td class="file low" data-value="userManagement.ts"><a href="userManagement.ts.html">userManagement.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="144" class="abs low">0/144</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="82" class="abs low">0/82</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="7" class="abs low">0/7</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="144" class="abs low">0/144</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-24T09:43:52.488Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    