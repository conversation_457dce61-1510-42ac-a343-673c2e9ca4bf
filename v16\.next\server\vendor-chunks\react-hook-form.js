"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-hook-form";
exports.ids = ["vendor-chunks/react-hook-form"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/react-hook-form/dist/index.esm.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Controller: () => (/* binding */ Controller),\n/* harmony export */   Form: () => (/* binding */ Form),\n/* harmony export */   FormProvider: () => (/* binding */ FormProvider),\n/* harmony export */   appendErrors: () => (/* binding */ appendErrors),\n/* harmony export */   createFormControl: () => (/* binding */ createFormControl),\n/* harmony export */   get: () => (/* binding */ get),\n/* harmony export */   set: () => (/* binding */ set),\n/* harmony export */   useController: () => (/* binding */ useController),\n/* harmony export */   useFieldArray: () => (/* binding */ useFieldArray),\n/* harmony export */   useForm: () => (/* binding */ useForm),\n/* harmony export */   useFormContext: () => (/* binding */ useFormContext),\n/* harmony export */   useFormState: () => (/* binding */ useFormState),\n/* harmony export */   useWatch: () => (/* binding */ useWatch)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\nconst _excluded = [\"children\"],\n  _excluded2 = [\"control\", \"onSubmit\", \"children\", \"action\", \"method\", \"headers\", \"encType\", \"onError\", \"render\", \"onSuccess\", \"validateStatus\"],\n  _excluded3 = [\"_f\"],\n  _excluded4 = [\"name\"],\n  _excluded5 = [\"_f\"],\n  _excluded6 = [\"ref\", \"message\", \"type\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return typeof key === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (typeof input !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (typeof res !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\n\nvar isCheckBoxInput = element => element.type === 'checkbox';\nvar isDateObject = value => value instanceof Date;\nvar isNullOrUndefined = value => value == null;\nconst isObjectType = value => typeof value === 'object';\nvar isObject = value => !isNullOrUndefined(value) && !Array.isArray(value) && isObjectType(value) && !isDateObject(value);\nvar getEventValue = event => isObject(event) && event.target ? isCheckBoxInput(event.target) ? event.target.checked : event.target.value : event;\nvar getNodeParentName = name => name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\nvar isNameInFieldArray = (names, name) => names.has(getNodeParentName(name));\nvar isPlainObject = tempObject => {\n  const prototypeCopy = tempObject.constructor && tempObject.constructor.prototype;\n  return isObject(prototypeCopy) && prototypeCopy.hasOwnProperty('isPrototypeOf');\n};\nvar isWeb =  false && 0;\nfunction cloneObject(data) {\n  let copy;\n  const isArray = Array.isArray(data);\n  const isFileListInstance = typeof FileList !== 'undefined' ? data instanceof FileList : false;\n  if (data instanceof Date) {\n    copy = new Date(data);\n  } else if (data instanceof Set) {\n    copy = new Set(data);\n  } else if (!(isWeb && (data instanceof Blob || isFileListInstance)) && (isArray || isObject(data))) {\n    copy = isArray ? [] : {};\n    if (!isArray && !isPlainObject(data)) {\n      copy = data;\n    } else {\n      for (const key in data) {\n        if (data.hasOwnProperty(key)) {\n          copy[key] = cloneObject(data[key]);\n        }\n      }\n    }\n  } else {\n    return data;\n  }\n  return copy;\n}\nvar compact = value => Array.isArray(value) ? value.filter(Boolean) : [];\nvar isUndefined = val => val === undefined;\nvar get = (object, path, defaultValue) => {\n  if (!path || !isObject(object)) {\n    return defaultValue;\n  }\n  const result = compact(path.split(/[,[\\].]+?/)).reduce((result, key) => isNullOrUndefined(result) ? result : result[key], object);\n  return isUndefined(result) || result === object ? isUndefined(object[path]) ? defaultValue : object[path] : result;\n};\nvar isBoolean = value => typeof value === 'boolean';\nvar isKey = value => /^\\w*$/.test(value);\nvar stringToPath = input => compact(input.replace(/[\"|']|\\]/g, '').split(/\\.|\\[/));\nvar set = (object, path, value) => {\n  let index = -1;\n  const tempPath = isKey(path) ? [path] : stringToPath(path);\n  const length = tempPath.length;\n  const lastIndex = length - 1;\n  while (++index < length) {\n    const key = tempPath[index];\n    let newValue = value;\n    if (index !== lastIndex) {\n      const objValue = object[key];\n      newValue = isObject(objValue) || Array.isArray(objValue) ? objValue : !isNaN(+tempPath[index + 1]) ? [] : {};\n    }\n    if (key === '__proto__' || key === 'constructor' || key === 'prototype') {\n      return;\n    }\n    object[key] = newValue;\n    object = object[key];\n  }\n};\nconst EVENTS = {\n  BLUR: 'blur',\n  FOCUS_OUT: 'focusout',\n  CHANGE: 'change'\n};\nconst VALIDATION_MODE = {\n  onBlur: 'onBlur',\n  onChange: 'onChange',\n  onSubmit: 'onSubmit',\n  onTouched: 'onTouched',\n  all: 'all'\n};\nconst INPUT_VALIDATION_RULES = {\n  max: 'max',\n  min: 'min',\n  maxLength: 'maxLength',\n  minLength: 'minLength',\n  pattern: 'pattern',\n  required: 'required',\n  validate: 'validate'\n};\nconst HookFormContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nconst useFormContext = () => react__WEBPACK_IMPORTED_MODULE_0__.useContext(HookFormContext);\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nconst FormProvider = props => {\n  const {\n      children\n    } = props,\n    data = _objectWithoutProperties(props, _excluded);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(HookFormContext.Provider, {\n    value: data\n  }, children);\n};\nvar getProxyFormState = (formState, control, localProxyFormState, isRoot = true) => {\n  const result = {\n    defaultValues: control._defaultValues\n  };\n  for (const key in formState) {\n    Object.defineProperty(result, key, {\n      get: () => {\n        const _key = key;\n        if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n          control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n        }\n        localProxyFormState && (localProxyFormState[_key] = true);\n        return formState[_key];\n      }\n    });\n  }\n  return result;\n};\nconst useIsomorphicLayoutEffect =  false ? 0 : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useFormState(props) {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    disabled,\n    name,\n    exact\n  } = props || {};\n  const [formState, updateFormState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._formState);\n  const _localProxyFormState = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n    isDirty: false,\n    isLoading: false,\n    dirtyFields: false,\n    touchedFields: false,\n    validatingFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false\n  });\n  useIsomorphicLayoutEffect(() => control._subscribe({\n    name: name,\n    formState: _localProxyFormState.current,\n    exact,\n    callback: formState => {\n      !disabled && updateFormState(_objectSpread(_objectSpread({}, control._formState), formState));\n    }\n  }), [name, disabled, exact]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    _localProxyFormState.current.isValid && control._setValid(true);\n  }, [control]);\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => getProxyFormState(formState, control, _localProxyFormState.current, false), [formState, control]);\n}\nvar isString = value => typeof value === 'string';\nvar generateWatchOutput = (names, _names, formValues, isGlobal, defaultValue) => {\n  if (isString(names)) {\n    isGlobal && _names.watch.add(names);\n    return get(formValues, names, defaultValue);\n  }\n  if (Array.isArray(names)) {\n    return names.map(fieldName => (isGlobal && _names.watch.add(fieldName), get(formValues, fieldName)));\n  }\n  isGlobal && (_names.watchAll = true);\n  return formValues;\n};\n\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */\nfunction useWatch(props) {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    name,\n    defaultValue,\n    disabled,\n    exact\n  } = props || {};\n  const _defaultValue = react__WEBPACK_IMPORTED_MODULE_0__.useRef(defaultValue);\n  const [value, updateValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._getWatch(name, _defaultValue.current));\n  useIsomorphicLayoutEffect(() => control._subscribe({\n    name: name,\n    formState: {\n      values: true\n    },\n    exact,\n    callback: formState => !disabled && updateValue(generateWatchOutput(name, control._names, formState.values || control._formValues, false, _defaultValue.current))\n  }), [name, control, disabled, exact]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => control._removeUnmounted());\n  return value;\n}\n\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */\nfunction useController(props) {\n  const methods = useFormContext();\n  const {\n    name,\n    disabled,\n    control = methods.control,\n    shouldUnregister\n  } = props;\n  const isArrayField = isNameInFieldArray(control._names.array, name);\n  const value = useWatch({\n    control,\n    name,\n    defaultValue: get(control._formValues, name, get(control._defaultValues, name, props.defaultValue)),\n    exact: true\n  });\n  const formState = useFormState({\n    control,\n    name,\n    exact: true\n  });\n  const _props = react__WEBPACK_IMPORTED_MODULE_0__.useRef(props);\n  const _registerProps = react__WEBPACK_IMPORTED_MODULE_0__.useRef(control.register(name, _objectSpread(_objectSpread({}, props.rules), {}, {\n    value\n  }, isBoolean(props.disabled) ? {\n    disabled: props.disabled\n  } : {})));\n  const fieldState = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => Object.defineProperties({}, {\n    invalid: {\n      enumerable: true,\n      get: () => !!get(formState.errors, name)\n    },\n    isDirty: {\n      enumerable: true,\n      get: () => !!get(formState.dirtyFields, name)\n    },\n    isTouched: {\n      enumerable: true,\n      get: () => !!get(formState.touchedFields, name)\n    },\n    isValidating: {\n      enumerable: true,\n      get: () => !!get(formState.validatingFields, name)\n    },\n    error: {\n      enumerable: true,\n      get: () => get(formState.errors, name)\n    }\n  }), [formState, name]);\n  const onChange = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(event => _registerProps.current.onChange({\n    target: {\n      value: getEventValue(event),\n      name: name\n    },\n    type: EVENTS.CHANGE\n  }), [name]);\n  const onBlur = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => _registerProps.current.onBlur({\n    target: {\n      value: get(control._formValues, name),\n      name: name\n    },\n    type: EVENTS.BLUR\n  }), [name, control._formValues]);\n  const ref = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(elm => {\n    const field = get(control._fields, name);\n    if (field && elm) {\n      field._f.ref = {\n        focus: () => elm.focus(),\n        select: () => elm.select(),\n        setCustomValidity: message => elm.setCustomValidity(message),\n        reportValidity: () => elm.reportValidity()\n      };\n    }\n  }, [control._fields, name]);\n  const field = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => _objectSpread(_objectSpread({\n    name,\n    value\n  }, isBoolean(disabled) || formState.disabled ? {\n    disabled: formState.disabled || disabled\n  } : {}), {}, {\n    onChange,\n    onBlur,\n    ref\n  }), [name, disabled, formState.disabled, onChange, onBlur, ref, value]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    const _shouldUnregisterField = control._options.shouldUnregister || shouldUnregister;\n    control.register(name, _objectSpread(_objectSpread({}, _props.current.rules), isBoolean(_props.current.disabled) ? {\n      disabled: _props.current.disabled\n    } : {}));\n    const updateMounted = (name, value) => {\n      const field = get(control._fields, name);\n      if (field && field._f) {\n        field._f.mount = value;\n      }\n    };\n    updateMounted(name, true);\n    if (_shouldUnregisterField) {\n      const value = cloneObject(get(control._options.defaultValues, name));\n      set(control._defaultValues, name, value);\n      if (isUndefined(get(control._formValues, name))) {\n        set(control._formValues, name, value);\n      }\n    }\n    !isArrayField && control.register(name);\n    return () => {\n      (isArrayField ? _shouldUnregisterField && !control._state.action : _shouldUnregisterField) ? control.unregister(name) : updateMounted(name, false);\n    };\n  }, [name, control, isArrayField, shouldUnregister]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    control._setDisabledField({\n      disabled,\n      name\n    });\n  }, [disabled, name, control]);\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({\n    field,\n    formState,\n    fieldState\n  }), [field, formState, fieldState]);\n}\n\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */\nconst Controller = props => props.render(useController(props));\nconst flatten = obj => {\n  const output = {};\n  for (const key of Object.keys(obj)) {\n    if (isObjectType(obj[key]) && obj[key] !== null) {\n      const nested = flatten(obj[key]);\n      for (const nestedKey of Object.keys(nested)) {\n        output[`${key}.${nestedKey}`] = nested[nestedKey];\n      }\n    } else {\n      output[key] = obj[key];\n    }\n  }\n  return output;\n};\nconst POST_REQUEST = 'post';\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */\nfunction Form(props) {\n  const methods = useFormContext();\n  const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n  const {\n      control = methods.control,\n      onSubmit,\n      children,\n      action,\n      method = POST_REQUEST,\n      headers,\n      encType,\n      onError,\n      render,\n      onSuccess,\n      validateStatus\n    } = props,\n    rest = _objectWithoutProperties(props, _excluded2);\n  const submit = async event => {\n    let hasError = false;\n    let type = '';\n    await control.handleSubmit(async data => {\n      const formData = new FormData();\n      let formDataJson = '';\n      try {\n        formDataJson = JSON.stringify(data);\n      } catch (_a) {}\n      const flattenFormValues = flatten(control._formValues);\n      for (const key in flattenFormValues) {\n        formData.append(key, flattenFormValues[key]);\n      }\n      if (onSubmit) {\n        await onSubmit({\n          data,\n          event,\n          method,\n          formData,\n          formDataJson\n        });\n      }\n      if (action) {\n        try {\n          const shouldStringifySubmissionData = [headers && headers['Content-Type'], encType].some(value => value && value.includes('json'));\n          const response = await fetch(String(action), {\n            method,\n            headers: _objectSpread(_objectSpread({}, headers), encType ? {\n              'Content-Type': encType\n            } : {}),\n            body: shouldStringifySubmissionData ? formDataJson : formData\n          });\n          if (response && (validateStatus ? !validateStatus(response.status) : response.status < 200 || response.status >= 300)) {\n            hasError = true;\n            onError && onError({\n              response\n            });\n            type = String(response.status);\n          } else {\n            onSuccess && onSuccess({\n              response\n            });\n          }\n        } catch (error) {\n          hasError = true;\n          onError && onError({\n            error\n          });\n        }\n      }\n    })(event);\n    if (hasError && props.control) {\n      props.control._subjects.state.next({\n        isSubmitSuccessful: false\n      });\n      props.control.setError('root.server', {\n        type\n      });\n    }\n  };\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    setMounted(true);\n  }, []);\n  return render ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, render({\n    submit\n  })) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"form\", _objectSpread({\n    noValidate: mounted,\n    action: action,\n    method: method,\n    encType: encType,\n    onSubmit: submit\n  }, rest), children);\n}\nvar appendErrors = (name, validateAllFieldCriteria, errors, type, message) => validateAllFieldCriteria ? _objectSpread(_objectSpread({}, errors[name]), {}, {\n  types: _objectSpread(_objectSpread({}, errors[name] && errors[name].types ? errors[name].types : {}), {}, {\n    [type]: message || true\n  })\n}) : {};\nvar convertToArrayPayload = value => Array.isArray(value) ? value : [value];\nvar createSubject = () => {\n  let _observers = [];\n  const next = value => {\n    for (const observer of _observers) {\n      observer.next && observer.next(value);\n    }\n  };\n  const subscribe = observer => {\n    _observers.push(observer);\n    return {\n      unsubscribe: () => {\n        _observers = _observers.filter(o => o !== observer);\n      }\n    };\n  };\n  const unsubscribe = () => {\n    _observers = [];\n  };\n  return {\n    get observers() {\n      return _observers;\n    },\n    next,\n    subscribe,\n    unsubscribe\n  };\n};\nvar isPrimitive = value => isNullOrUndefined(value) || !isObjectType(value);\nfunction deepEqual(object1, object2) {\n  if (isPrimitive(object1) || isPrimitive(object2)) {\n    return object1 === object2;\n  }\n  if (isDateObject(object1) && isDateObject(object2)) {\n    return object1.getTime() === object2.getTime();\n  }\n  const keys1 = Object.keys(object1);\n  const keys2 = Object.keys(object2);\n  if (keys1.length !== keys2.length) {\n    return false;\n  }\n  for (const key of keys1) {\n    const val1 = object1[key];\n    if (!keys2.includes(key)) {\n      return false;\n    }\n    if (key !== 'ref') {\n      const val2 = object2[key];\n      if (isDateObject(val1) && isDateObject(val2) || isObject(val1) && isObject(val2) || Array.isArray(val1) && Array.isArray(val2) ? !deepEqual(val1, val2) : val1 !== val2) {\n        return false;\n      }\n    }\n  }\n  return true;\n}\nvar isEmptyObject = value => isObject(value) && !Object.keys(value).length;\nvar isFileInput = element => element.type === 'file';\nvar isFunction = value => typeof value === 'function';\nvar isHTMLElement = value => {\n  if (!isWeb) {\n    return false;\n  }\n  const owner = value ? value.ownerDocument : 0;\n  return value instanceof (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement);\n};\nvar isMultipleSelect = element => element.type === `select-multiple`;\nvar isRadioInput = element => element.type === 'radio';\nvar isRadioOrCheckbox = ref => isRadioInput(ref) || isCheckBoxInput(ref);\nvar live = ref => isHTMLElement(ref) && ref.isConnected;\nfunction baseGet(object, updatePath) {\n  const length = updatePath.slice(0, -1).length;\n  let index = 0;\n  while (index < length) {\n    object = isUndefined(object) ? index++ : object[updatePath[index++]];\n  }\n  return object;\n}\nfunction isEmptyArray(obj) {\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction unset(object, path) {\n  const paths = Array.isArray(path) ? path : isKey(path) ? [path] : stringToPath(path);\n  const childObject = paths.length === 1 ? object : baseGet(object, paths);\n  const index = paths.length - 1;\n  const key = paths[index];\n  if (childObject) {\n    delete childObject[key];\n  }\n  if (index !== 0 && (isObject(childObject) && isEmptyObject(childObject) || Array.isArray(childObject) && isEmptyArray(childObject))) {\n    unset(object, paths.slice(0, -1));\n  }\n  return object;\n}\nvar objectHasFunction = data => {\n  for (const key in data) {\n    if (isFunction(data[key])) {\n      return true;\n    }\n  }\n  return false;\n};\nfunction markFieldsDirty(data, fields = {}) {\n  const isParentNodeArray = Array.isArray(data);\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (Array.isArray(data[key]) || isObject(data[key]) && !objectHasFunction(data[key])) {\n        fields[key] = Array.isArray(data[key]) ? [] : {};\n        markFieldsDirty(data[key], fields[key]);\n      } else if (!isNullOrUndefined(data[key])) {\n        fields[key] = true;\n      }\n    }\n  }\n  return fields;\n}\nfunction getDirtyFieldsFromDefaultValues(data, formValues, dirtyFieldsFromValues) {\n  const isParentNodeArray = Array.isArray(data);\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (Array.isArray(data[key]) || isObject(data[key]) && !objectHasFunction(data[key])) {\n        if (isUndefined(formValues) || isPrimitive(dirtyFieldsFromValues[key])) {\n          dirtyFieldsFromValues[key] = Array.isArray(data[key]) ? markFieldsDirty(data[key], []) : _objectSpread({}, markFieldsDirty(data[key]));\n        } else {\n          getDirtyFieldsFromDefaultValues(data[key], isNullOrUndefined(formValues) ? {} : formValues[key], dirtyFieldsFromValues[key]);\n        }\n      } else {\n        dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n      }\n    }\n  }\n  return dirtyFieldsFromValues;\n}\nvar getDirtyFields = (defaultValues, formValues) => getDirtyFieldsFromDefaultValues(defaultValues, formValues, markFieldsDirty(formValues));\nconst defaultResult = {\n  value: false,\n  isValid: false\n};\nconst validResult = {\n  value: true,\n  isValid: true\n};\nvar getCheckboxValue = options => {\n  if (Array.isArray(options)) {\n    if (options.length > 1) {\n      const values = options.filter(option => option && option.checked && !option.disabled).map(option => option.value);\n      return {\n        value: values,\n        isValid: !!values.length\n      };\n    }\n    return options[0].checked && !options[0].disabled ?\n    // @ts-expect-error expected to work in the browser\n    options[0].attributes && !isUndefined(options[0].attributes.value) ? isUndefined(options[0].value) || options[0].value === '' ? validResult : {\n      value: options[0].value,\n      isValid: true\n    } : validResult : defaultResult;\n  }\n  return defaultResult;\n};\nvar getFieldValueAs = (value, {\n  valueAsNumber,\n  valueAsDate,\n  setValueAs\n}) => isUndefined(value) ? value : valueAsNumber ? value === '' ? NaN : value ? +value : value : valueAsDate && isString(value) ? new Date(value) : setValueAs ? setValueAs(value) : value;\nconst defaultReturn = {\n  isValid: false,\n  value: null\n};\nvar getRadioValue = options => Array.isArray(options) ? options.reduce((previous, option) => option && option.checked && !option.disabled ? {\n  isValid: true,\n  value: option.value\n} : previous, defaultReturn) : defaultReturn;\nfunction getFieldValue(_f) {\n  const ref = _f.ref;\n  if (isFileInput(ref)) {\n    return ref.files;\n  }\n  if (isRadioInput(ref)) {\n    return getRadioValue(_f.refs).value;\n  }\n  if (isMultipleSelect(ref)) {\n    return [...ref.selectedOptions].map(({\n      value\n    }) => value);\n  }\n  if (isCheckBoxInput(ref)) {\n    return getCheckboxValue(_f.refs).value;\n  }\n  return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\nvar getResolverOptions = (fieldsNames, _fields, criteriaMode, shouldUseNativeValidation) => {\n  const fields = {};\n  for (const name of fieldsNames) {\n    const field = get(_fields, name);\n    field && set(fields, name, field._f);\n  }\n  return {\n    criteriaMode,\n    names: [...fieldsNames],\n    fields,\n    shouldUseNativeValidation\n  };\n};\nvar isRegex = value => value instanceof RegExp;\nvar getRuleValue = rule => isUndefined(rule) ? rule : isRegex(rule) ? rule.source : isObject(rule) ? isRegex(rule.value) ? rule.value.source : rule.value : rule;\nvar getValidationModes = mode => ({\n  isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n  isOnBlur: mode === VALIDATION_MODE.onBlur,\n  isOnChange: mode === VALIDATION_MODE.onChange,\n  isOnAll: mode === VALIDATION_MODE.all,\n  isOnTouch: mode === VALIDATION_MODE.onTouched\n});\nconst ASYNC_FUNCTION = 'AsyncFunction';\nvar hasPromiseValidation = fieldReference => !!fieldReference && !!fieldReference.validate && !!(isFunction(fieldReference.validate) && fieldReference.validate.constructor.name === ASYNC_FUNCTION || isObject(fieldReference.validate) && Object.values(fieldReference.validate).find(validateFunction => validateFunction.constructor.name === ASYNC_FUNCTION));\nvar hasValidation = options => options.mount && (options.required || options.min || options.max || options.maxLength || options.minLength || options.pattern || options.validate);\nvar isWatched = (name, _names, isBlurEvent) => !isBlurEvent && (_names.watchAll || _names.watch.has(name) || [..._names.watch].some(watchName => name.startsWith(watchName) && /^\\.\\w+/.test(name.slice(watchName.length))));\nconst iterateFieldsByAction = (fields, action, fieldsNames, abortEarly) => {\n  for (const key of fieldsNames || Object.keys(fields)) {\n    const field = get(fields, key);\n    if (field) {\n      const {\n          _f\n        } = field,\n        currentField = _objectWithoutProperties(field, _excluded3);\n      if (_f) {\n        if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n          return true;\n        } else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n          return true;\n        } else {\n          if (iterateFieldsByAction(currentField, action)) {\n            break;\n          }\n        }\n      } else if (isObject(currentField)) {\n        if (iterateFieldsByAction(currentField, action)) {\n          break;\n        }\n      }\n    }\n  }\n  return;\n};\nfunction schemaErrorLookup(errors, _fields, name) {\n  const error = get(errors, name);\n  if (error || isKey(name)) {\n    return {\n      error,\n      name\n    };\n  }\n  const names = name.split('.');\n  while (names.length) {\n    const fieldName = names.join('.');\n    const field = get(_fields, fieldName);\n    const foundError = get(errors, fieldName);\n    if (field && !Array.isArray(field) && name !== fieldName) {\n      return {\n        name\n      };\n    }\n    if (foundError && foundError.type) {\n      return {\n        name: fieldName,\n        error: foundError\n      };\n    }\n    names.pop();\n  }\n  return {\n    name\n  };\n}\nvar shouldRenderFormState = (formStateData, _proxyFormState, updateFormState, isRoot) => {\n  updateFormState(formStateData);\n  const {\n      name\n    } = formStateData,\n    formState = _objectWithoutProperties(formStateData, _excluded4);\n  return isEmptyObject(formState) || Object.keys(formState).length >= Object.keys(_proxyFormState).length || Object.keys(formState).find(key => _proxyFormState[key] === (!isRoot || VALIDATION_MODE.all));\n};\nvar shouldSubscribeByName = (name, signalName, exact) => !name || !signalName || name === signalName || convertToArrayPayload(name).some(currentName => currentName && (exact ? currentName === signalName : currentName.startsWith(signalName) || signalName.startsWith(currentName)));\nvar skipValidation = (isBlurEvent, isTouched, isSubmitted, reValidateMode, mode) => {\n  if (mode.isOnAll) {\n    return false;\n  } else if (!isSubmitted && mode.isOnTouch) {\n    return !(isTouched || isBlurEvent);\n  } else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n    return !isBlurEvent;\n  } else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n    return isBlurEvent;\n  }\n  return true;\n};\nvar unsetEmptyArray = (ref, name) => !compact(get(ref, name)).length && unset(ref, name);\nvar updateFieldArrayRootError = (errors, error, name) => {\n  const fieldArrayErrors = convertToArrayPayload(get(errors, name));\n  set(fieldArrayErrors, 'root', error[name]);\n  set(errors, name, fieldArrayErrors);\n  return errors;\n};\nvar isMessage = value => isString(value);\nfunction getValidateError(result, ref, type = 'validate') {\n  if (isMessage(result) || Array.isArray(result) && result.every(isMessage) || isBoolean(result) && !result) {\n    return {\n      type,\n      message: isMessage(result) ? result : '',\n      ref\n    };\n  }\n}\nvar getValueAndMessage = validationData => isObject(validationData) && !isRegex(validationData) ? validationData : {\n  value: validationData,\n  message: ''\n};\nvar validateField = async (field, disabledFieldNames, formValues, validateAllFieldCriteria, shouldUseNativeValidation, isFieldArray) => {\n  const {\n    ref,\n    refs,\n    required,\n    maxLength,\n    minLength,\n    min,\n    max,\n    pattern,\n    validate,\n    name,\n    valueAsNumber,\n    mount\n  } = field._f;\n  const inputValue = get(formValues, name);\n  if (!mount || disabledFieldNames.has(name)) {\n    return {};\n  }\n  const inputRef = refs ? refs[0] : ref;\n  const setCustomValidity = message => {\n    if (shouldUseNativeValidation && inputRef.reportValidity) {\n      inputRef.setCustomValidity(isBoolean(message) ? '' : message || '');\n      inputRef.reportValidity();\n    }\n  };\n  const error = {};\n  const isRadio = isRadioInput(ref);\n  const isCheckBox = isCheckBoxInput(ref);\n  const isRadioOrCheckbox = isRadio || isCheckBox;\n  const isEmpty = (valueAsNumber || isFileInput(ref)) && isUndefined(ref.value) && isUndefined(inputValue) || isHTMLElement(ref) && ref.value === '' || inputValue === '' || Array.isArray(inputValue) && !inputValue.length;\n  const appendErrorsCurry = appendErrors.bind(null, name, validateAllFieldCriteria, error);\n  const getMinMaxMessage = (exceedMax, maxLengthMessage, minLengthMessage, maxType = INPUT_VALIDATION_RULES.maxLength, minType = INPUT_VALIDATION_RULES.minLength) => {\n    const message = exceedMax ? maxLengthMessage : minLengthMessage;\n    error[name] = _objectSpread({\n      type: exceedMax ? maxType : minType,\n      message,\n      ref\n    }, appendErrorsCurry(exceedMax ? maxType : minType, message));\n  };\n  if (isFieldArray ? !Array.isArray(inputValue) || !inputValue.length : required && (!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue)) || isBoolean(inputValue) && !inputValue || isCheckBox && !getCheckboxValue(refs).isValid || isRadio && !getRadioValue(refs).isValid)) {\n    const {\n      value,\n      message\n    } = isMessage(required) ? {\n      value: !!required,\n      message: required\n    } : getValueAndMessage(required);\n    if (value) {\n      error[name] = _objectSpread({\n        type: INPUT_VALIDATION_RULES.required,\n        message,\n        ref: inputRef\n      }, appendErrorsCurry(INPUT_VALIDATION_RULES.required, message));\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n  if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n    let exceedMax;\n    let exceedMin;\n    const maxOutput = getValueAndMessage(max);\n    const minOutput = getValueAndMessage(min);\n    if (!isNullOrUndefined(inputValue) && !isNaN(inputValue)) {\n      const valueNumber = ref.valueAsNumber || (inputValue ? +inputValue : inputValue);\n      if (!isNullOrUndefined(maxOutput.value)) {\n        exceedMax = valueNumber > maxOutput.value;\n      }\n      if (!isNullOrUndefined(minOutput.value)) {\n        exceedMin = valueNumber < minOutput.value;\n      }\n    } else {\n      const valueDate = ref.valueAsDate || new Date(inputValue);\n      const convertTimeToDate = time => new Date(new Date().toDateString() + ' ' + time);\n      const isTime = ref.type == 'time';\n      const isWeek = ref.type == 'week';\n      if (isString(maxOutput.value) && inputValue) {\n        exceedMax = isTime ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value) : isWeek ? inputValue > maxOutput.value : valueDate > new Date(maxOutput.value);\n      }\n      if (isString(minOutput.value) && inputValue) {\n        exceedMin = isTime ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value) : isWeek ? inputValue < minOutput.value : valueDate < new Date(minOutput.value);\n      }\n    }\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(!!exceedMax, maxOutput.message, minOutput.message, INPUT_VALIDATION_RULES.max, INPUT_VALIDATION_RULES.min);\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name].message);\n        return error;\n      }\n    }\n  }\n  if ((maxLength || minLength) && !isEmpty && (isString(inputValue) || isFieldArray && Array.isArray(inputValue))) {\n    const maxLengthOutput = getValueAndMessage(maxLength);\n    const minLengthOutput = getValueAndMessage(minLength);\n    const exceedMax = !isNullOrUndefined(maxLengthOutput.value) && inputValue.length > +maxLengthOutput.value;\n    const exceedMin = !isNullOrUndefined(minLengthOutput.value) && inputValue.length < +minLengthOutput.value;\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(exceedMax, maxLengthOutput.message, minLengthOutput.message);\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name].message);\n        return error;\n      }\n    }\n  }\n  if (pattern && !isEmpty && isString(inputValue)) {\n    const {\n      value: patternValue,\n      message\n    } = getValueAndMessage(pattern);\n    if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n      error[name] = _objectSpread({\n        type: INPUT_VALIDATION_RULES.pattern,\n        message,\n        ref\n      }, appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message));\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n  if (validate) {\n    if (isFunction(validate)) {\n      const result = await validate(inputValue, formValues);\n      const validateError = getValidateError(result, inputRef);\n      if (validateError) {\n        error[name] = _objectSpread(_objectSpread({}, validateError), appendErrorsCurry(INPUT_VALIDATION_RULES.validate, validateError.message));\n        if (!validateAllFieldCriteria) {\n          setCustomValidity(validateError.message);\n          return error;\n        }\n      }\n    } else if (isObject(validate)) {\n      let validationResult = {};\n      for (const key in validate) {\n        if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n          break;\n        }\n        const validateError = getValidateError(await validate[key](inputValue, formValues), inputRef, key);\n        if (validateError) {\n          validationResult = _objectSpread(_objectSpread({}, validateError), appendErrorsCurry(key, validateError.message));\n          setCustomValidity(validateError.message);\n          if (validateAllFieldCriteria) {\n            error[name] = validationResult;\n          }\n        }\n      }\n      if (!isEmptyObject(validationResult)) {\n        error[name] = _objectSpread({\n          ref: inputRef\n        }, validationResult);\n        if (!validateAllFieldCriteria) {\n          return error;\n        }\n      }\n    }\n  }\n  setCustomValidity(true);\n  return error;\n};\nconst defaultOptions = {\n  mode: VALIDATION_MODE.onSubmit,\n  reValidateMode: VALIDATION_MODE.onChange,\n  shouldFocusError: true\n};\nfunction createFormControl(props = {}) {\n  let _options = _objectSpread(_objectSpread({}, defaultOptions), props);\n  let _formState = {\n    submitCount: 0,\n    isDirty: false,\n    isReady: false,\n    isLoading: isFunction(_options.defaultValues),\n    isValidating: false,\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    touchedFields: {},\n    dirtyFields: {},\n    validatingFields: {},\n    errors: _options.errors || {},\n    disabled: _options.disabled || false\n  };\n  const _fields = {};\n  let _defaultValues = isObject(_options.defaultValues) || isObject(_options.values) ? cloneObject(_options.defaultValues || _options.values) || {} : {};\n  let _formValues = _options.shouldUnregister ? {} : cloneObject(_defaultValues);\n  let _state = {\n    action: false,\n    mount: false,\n    watch: false\n  };\n  let _names = {\n    mount: new Set(),\n    disabled: new Set(),\n    unMount: new Set(),\n    array: new Set(),\n    watch: new Set()\n  };\n  let delayErrorCallback;\n  let timer = 0;\n  const _proxyFormState = {\n    isDirty: false,\n    dirtyFields: false,\n    validatingFields: false,\n    touchedFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false\n  };\n  let _proxySubscribeFormState = _objectSpread({}, _proxyFormState);\n  const _subjects = {\n    array: createSubject(),\n    state: createSubject()\n  };\n  const shouldDisplayAllAssociatedErrors = _options.criteriaMode === VALIDATION_MODE.all;\n  const debounce = callback => wait => {\n    clearTimeout(timer);\n    timer = setTimeout(callback, wait);\n  };\n  const _setValid = async shouldUpdateValid => {\n    if (!_options.disabled && (_proxyFormState.isValid || _proxySubscribeFormState.isValid || shouldUpdateValid)) {\n      const isValid = _options.resolver ? isEmptyObject((await _runSchema()).errors) : await executeBuiltInValidation(_fields, true);\n      if (isValid !== _formState.isValid) {\n        _subjects.state.next({\n          isValid\n        });\n      }\n    }\n  };\n  const _updateIsValidating = (names, isValidating) => {\n    if (!_options.disabled && (_proxyFormState.isValidating || _proxyFormState.validatingFields || _proxySubscribeFormState.isValidating || _proxySubscribeFormState.validatingFields)) {\n      (names || Array.from(_names.mount)).forEach(name => {\n        if (name) {\n          isValidating ? set(_formState.validatingFields, name, isValidating) : unset(_formState.validatingFields, name);\n        }\n      });\n      _subjects.state.next({\n        validatingFields: _formState.validatingFields,\n        isValidating: !isEmptyObject(_formState.validatingFields)\n      });\n    }\n  };\n  const _setFieldArray = (name, values = [], method, args, shouldSetValues = true, shouldUpdateFieldsAndState = true) => {\n    if (args && method && !_options.disabled) {\n      _state.action = true;\n      if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n        const fieldValues = method(get(_fields, name), args.argA, args.argB);\n        shouldSetValues && set(_fields, name, fieldValues);\n      }\n      if (shouldUpdateFieldsAndState && Array.isArray(get(_formState.errors, name))) {\n        const errors = method(get(_formState.errors, name), args.argA, args.argB);\n        shouldSetValues && set(_formState.errors, name, errors);\n        unsetEmptyArray(_formState.errors, name);\n      }\n      if ((_proxyFormState.touchedFields || _proxySubscribeFormState.touchedFields) && shouldUpdateFieldsAndState && Array.isArray(get(_formState.touchedFields, name))) {\n        const touchedFields = method(get(_formState.touchedFields, name), args.argA, args.argB);\n        shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n      }\n      if (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) {\n        _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n      }\n      _subjects.state.next({\n        name,\n        isDirty: _getDirty(name, values),\n        dirtyFields: _formState.dirtyFields,\n        errors: _formState.errors,\n        isValid: _formState.isValid\n      });\n    } else {\n      set(_formValues, name, values);\n    }\n  };\n  const updateErrors = (name, error) => {\n    set(_formState.errors, name, error);\n    _subjects.state.next({\n      errors: _formState.errors\n    });\n  };\n  const _setErrors = errors => {\n    _formState.errors = errors;\n    _subjects.state.next({\n      errors: _formState.errors,\n      isValid: false\n    });\n  };\n  const updateValidAndValue = (name, shouldSkipSetValueAs, value, ref) => {\n    const field = get(_fields, name);\n    if (field) {\n      const defaultValue = get(_formValues, name, isUndefined(value) ? get(_defaultValues, name) : value);\n      isUndefined(defaultValue) || ref && ref.defaultChecked || shouldSkipSetValueAs ? set(_formValues, name, shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f)) : setFieldValue(name, defaultValue);\n      _state.mount && _setValid();\n    }\n  };\n  const updateTouchAndDirty = (name, fieldValue, isBlurEvent, shouldDirty, shouldRender) => {\n    let shouldUpdateField = false;\n    let isPreviousDirty = false;\n    const output = {\n      name\n    };\n    if (!_options.disabled) {\n      if (!isBlurEvent || shouldDirty) {\n        if (_proxyFormState.isDirty || _proxySubscribeFormState.isDirty) {\n          isPreviousDirty = _formState.isDirty;\n          _formState.isDirty = output.isDirty = _getDirty();\n          shouldUpdateField = isPreviousDirty !== output.isDirty;\n        }\n        const isCurrentFieldPristine = deepEqual(get(_defaultValues, name), fieldValue);\n        isPreviousDirty = !!get(_formState.dirtyFields, name);\n        isCurrentFieldPristine ? unset(_formState.dirtyFields, name) : set(_formState.dirtyFields, name, true);\n        output.dirtyFields = _formState.dirtyFields;\n        shouldUpdateField = shouldUpdateField || (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) && isPreviousDirty !== !isCurrentFieldPristine;\n      }\n      if (isBlurEvent) {\n        const isPreviousFieldTouched = get(_formState.touchedFields, name);\n        if (!isPreviousFieldTouched) {\n          set(_formState.touchedFields, name, isBlurEvent);\n          output.touchedFields = _formState.touchedFields;\n          shouldUpdateField = shouldUpdateField || (_proxyFormState.touchedFields || _proxySubscribeFormState.touchedFields) && isPreviousFieldTouched !== isBlurEvent;\n        }\n      }\n      shouldUpdateField && shouldRender && _subjects.state.next(output);\n    }\n    return shouldUpdateField ? output : {};\n  };\n  const shouldRenderByError = (name, isValid, error, fieldState) => {\n    const previousFieldError = get(_formState.errors, name);\n    const shouldUpdateValid = (_proxyFormState.isValid || _proxySubscribeFormState.isValid) && isBoolean(isValid) && _formState.isValid !== isValid;\n    if (_options.delayError && error) {\n      delayErrorCallback = debounce(() => updateErrors(name, error));\n      delayErrorCallback(_options.delayError);\n    } else {\n      clearTimeout(timer);\n      delayErrorCallback = null;\n      error ? set(_formState.errors, name, error) : unset(_formState.errors, name);\n    }\n    if ((error ? !deepEqual(previousFieldError, error) : previousFieldError) || !isEmptyObject(fieldState) || shouldUpdateValid) {\n      const updatedFormState = _objectSpread(_objectSpread(_objectSpread({}, fieldState), shouldUpdateValid && isBoolean(isValid) ? {\n        isValid\n      } : {}), {}, {\n        errors: _formState.errors,\n        name\n      });\n      _formState = _objectSpread(_objectSpread({}, _formState), updatedFormState);\n      _subjects.state.next(updatedFormState);\n    }\n  };\n  const _runSchema = async name => {\n    _updateIsValidating(name, true);\n    const result = await _options.resolver(_formValues, _options.context, getResolverOptions(name || _names.mount, _fields, _options.criteriaMode, _options.shouldUseNativeValidation));\n    _updateIsValidating(name);\n    return result;\n  };\n  const executeSchemaAndUpdateState = async names => {\n    const {\n      errors\n    } = await _runSchema(names);\n    if (names) {\n      for (const name of names) {\n        const error = get(errors, name);\n        error ? set(_formState.errors, name, error) : unset(_formState.errors, name);\n      }\n    } else {\n      _formState.errors = errors;\n    }\n    return errors;\n  };\n  const executeBuiltInValidation = async (fields, shouldOnlyCheckValid, context = {\n    valid: true\n  }) => {\n    for (const name in fields) {\n      const field = fields[name];\n      if (field) {\n        const {\n            _f\n          } = field,\n          fieldValue = _objectWithoutProperties(field, _excluded5);\n        if (_f) {\n          const isFieldArrayRoot = _names.array.has(_f.name);\n          const isPromiseFunction = field._f && hasPromiseValidation(field._f);\n          if (isPromiseFunction && _proxyFormState.validatingFields) {\n            _updateIsValidating([name], true);\n          }\n          const fieldError = await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation && !shouldOnlyCheckValid, isFieldArrayRoot);\n          if (isPromiseFunction && _proxyFormState.validatingFields) {\n            _updateIsValidating([name]);\n          }\n          if (fieldError[_f.name]) {\n            context.valid = false;\n            if (shouldOnlyCheckValid) {\n              break;\n            }\n          }\n          !shouldOnlyCheckValid && (get(fieldError, _f.name) ? isFieldArrayRoot ? updateFieldArrayRootError(_formState.errors, fieldError, _f.name) : set(_formState.errors, _f.name, fieldError[_f.name]) : unset(_formState.errors, _f.name));\n        }\n        !isEmptyObject(fieldValue) && (await executeBuiltInValidation(fieldValue, shouldOnlyCheckValid, context));\n      }\n    }\n    return context.valid;\n  };\n  const _removeUnmounted = () => {\n    for (const name of _names.unMount) {\n      const field = get(_fields, name);\n      field && (field._f.refs ? field._f.refs.every(ref => !live(ref)) : !live(field._f.ref)) && unregister(name);\n    }\n    _names.unMount = new Set();\n  };\n  const _getDirty = (name, data) => !_options.disabled && (name && data && set(_formValues, name, data), !deepEqual(getValues(), _defaultValues));\n  const _getWatch = (names, defaultValue, isGlobal) => generateWatchOutput(names, _names, _objectSpread({}, _state.mount ? _formValues : isUndefined(defaultValue) ? _defaultValues : isString(names) ? {\n    [names]: defaultValue\n  } : defaultValue), isGlobal, defaultValue);\n  const _getFieldArray = name => compact(get(_state.mount ? _formValues : _defaultValues, name, _options.shouldUnregister ? get(_defaultValues, name, []) : []));\n  const setFieldValue = (name, value, options = {}) => {\n    const field = get(_fields, name);\n    let fieldValue = value;\n    if (field) {\n      const fieldReference = field._f;\n      if (fieldReference) {\n        !fieldReference.disabled && set(_formValues, name, getFieldValueAs(value, fieldReference));\n        fieldValue = isHTMLElement(fieldReference.ref) && isNullOrUndefined(value) ? '' : value;\n        if (isMultipleSelect(fieldReference.ref)) {\n          [...fieldReference.ref.options].forEach(optionRef => optionRef.selected = fieldValue.includes(optionRef.value));\n        } else if (fieldReference.refs) {\n          if (isCheckBoxInput(fieldReference.ref)) {\n            fieldReference.refs.forEach(checkboxRef => {\n              if (!checkboxRef.defaultChecked || !checkboxRef.disabled) {\n                if (Array.isArray(fieldValue)) {\n                  checkboxRef.checked = !!fieldValue.find(data => data === checkboxRef.value);\n                } else {\n                  checkboxRef.checked = fieldValue === checkboxRef.value || !!fieldValue;\n                }\n              }\n            });\n          } else {\n            fieldReference.refs.forEach(radioRef => radioRef.checked = radioRef.value === fieldValue);\n          }\n        } else if (isFileInput(fieldReference.ref)) {\n          fieldReference.ref.value = '';\n        } else {\n          fieldReference.ref.value = fieldValue;\n          if (!fieldReference.ref.type) {\n            _subjects.state.next({\n              name,\n              values: cloneObject(_formValues)\n            });\n          }\n        }\n      }\n    }\n    (options.shouldDirty || options.shouldTouch) && updateTouchAndDirty(name, fieldValue, options.shouldTouch, options.shouldDirty, true);\n    options.shouldValidate && trigger(name);\n  };\n  const setValues = (name, value, options) => {\n    for (const fieldKey in value) {\n      if (!value.hasOwnProperty(fieldKey)) {\n        return;\n      }\n      const fieldValue = value[fieldKey];\n      const fieldName = `${name}.${fieldKey}`;\n      const field = get(_fields, fieldName);\n      (_names.array.has(name) || isObject(fieldValue) || field && !field._f) && !isDateObject(fieldValue) ? setValues(fieldName, fieldValue, options) : setFieldValue(fieldName, fieldValue, options);\n    }\n  };\n  const setValue = (name, value, options = {}) => {\n    const field = get(_fields, name);\n    const isFieldArray = _names.array.has(name);\n    const cloneValue = cloneObject(value);\n    set(_formValues, name, cloneValue);\n    if (isFieldArray) {\n      _subjects.array.next({\n        name,\n        values: cloneObject(_formValues)\n      });\n      if ((_proxyFormState.isDirty || _proxyFormState.dirtyFields || _proxySubscribeFormState.isDirty || _proxySubscribeFormState.dirtyFields) && options.shouldDirty) {\n        _subjects.state.next({\n          name,\n          dirtyFields: getDirtyFields(_defaultValues, _formValues),\n          isDirty: _getDirty(name, cloneValue)\n        });\n      }\n    } else {\n      field && !field._f && !isNullOrUndefined(cloneValue) ? setValues(name, cloneValue, options) : setFieldValue(name, cloneValue, options);\n    }\n    isWatched(name, _names) && _subjects.state.next(_objectSpread({}, _formState));\n    _subjects.state.next({\n      name: _state.mount ? name : undefined,\n      values: cloneObject(_formValues)\n    });\n  };\n  const onChange = async event => {\n    _state.mount = true;\n    const target = event.target;\n    let name = target.name;\n    let isFieldValueUpdated = true;\n    const field = get(_fields, name);\n    const _updateIsFieldValueUpdated = fieldValue => {\n      isFieldValueUpdated = Number.isNaN(fieldValue) || isDateObject(fieldValue) && isNaN(fieldValue.getTime()) || deepEqual(fieldValue, get(_formValues, name, fieldValue));\n    };\n    const validationModeBeforeSubmit = getValidationModes(_options.mode);\n    const validationModeAfterSubmit = getValidationModes(_options.reValidateMode);\n    if (field) {\n      let error;\n      let isValid;\n      const fieldValue = target.type ? getFieldValue(field._f) : getEventValue(event);\n      const isBlurEvent = event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n      const shouldSkipValidation = !hasValidation(field._f) && !_options.resolver && !get(_formState.errors, name) && !field._f.deps || skipValidation(isBlurEvent, get(_formState.touchedFields, name), _formState.isSubmitted, validationModeAfterSubmit, validationModeBeforeSubmit);\n      const watched = isWatched(name, _names, isBlurEvent);\n      set(_formValues, name, fieldValue);\n      if (isBlurEvent) {\n        field._f.onBlur && field._f.onBlur(event);\n        delayErrorCallback && delayErrorCallback(0);\n      } else if (field._f.onChange) {\n        field._f.onChange(event);\n      }\n      const fieldState = updateTouchAndDirty(name, fieldValue, isBlurEvent);\n      const shouldRender = !isEmptyObject(fieldState) || watched;\n      !isBlurEvent && _subjects.state.next({\n        name,\n        type: event.type,\n        values: cloneObject(_formValues)\n      });\n      if (shouldSkipValidation) {\n        if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n          if (_options.mode === 'onBlur') {\n            if (isBlurEvent) {\n              _setValid();\n            }\n          } else if (!isBlurEvent) {\n            _setValid();\n          }\n        }\n        return shouldRender && _subjects.state.next(_objectSpread({\n          name\n        }, watched ? {} : fieldState));\n      }\n      !isBlurEvent && watched && _subjects.state.next(_objectSpread({}, _formState));\n      if (_options.resolver) {\n        const {\n          errors\n        } = await _runSchema([name]);\n        _updateIsFieldValueUpdated(fieldValue);\n        if (isFieldValueUpdated) {\n          const previousErrorLookupResult = schemaErrorLookup(_formState.errors, _fields, name);\n          const errorLookupResult = schemaErrorLookup(errors, _fields, previousErrorLookupResult.name || name);\n          error = errorLookupResult.error;\n          name = errorLookupResult.name;\n          isValid = isEmptyObject(errors);\n        }\n      } else {\n        _updateIsValidating([name], true);\n        error = (await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation))[name];\n        _updateIsValidating([name]);\n        _updateIsFieldValueUpdated(fieldValue);\n        if (isFieldValueUpdated) {\n          if (error) {\n            isValid = false;\n          } else if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n            isValid = await executeBuiltInValidation(_fields, true);\n          }\n        }\n      }\n      if (isFieldValueUpdated) {\n        field._f.deps && trigger(field._f.deps);\n        shouldRenderByError(name, isValid, error, fieldState);\n      }\n    }\n  };\n  const _focusInput = (ref, key) => {\n    if (get(_formState.errors, key) && ref.focus) {\n      ref.focus();\n      return 1;\n    }\n    return;\n  };\n  const trigger = async (name, options = {}) => {\n    let isValid;\n    let validationResult;\n    const fieldNames = convertToArrayPayload(name);\n    if (_options.resolver) {\n      const errors = await executeSchemaAndUpdateState(isUndefined(name) ? name : fieldNames);\n      isValid = isEmptyObject(errors);\n      validationResult = name ? !fieldNames.some(name => get(errors, name)) : isValid;\n    } else if (name) {\n      validationResult = (await Promise.all(fieldNames.map(async fieldName => {\n        const field = get(_fields, fieldName);\n        return await executeBuiltInValidation(field && field._f ? {\n          [fieldName]: field\n        } : field);\n      }))).every(Boolean);\n      !(!validationResult && !_formState.isValid) && _setValid();\n    } else {\n      validationResult = isValid = await executeBuiltInValidation(_fields);\n    }\n    _subjects.state.next(_objectSpread(_objectSpread(_objectSpread({}, !isString(name) || (_proxyFormState.isValid || _proxySubscribeFormState.isValid) && isValid !== _formState.isValid ? {} : {\n      name\n    }), _options.resolver || !name ? {\n      isValid\n    } : {}), {}, {\n      errors: _formState.errors\n    }));\n    options.shouldFocus && !validationResult && iterateFieldsByAction(_fields, _focusInput, name ? fieldNames : _names.mount);\n    return validationResult;\n  };\n  const getValues = fieldNames => {\n    const values = _objectSpread({}, _state.mount ? _formValues : _defaultValues);\n    return isUndefined(fieldNames) ? values : isString(fieldNames) ? get(values, fieldNames) : fieldNames.map(name => get(values, name));\n  };\n  const getFieldState = (name, formState) => ({\n    invalid: !!get((formState || _formState).errors, name),\n    isDirty: !!get((formState || _formState).dirtyFields, name),\n    error: get((formState || _formState).errors, name),\n    isValidating: !!get(_formState.validatingFields, name),\n    isTouched: !!get((formState || _formState).touchedFields, name)\n  });\n  const clearErrors = name => {\n    name && convertToArrayPayload(name).forEach(inputName => unset(_formState.errors, inputName));\n    _subjects.state.next({\n      errors: name ? _formState.errors : {}\n    });\n  };\n  const setError = (name, error, options) => {\n    const ref = (get(_fields, name, {\n      _f: {}\n    })._f || {}).ref;\n    const currentError = get(_formState.errors, name) || {};\n    // Don't override existing error messages elsewhere in the object tree.\n    const {\n        ref: currentRef,\n        message,\n        type\n      } = currentError,\n      restOfErrorTree = _objectWithoutProperties(currentError, _excluded6);\n    set(_formState.errors, name, _objectSpread(_objectSpread(_objectSpread({}, restOfErrorTree), error), {}, {\n      ref\n    }));\n    _subjects.state.next({\n      name,\n      errors: _formState.errors,\n      isValid: false\n    });\n    options && options.shouldFocus && ref && ref.focus && ref.focus();\n  };\n  const watch = (name, defaultValue) => isFunction(name) ? _subjects.state.subscribe({\n    next: payload => name(_getWatch(undefined, defaultValue), payload)\n  }) : _getWatch(name, defaultValue, true);\n  const _subscribe = props => _subjects.state.subscribe({\n    next: formState => {\n      if (shouldSubscribeByName(props.name, formState.name, props.exact) && shouldRenderFormState(formState, props.formState || _proxyFormState, _setFormState, props.reRenderRoot)) {\n        props.callback(_objectSpread(_objectSpread({\n          values: _objectSpread({}, _formValues)\n        }, _formState), formState));\n      }\n    }\n  }).unsubscribe;\n  const subscribe = props => {\n    _state.mount = true;\n    _proxySubscribeFormState = _objectSpread(_objectSpread({}, _proxySubscribeFormState), props.formState);\n    return _subscribe(_objectSpread(_objectSpread({}, props), {}, {\n      formState: _proxySubscribeFormState\n    }));\n  };\n  const unregister = (name, options = {}) => {\n    for (const fieldName of name ? convertToArrayPayload(name) : _names.mount) {\n      _names.mount.delete(fieldName);\n      _names.array.delete(fieldName);\n      if (!options.keepValue) {\n        unset(_fields, fieldName);\n        unset(_formValues, fieldName);\n      }\n      !options.keepError && unset(_formState.errors, fieldName);\n      !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n      !options.keepTouched && unset(_formState.touchedFields, fieldName);\n      !options.keepIsValidating && unset(_formState.validatingFields, fieldName);\n      !_options.shouldUnregister && !options.keepDefaultValue && unset(_defaultValues, fieldName);\n    }\n    _subjects.state.next({\n      values: cloneObject(_formValues)\n    });\n    _subjects.state.next(_objectSpread(_objectSpread({}, _formState), !options.keepDirty ? {} : {\n      isDirty: _getDirty()\n    }));\n    !options.keepIsValid && _setValid();\n  };\n  const _setDisabledField = ({\n    disabled,\n    name\n  }) => {\n    if (isBoolean(disabled) && _state.mount || !!disabled || _names.disabled.has(name)) {\n      disabled ? _names.disabled.add(name) : _names.disabled.delete(name);\n    }\n  };\n  const register = (name, options = {}) => {\n    let field = get(_fields, name);\n    const disabledIsDefined = isBoolean(options.disabled) || isBoolean(_options.disabled);\n    set(_fields, name, _objectSpread(_objectSpread({}, field || {}), {}, {\n      _f: _objectSpread(_objectSpread({}, field && field._f ? field._f : {\n        ref: {\n          name\n        }\n      }), {}, {\n        name,\n        mount: true\n      }, options)\n    }));\n    _names.mount.add(name);\n    if (field) {\n      _setDisabledField({\n        disabled: isBoolean(options.disabled) ? options.disabled : _options.disabled,\n        name\n      });\n    } else {\n      updateValidAndValue(name, true, options.value);\n    }\n    return _objectSpread(_objectSpread(_objectSpread({}, disabledIsDefined ? {\n      disabled: options.disabled || _options.disabled\n    } : {}), _options.progressive ? {\n      required: !!options.required,\n      min: getRuleValue(options.min),\n      max: getRuleValue(options.max),\n      minLength: getRuleValue(options.minLength),\n      maxLength: getRuleValue(options.maxLength),\n      pattern: getRuleValue(options.pattern)\n    } : {}), {}, {\n      name,\n      onChange,\n      onBlur: onChange,\n      ref: ref => {\n        if (ref) {\n          register(name, options);\n          field = get(_fields, name);\n          const fieldRef = isUndefined(ref.value) ? ref.querySelectorAll ? ref.querySelectorAll('input,select,textarea')[0] || ref : ref : ref;\n          const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n          const refs = field._f.refs || [];\n          if (radioOrCheckbox ? refs.find(option => option === fieldRef) : fieldRef === field._f.ref) {\n            return;\n          }\n          set(_fields, name, {\n            _f: _objectSpread(_objectSpread({}, field._f), radioOrCheckbox ? {\n              refs: [...refs.filter(live), fieldRef, ...(Array.isArray(get(_defaultValues, name)) ? [{}] : [])],\n              ref: {\n                type: fieldRef.type,\n                name\n              }\n            } : {\n              ref: fieldRef\n            })\n          });\n          updateValidAndValue(name, false, undefined, fieldRef);\n        } else {\n          field = get(_fields, name, {});\n          if (field._f) {\n            field._f.mount = false;\n          }\n          (_options.shouldUnregister || options.shouldUnregister) && !(isNameInFieldArray(_names.array, name) && _state.action) && _names.unMount.add(name);\n        }\n      }\n    });\n  };\n  const _focusError = () => _options.shouldFocusError && iterateFieldsByAction(_fields, _focusInput, _names.mount);\n  const _disableForm = disabled => {\n    if (isBoolean(disabled)) {\n      _subjects.state.next({\n        disabled\n      });\n      iterateFieldsByAction(_fields, (ref, name) => {\n        const currentField = get(_fields, name);\n        if (currentField) {\n          ref.disabled = currentField._f.disabled || disabled;\n          if (Array.isArray(currentField._f.refs)) {\n            currentField._f.refs.forEach(inputRef => {\n              inputRef.disabled = currentField._f.disabled || disabled;\n            });\n          }\n        }\n      }, 0, false);\n    }\n  };\n  const handleSubmit = (onValid, onInvalid) => async e => {\n    let onValidError = undefined;\n    if (e) {\n      e.preventDefault && e.preventDefault();\n      e.persist && e.persist();\n    }\n    let fieldValues = cloneObject(_formValues);\n    _subjects.state.next({\n      isSubmitting: true\n    });\n    if (_options.resolver) {\n      const {\n        errors,\n        values\n      } = await _runSchema();\n      _formState.errors = errors;\n      fieldValues = values;\n    } else {\n      await executeBuiltInValidation(_fields);\n    }\n    if (_names.disabled.size) {\n      for (const name of _names.disabled) {\n        set(fieldValues, name, undefined);\n      }\n    }\n    unset(_formState.errors, 'root');\n    if (isEmptyObject(_formState.errors)) {\n      _subjects.state.next({\n        errors: {}\n      });\n      try {\n        await onValid(fieldValues, e);\n      } catch (error) {\n        onValidError = error;\n      }\n    } else {\n      if (onInvalid) {\n        await onInvalid(_objectSpread({}, _formState.errors), e);\n      }\n      _focusError();\n      setTimeout(_focusError);\n    }\n    _subjects.state.next({\n      isSubmitted: true,\n      isSubmitting: false,\n      isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n      submitCount: _formState.submitCount + 1,\n      errors: _formState.errors\n    });\n    if (onValidError) {\n      throw onValidError;\n    }\n  };\n  const resetField = (name, options = {}) => {\n    if (get(_fields, name)) {\n      if (isUndefined(options.defaultValue)) {\n        setValue(name, cloneObject(get(_defaultValues, name)));\n      } else {\n        setValue(name, options.defaultValue);\n        set(_defaultValues, name, cloneObject(options.defaultValue));\n      }\n      if (!options.keepTouched) {\n        unset(_formState.touchedFields, name);\n      }\n      if (!options.keepDirty) {\n        unset(_formState.dirtyFields, name);\n        _formState.isDirty = options.defaultValue ? _getDirty(name, cloneObject(get(_defaultValues, name))) : _getDirty();\n      }\n      if (!options.keepError) {\n        unset(_formState.errors, name);\n        _proxyFormState.isValid && _setValid();\n      }\n      _subjects.state.next(_objectSpread({}, _formState));\n    }\n  };\n  const _reset = (formValues, keepStateOptions = {}) => {\n    const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n    const cloneUpdatedValues = cloneObject(updatedValues);\n    const isEmptyResetValues = isEmptyObject(formValues);\n    const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n    if (!keepStateOptions.keepDefaultValues) {\n      _defaultValues = updatedValues;\n    }\n    if (!keepStateOptions.keepValues) {\n      if (keepStateOptions.keepDirtyValues) {\n        const fieldsToCheck = new Set([..._names.mount, ...Object.keys(getDirtyFields(_defaultValues, _formValues))]);\n        for (const fieldName of Array.from(fieldsToCheck)) {\n          get(_formState.dirtyFields, fieldName) ? set(values, fieldName, get(_formValues, fieldName)) : setValue(fieldName, get(values, fieldName));\n        }\n      } else {\n        if (isWeb && isUndefined(formValues)) {\n          for (const name of _names.mount) {\n            const field = get(_fields, name);\n            if (field && field._f) {\n              const fieldReference = Array.isArray(field._f.refs) ? field._f.refs[0] : field._f.ref;\n              if (isHTMLElement(fieldReference)) {\n                const form = fieldReference.closest('form');\n                if (form) {\n                  form.reset();\n                  break;\n                }\n              }\n            }\n          }\n        }\n        for (const fieldName of _names.mount) {\n          setValue(fieldName, get(values, fieldName));\n        }\n      }\n      _formValues = cloneObject(values);\n      _subjects.array.next({\n        values: _objectSpread({}, values)\n      });\n      _subjects.state.next({\n        values: _objectSpread({}, values)\n      });\n    }\n    _names = {\n      mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n      unMount: new Set(),\n      array: new Set(),\n      disabled: new Set(),\n      watch: new Set(),\n      watchAll: false,\n      focus: ''\n    };\n    _state.mount = !_proxyFormState.isValid || !!keepStateOptions.keepIsValid || !!keepStateOptions.keepDirtyValues;\n    _state.watch = !!_options.shouldUnregister;\n    _subjects.state.next({\n      submitCount: keepStateOptions.keepSubmitCount ? _formState.submitCount : 0,\n      isDirty: isEmptyResetValues ? false : keepStateOptions.keepDirty ? _formState.isDirty : !!(keepStateOptions.keepDefaultValues && !deepEqual(formValues, _defaultValues)),\n      isSubmitted: keepStateOptions.keepIsSubmitted ? _formState.isSubmitted : false,\n      dirtyFields: isEmptyResetValues ? {} : keepStateOptions.keepDirtyValues ? keepStateOptions.keepDefaultValues && _formValues ? getDirtyFields(_defaultValues, _formValues) : _formState.dirtyFields : keepStateOptions.keepDefaultValues && formValues ? getDirtyFields(_defaultValues, formValues) : keepStateOptions.keepDirty ? _formState.dirtyFields : {},\n      touchedFields: keepStateOptions.keepTouched ? _formState.touchedFields : {},\n      errors: keepStateOptions.keepErrors ? _formState.errors : {},\n      isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful ? _formState.isSubmitSuccessful : false,\n      isSubmitting: false\n    });\n  };\n  const reset = (formValues, keepStateOptions) => _reset(isFunction(formValues) ? formValues(_formValues) : formValues, keepStateOptions);\n  const setFocus = (name, options = {}) => {\n    const field = get(_fields, name);\n    const fieldReference = field && field._f;\n    if (fieldReference) {\n      const fieldRef = fieldReference.refs ? fieldReference.refs[0] : fieldReference.ref;\n      if (fieldRef.focus) {\n        fieldRef.focus();\n        options.shouldSelect && isFunction(fieldRef.select) && fieldRef.select();\n      }\n    }\n  };\n  const _setFormState = updatedFormState => {\n    _formState = _objectSpread(_objectSpread({}, _formState), updatedFormState);\n  };\n  const _resetDefaultValues = () => isFunction(_options.defaultValues) && _options.defaultValues().then(values => {\n    reset(values, _options.resetOptions);\n    _subjects.state.next({\n      isLoading: false\n    });\n  });\n  const methods = {\n    control: {\n      register,\n      unregister,\n      getFieldState,\n      handleSubmit,\n      setError,\n      _subscribe,\n      _runSchema,\n      _getWatch,\n      _getDirty,\n      _setValid,\n      _setFieldArray,\n      _setDisabledField,\n      _setErrors,\n      _getFieldArray,\n      _reset,\n      _resetDefaultValues,\n      _removeUnmounted,\n      _disableForm,\n      _subjects,\n      _proxyFormState,\n      get _fields() {\n        return _fields;\n      },\n      get _formValues() {\n        return _formValues;\n      },\n      get _state() {\n        return _state;\n      },\n      set _state(value) {\n        _state = value;\n      },\n      get _defaultValues() {\n        return _defaultValues;\n      },\n      get _names() {\n        return _names;\n      },\n      set _names(value) {\n        _names = value;\n      },\n      get _formState() {\n        return _formState;\n      },\n      get _options() {\n        return _options;\n      },\n      set _options(value) {\n        _options = _objectSpread(_objectSpread({}, _options), value);\n      }\n    },\n    subscribe,\n    trigger,\n    register,\n    handleSubmit,\n    watch,\n    setValue,\n    getValues,\n    reset,\n    resetField,\n    clearErrors,\n    unregister,\n    setError,\n    setFocus,\n    getFieldState\n  };\n  return _objectSpread(_objectSpread({}, methods), {}, {\n    formControl: methods\n  });\n}\nvar generateId = () => {\n  const d = typeof performance === 'undefined' ? Date.now() : performance.now() * 1000;\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {\n    const r = (Math.random() * 16 + d) % 16 | 0;\n    return (c == 'x' ? r : r & 0x3 | 0x8).toString(16);\n  });\n};\nvar getFocusFieldName = (name, index, options = {}) => options.shouldFocus || isUndefined(options.shouldFocus) ? options.focusName || `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.` : '';\nvar appendAt = (data, value) => [...data, ...convertToArrayPayload(value)];\nvar fillEmptyArray = value => Array.isArray(value) ? value.map(() => undefined) : undefined;\nfunction insert(data, index, value) {\n  return [...data.slice(0, index), ...convertToArrayPayload(value), ...data.slice(index)];\n}\nvar moveArrayAt = (data, from, to) => {\n  if (!Array.isArray(data)) {\n    return [];\n  }\n  if (isUndefined(data[to])) {\n    data[to] = undefined;\n  }\n  data.splice(to, 0, data.splice(from, 1)[0]);\n  return data;\n};\nvar prependAt = (data, value) => [...convertToArrayPayload(value), ...convertToArrayPayload(data)];\nfunction removeAtIndexes(data, indexes) {\n  let i = 0;\n  const temp = [...data];\n  for (const index of indexes) {\n    temp.splice(index - i, 1);\n    i++;\n  }\n  return compact(temp).length ? temp : [];\n}\nvar removeArrayAt = (data, index) => isUndefined(index) ? [] : removeAtIndexes(data, convertToArrayPayload(index).sort((a, b) => a - b));\nvar swapArrayAt = (data, indexA, indexB) => {\n  [data[indexA], data[indexB]] = [data[indexB], data[indexA]];\n};\nvar updateAt = (fieldValues, index, value) => {\n  fieldValues[index] = value;\n  return fieldValues;\n};\n\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useFieldArray(props) {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    name,\n    keyName = 'id',\n    shouldUnregister,\n    rules\n  } = props;\n  const [fields, setFields] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._getFieldArray(name));\n  const ids = react__WEBPACK_IMPORTED_MODULE_0__.useRef(control._getFieldArray(name).map(generateId));\n  const _fieldIds = react__WEBPACK_IMPORTED_MODULE_0__.useRef(fields);\n  const _name = react__WEBPACK_IMPORTED_MODULE_0__.useRef(name);\n  const _actioned = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n  _name.current = name;\n  _fieldIds.current = fields;\n  control._names.array.add(name);\n  rules && control.register(name, rules);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => control._subjects.array.subscribe({\n    next: ({\n      values,\n      name: fieldArrayName\n    }) => {\n      if (fieldArrayName === _name.current || !fieldArrayName) {\n        const fieldValues = get(values, _name.current);\n        if (Array.isArray(fieldValues)) {\n          setFields(fieldValues);\n          ids.current = fieldValues.map(generateId);\n        }\n      }\n    }\n  }).unsubscribe, [control]);\n  const updateValues = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(updatedFieldArrayValues => {\n    _actioned.current = true;\n    control._setFieldArray(name, updatedFieldArrayValues);\n  }, [control, name]);\n  const append = (value, options) => {\n    const appendValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = appendAt(control._getFieldArray(name), appendValue);\n    control._names.focus = getFocusFieldName(name, updatedFieldArrayValues.length - 1, options);\n    ids.current = appendAt(ids.current, appendValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, appendAt, {\n      argA: fillEmptyArray(value)\n    });\n  };\n  const prepend = (value, options) => {\n    const prependValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = prependAt(control._getFieldArray(name), prependValue);\n    control._names.focus = getFocusFieldName(name, 0, options);\n    ids.current = prependAt(ids.current, prependValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, prependAt, {\n      argA: fillEmptyArray(value)\n    });\n  };\n  const remove = index => {\n    const updatedFieldArrayValues = removeArrayAt(control._getFieldArray(name), index);\n    ids.current = removeArrayAt(ids.current, index);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    !Array.isArray(get(control._fields, name)) && set(control._fields, name, undefined);\n    control._setFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n      argA: index\n    });\n  };\n  const insert$1 = (index, value, options) => {\n    const insertValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = insert(control._getFieldArray(name), index, insertValue);\n    control._names.focus = getFocusFieldName(name, index, options);\n    ids.current = insert(ids.current, index, insertValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, insert, {\n      argA: index,\n      argB: fillEmptyArray(value)\n    });\n  };\n  const swap = (indexA, indexB) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n    swapArrayAt(ids.current, indexA, indexB);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, swapArrayAt, {\n      argA: indexA,\n      argB: indexB\n    }, false);\n  };\n  const move = (from, to) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    moveArrayAt(updatedFieldArrayValues, from, to);\n    moveArrayAt(ids.current, from, to);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, moveArrayAt, {\n      argA: from,\n      argB: to\n    }, false);\n  };\n  const update = (index, value) => {\n    const updateValue = cloneObject(value);\n    const updatedFieldArrayValues = updateAt(control._getFieldArray(name), index, updateValue);\n    ids.current = [...updatedFieldArrayValues].map((item, i) => !item || i === index ? generateId() : ids.current[i]);\n    updateValues(updatedFieldArrayValues);\n    setFields([...updatedFieldArrayValues]);\n    control._setFieldArray(name, updatedFieldArrayValues, updateAt, {\n      argA: index,\n      argB: updateValue\n    }, true, false);\n  };\n  const replace = value => {\n    const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value));\n    ids.current = updatedFieldArrayValues.map(generateId);\n    updateValues([...updatedFieldArrayValues]);\n    setFields([...updatedFieldArrayValues]);\n    control._setFieldArray(name, [...updatedFieldArrayValues], data => data, {}, true, false);\n  };\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    control._state.action = false;\n    isWatched(name, control._names) && control._subjects.state.next(_objectSpread({}, control._formState));\n    if (_actioned.current && (!getValidationModes(control._options.mode).isOnSubmit || control._formState.isSubmitted) && !getValidationModes(control._options.reValidateMode).isOnSubmit) {\n      if (control._options.resolver) {\n        control._runSchema([name]).then(result => {\n          const error = get(result.errors, name);\n          const existingError = get(control._formState.errors, name);\n          if (existingError ? !error && existingError.type || error && (existingError.type !== error.type || existingError.message !== error.message) : error && error.type) {\n            error ? set(control._formState.errors, name, error) : unset(control._formState.errors, name);\n            control._subjects.state.next({\n              errors: control._formState.errors\n            });\n          }\n        });\n      } else {\n        const field = get(control._fields, name);\n        if (field && field._f && !(getValidationModes(control._options.reValidateMode).isOnSubmit && getValidationModes(control._options.mode).isOnSubmit)) {\n          validateField(field, control._names.disabled, control._formValues, control._options.criteriaMode === VALIDATION_MODE.all, control._options.shouldUseNativeValidation, true).then(error => !isEmptyObject(error) && control._subjects.state.next({\n            errors: updateFieldArrayRootError(control._formState.errors, error, name)\n          }));\n        }\n      }\n    }\n    control._subjects.state.next({\n      name,\n      values: cloneObject(control._formValues)\n    });\n    control._names.focus && iterateFieldsByAction(control._fields, (ref, key) => {\n      if (control._names.focus && key.startsWith(control._names.focus) && ref.focus) {\n        ref.focus();\n        return 1;\n      }\n      return;\n    });\n    control._names.focus = '';\n    control._setValid();\n    _actioned.current = false;\n  }, [fields, name, control]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    !get(control._formValues, name) && control._setFieldArray(name);\n    return () => {\n      const updateMounted = (name, value) => {\n        const field = get(control._fields, name);\n        if (field && field._f) {\n          field._f.mount = value;\n        }\n      };\n      control._options.shouldUnregister || shouldUnregister ? control.unregister(name) : updateMounted(name, false);\n    };\n  }, [name, control, keyName, shouldUnregister]);\n  return {\n    swap: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(swap, [updateValues, name, control]),\n    move: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(move, [updateValues, name, control]),\n    prepend: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(prepend, [updateValues, name, control]),\n    append: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(append, [updateValues, name, control]),\n    remove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(remove, [updateValues, name, control]),\n    insert: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(insert$1, [updateValues, name, control]),\n    update: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(update, [updateValues, name, control]),\n    replace: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(replace, [updateValues, name, control]),\n    fields: react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => fields.map((field, index) => _objectSpread(_objectSpread({}, field), {}, {\n      [keyName]: ids.current[index] || generateId()\n    })), [fields, keyName])\n  };\n}\n\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useForm(props = {}) {\n  const _formControl = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n  const _values = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n  const [formState, updateFormState] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n    isDirty: false,\n    isValidating: false,\n    isLoading: isFunction(props.defaultValues),\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    submitCount: 0,\n    dirtyFields: {},\n    touchedFields: {},\n    validatingFields: {},\n    errors: props.errors || {},\n    disabled: props.disabled || false,\n    isReady: false,\n    defaultValues: isFunction(props.defaultValues) ? undefined : props.defaultValues\n  });\n  if (!_formControl.current) {\n    _formControl.current = _objectSpread(_objectSpread({}, props.formControl ? props.formControl : createFormControl(props)), {}, {\n      formState\n    });\n    if (props.formControl && props.defaultValues && !isFunction(props.defaultValues)) {\n      props.formControl.reset(props.defaultValues, props.resetOptions);\n    }\n  }\n  const control = _formControl.current.control;\n  control._options = props;\n  useIsomorphicLayoutEffect(() => {\n    const sub = control._subscribe({\n      formState: control._proxyFormState,\n      callback: () => updateFormState(_objectSpread({}, control._formState)),\n      reRenderRoot: true\n    });\n    updateFormState(data => _objectSpread(_objectSpread({}, data), {}, {\n      isReady: true\n    }));\n    control._formState.isReady = true;\n    return sub;\n  }, [control]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => control._disableForm(props.disabled), [control, props.disabled]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (props.mode) {\n      control._options.mode = props.mode;\n    }\n    if (props.reValidateMode) {\n      control._options.reValidateMode = props.reValidateMode;\n    }\n    if (props.errors && !isEmptyObject(props.errors)) {\n      control._setErrors(props.errors);\n    }\n  }, [control, props.errors, props.mode, props.reValidateMode]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    props.shouldUnregister && control._subjects.state.next({\n      values: control._getWatch()\n    });\n  }, [control, props.shouldUnregister]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (control._proxyFormState.isDirty) {\n      const isDirty = control._getDirty();\n      if (isDirty !== formState.isDirty) {\n        control._subjects.state.next({\n          isDirty\n        });\n      }\n    }\n  }, [control, formState.isDirty]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (props.values && !deepEqual(props.values, _values.current)) {\n      control._reset(props.values, control._options.resetOptions);\n      _values.current = props.values;\n      updateFormState(state => _objectSpread({}, state));\n    } else {\n      control._resetDefaultValues();\n    }\n  }, [control, props.values]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (!control._state.mount) {\n      control._setValid();\n      control._state.mount = true;\n    }\n    if (control._state.watch) {\n      control._state.watch = false;\n      control._subjects.state.next(_objectSpread({}, control._formState));\n    }\n    control._removeUnmounted();\n  });\n  _formControl.current.formState = getProxyFormState(formState, control);\n  return _formControl.current;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\n");

/***/ })

};
;