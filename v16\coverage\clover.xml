<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1750758233485" clover="3.2.0">
  <project timestamp="1750758233485" name="All files">
    <metrics statements="6671" coveredstatements="303" conditionals="5226" coveredconditionals="184" methods="929" coveredmethods="54" elements="12826" coveredelements="541" complexity="0" loc="6671" ncloc="6671" packages="54" files="108" classes="108"/>
    <package name="src">
      <metrics statements="142" coveredstatements="0" conditionals="101" coveredconditionals="0" methods="22" coveredmethods="0"/>
      <file name="middleware.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\middleware.ts">
        <metrics statements="142" coveredstatements="0" conditionals="101" coveredconditionals="0" methods="22" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="80" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="96" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="98" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="106" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="115" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="119" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="125" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="126" count="0" type="stmt"/>
        <line num="129" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="133" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="139" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="154" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="157" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="170" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="173" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="182" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="183" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="184" count="0" type="stmt"/>
        <line num="192" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="201" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="202" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="211" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="221" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="230" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="258" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="260" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="300" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="301" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="305" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
        <line num="310" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="312" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="313" count="0" type="stmt"/>
        <line num="319" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="320" count="0" type="stmt"/>
        <line num="321" count="0" type="stmt"/>
        <line num="322" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="324" count="0" type="stmt"/>
        <line num="325" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="326" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="330" count="0" type="stmt"/>
        <line num="337" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="338" count="0" type="stmt"/>
        <line num="343" count="0" type="stmt"/>
        <line num="347" count="0" type="stmt"/>
        <line num="348" count="0" type="stmt"/>
        <line num="355" count="0" type="stmt"/>
        <line num="358" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="359" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="360" count="0" type="stmt"/>
        <line num="364" count="0" type="stmt"/>
        <line num="365" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="366" count="0" type="stmt"/>
        <line num="373" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="374" count="0" type="stmt"/>
        <line num="380" count="0" type="stmt"/>
        <line num="384" count="0" type="stmt"/>
        <line num="385" count="0" type="stmt"/>
        <line num="394" count="0" type="stmt"/>
        <line num="395" count="0" type="stmt"/>
        <line num="396" count="0" type="stmt"/>
        <line num="397" count="0" type="stmt"/>
        <line num="400" count="0" type="stmt"/>
        <line num="401" count="0" type="stmt"/>
        <line num="402" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="403" count="0" type="stmt"/>
        <line num="405" count="0" type="stmt"/>
        <line num="408" count="0" type="stmt"/>
        <line num="409" count="0" type="stmt"/>
        <line num="410" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="411" count="0" type="stmt"/>
        <line num="412" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="413" count="0" type="stmt"/>
        <line num="415" count="0" type="stmt"/>
        <line num="420" count="0" type="stmt"/>
        <line num="421" count="0" type="stmt"/>
        <line num="422" count="0" type="stmt"/>
        <line num="423" count="0" type="stmt"/>
        <line num="425" count="0" type="stmt"/>
        <line num="426" count="0" type="stmt"/>
        <line num="429" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.app.api.admin.cleanup-expired-free">
      <metrics statements="56" coveredstatements="0" conditionals="77" coveredconditionals="0" methods="10" coveredmethods="0"/>
      <file name="route.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\app\api\admin\cleanup-expired-free\route.ts">
        <metrics statements="56" coveredstatements="0" conditionals="77" coveredconditionals="0" methods="10" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="27" count="0" type="stmt"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="47" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="61" count="0" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="85" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="156" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="171" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="184" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.app.api.admin.email-failures">
      <metrics statements="47" coveredstatements="0" conditionals="40" coveredconditionals="0" methods="8" coveredmethods="0"/>
      <file name="route.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\app\api\admin\email-failures\route.ts">
        <metrics statements="47" coveredstatements="0" conditionals="40" coveredconditionals="0" methods="8" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="24" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="37" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="77" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="86" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="87" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="117" count="0" type="stmt"/>
        <line num="119" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="120" count="0" type="stmt"/>
        <line num="122" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="123" count="0" type="stmt"/>
        <line num="125" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="126" count="0" type="stmt"/>
        <line num="128" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="129" count="0" type="stmt"/>
        <line num="131" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="132" count="0" type="stmt"/>
        <line num="134" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="135" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.app.api.admin.notification-stats">
      <metrics statements="25" coveredstatements="0" conditionals="20" coveredconditionals="0" methods="5" coveredmethods="0"/>
      <file name="route.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\app\api\admin\notification-stats\route.ts">
        <metrics statements="25" coveredstatements="0" conditionals="20" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="23" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="40" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="41" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="42" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="47" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.app.api.admin.process-expired-grace-periods">
      <metrics statements="49" coveredstatements="0" conditionals="56" coveredconditionals="0" methods="12" coveredmethods="0"/>
      <file name="route.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\app\api\admin\process-expired-grace-periods\route.ts">
        <metrics statements="49" coveredstatements="0" conditionals="56" coveredconditionals="0" methods="12" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="20" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="31" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="39" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="83" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="96" count="0" type="stmt"/>
        <line num="99" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="101" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="103" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="105" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="110" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="114" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.app.api.admin.reactivate-user">
      <metrics statements="58" coveredstatements="0" conditionals="100" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="route.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\app\api\admin\reactivate-user\route.ts">
        <metrics statements="58" coveredstatements="0" conditionals="100" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="12" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="26" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="47" count="0" type="stmt"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="19"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="59" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="67" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="79" count="0" type="stmt"/>
        <line num="82" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="85" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="86" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="125" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="140" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="148" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.app.api.admin.send-grace-period-reminders">
      <metrics statements="74" coveredstatements="0" conditionals="74" coveredconditionals="0" methods="10" coveredmethods="0"/>
      <file name="route.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\app\api\admin\send-grace-period-reminders\route.ts">
        <metrics statements="74" coveredstatements="0" conditionals="74" coveredconditionals="0" methods="10" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="20" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="31" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="39" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="55" count="0" type="stmt"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="72" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="88" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="90" count="0" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="107" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="150" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="161" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="162" count="0" type="stmt"/>
        <line num="165" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="168" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="175" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.app.api.ai">
      <metrics statements="68" coveredstatements="0" conditionals="64" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="route.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\app\api\ai\route.ts">
        <metrics statements="68" coveredstatements="0" conditionals="64" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="16" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="22" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="38" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="52" count="0" type="stmt"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="64" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="79" count="0" type="cond" truecount="0" falsecount="7"/>
        <line num="82" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="84" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="93" count="0" type="stmt"/>
        <line num="96" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="97" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="98" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="106" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="107" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="112" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="126" count="0" type="stmt"/>
        <line num="133" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="134" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="143" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="147" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="148" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="156" count="0" type="stmt"/>
        <line num="163" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="164" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="165" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.app.api.auth.free-account-status">
      <metrics statements="53" coveredstatements="0" conditionals="48" coveredconditionals="0" methods="5" coveredmethods="0"/>
      <file name="route.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\app\api\auth\free-account-status\route.ts">
        <metrics statements="53" coveredstatements="0" conditionals="48" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="23" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="69" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="76" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="83" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="93" count="0" type="stmt"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="101" count="0" type="stmt"/>
        <line num="106" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="107" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="118" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="134" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="135" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.app.api.auth.generate-password-reset">
      <metrics statements="11" coveredstatements="0" conditionals="8" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="route.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\app\api\auth\generate-password-reset\route.ts">
        <metrics statements="11" coveredstatements="0" conditionals="8" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="10" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="24" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.app.api.auth.pre-register-paid">
      <metrics statements="42" coveredstatements="0" conditionals="33" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="route.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\app\api\auth\pre-register-paid\route.ts">
        <metrics statements="42" coveredstatements="0" conditionals="33" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="17" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="70" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="72" count="0" type="stmt"/>
        <line num="74" count="0" type="cond" truecount="0" falsecount="12"/>
        <line num="75" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="88" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.app.api.auth.register-free">
      <metrics statements="30" coveredstatements="0" conditionals="31" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="route.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\app\api\auth\register-free\route.ts">
        <metrics statements="30" coveredstatements="0" conditionals="31" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="13" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="17" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="18" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="28" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="49" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="62" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="93" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.app.api.auth.resend-confirmation">
      <metrics statements="22" coveredstatements="0" conditionals="13" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="route.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\app\api\auth\resend-confirmation\route.ts">
        <metrics statements="22" coveredstatements="0" conditionals="13" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="10" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="20" count="0" type="stmt"/>
        <line num="27" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="28" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="37" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.app.api.document.upload">
      <metrics statements="85" coveredstatements="0" conditionals="52" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="route.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\app\api\document\upload\route.ts">
        <metrics statements="85" coveredstatements="0" conditionals="52" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="11" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="45" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="57" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="68" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="71" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="72" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="82" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="100" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="130" count="0" type="stmt"/>
        <line num="138" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="149" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="150" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="166" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="168" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="193" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.app.api.health">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="route.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\app\api\health\route.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.app.api.notify-signup">
      <metrics statements="22" coveredstatements="0" conditionals="27" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="route.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\app\api\notify-signup\route.ts">
        <metrics statements="22" coveredstatements="0" conditionals="27" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="10" count="0" type="stmt"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="13" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="14" count="0" type="stmt"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="22" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="38" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.app.api.payment.status">
      <metrics statements="32" coveredstatements="0" conditionals="79" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="route.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\app\api\payment\status\route.ts">
        <metrics statements="32" coveredstatements="0" conditionals="79" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="12" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="23" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="12"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="cond" truecount="0" falsecount="8"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="44" count="0" type="stmt"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="13"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.app.api.stripe.create-checkout-session">
      <metrics statements="45" coveredstatements="0" conditionals="38" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="route.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\app\api\stripe\create-checkout-session\route.ts">
        <metrics statements="45" coveredstatements="0" conditionals="38" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="39" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="65" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="67" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="101" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="102" count="0" type="stmt"/>
        <line num="114" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.app.api.stripe.webhook">
      <metrics statements="44" coveredstatements="0" conditionals="21" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="route.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\app\api\stripe\webhook\route.ts">
        <metrics statements="44" coveredstatements="0" conditionals="21" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="7"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="88" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.app.api.tokens.purchase">
      <metrics statements="26" coveredstatements="0" conditionals="22" coveredconditionals="0" methods="3" coveredmethods="0"/>
      <file name="route.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\app\api\tokens\purchase\route.ts">
        <metrics statements="26" coveredstatements="0" conditionals="22" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="8" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="9" count="0" type="stmt"/>
        <line num="16" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="17" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="34" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="43" count="0" type="stmt"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="50" count="0" type="stmt"/>
        <line num="56" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="57" count="0" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.app.api.user.cancel-subscription">
      <metrics statements="25" coveredstatements="0" conditionals="24" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="route.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\app\api\user\cancel-subscription\route.ts">
        <metrics statements="25" coveredstatements="0" conditionals="24" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="12" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="22" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="31" count="0" type="stmt"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="39" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="59" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="93" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.app.api.user.notifications">
      <metrics statements="14" coveredstatements="0" conditionals="10" coveredconditionals="0" methods="4" coveredmethods="0"/>
      <file name="route.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\app\api\user\notifications\route.ts">
        <metrics statements="14" coveredstatements="0" conditionals="10" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="19" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="30" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.app.api.user.plan">
      <metrics statements="12" coveredstatements="0" conditionals="12" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="route.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\app\api\user\plan\route.ts">
        <metrics statements="12" coveredstatements="0" conditionals="12" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="9" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="18" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.app.api.user.profile">
      <metrics statements="40" coveredstatements="0" conditionals="56" coveredconditionals="0" methods="6" coveredmethods="0"/>
      <file name="route.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\app\api\user\profile\route.ts">
        <metrics statements="40" coveredstatements="0" conditionals="56" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="21" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="35" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="44" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="102" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="111" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="112" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="118" count="0" type="stmt"/>
        <line num="122" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="123" count="0" type="stmt"/>
        <line num="130" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.app.api.user.reactivate">
      <metrics statements="20" coveredstatements="0" conditionals="18" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="route.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\app\api\user\reactivate\route.ts">
        <metrics statements="20" coveredstatements="0" conditionals="18" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="8" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="18" count="0" type="stmt"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="28" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.app.api.user.status">
      <metrics statements="15" coveredstatements="0" conditionals="10" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="route.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\app\api\user\status\route.ts">
        <metrics statements="15" coveredstatements="0" conditionals="10" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="7" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="18" count="0" type="stmt"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="29" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.app.api.user.validate-access">
      <metrics statements="35" coveredstatements="0" conditionals="30" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="route.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\app\api\user\validate-access\route.ts">
        <metrics statements="35" coveredstatements="0" conditionals="30" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="12" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="21" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="30" count="0" type="stmt"/>
        <line num="39" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="42" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="71" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="80" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="89" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.config">
      <metrics statements="13" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="openai.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\config\openai.ts">
        <metrics statements="6" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="2" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="63" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
      </file>
      <file name="prompts.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\config\prompts.ts">
        <metrics statements="7" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="12" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
        <line num="619" count="0" type="stmt"/>
        <line num="674" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.features.auth.hooks">
      <metrics statements="43" coveredstatements="0" conditionals="21" coveredconditionals="0" methods="14" coveredmethods="0"/>
      <file name="useInactivityTimer.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\features\auth\hooks\useInactivityTimer.ts">
        <metrics statements="43" coveredstatements="0" conditionals="21" coveredconditionals="0" methods="14" coveredmethods="0"/>
        <line num="5" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="15" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="16" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="24" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="26" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="101" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.features.auth.services">
      <metrics statements="72" coveredstatements="4" conditionals="47" coveredconditionals="0" methods="15" coveredmethods="1"/>
      <file name="authService.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\features\auth\services\authService.ts">
        <metrics statements="72" coveredstatements="4" conditionals="47" coveredconditionals="0" methods="15" coveredmethods="1"/>
        <line num="2" count="2" type="stmt"/>
        <line num="6" count="10" type="stmt"/>
        <line num="11" count="2" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="28" count="2" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="32" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="33" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="47" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="55" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="68" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="71" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="90" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="91" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="105" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="113" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="114" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="133" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="156" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="157" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="183" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="184" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.features.conversations.services">
      <metrics statements="135" coveredstatements="0" conditionals="93" coveredconditionals="0" methods="11" coveredmethods="0"/>
      <file name="conversacionesService.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\features\conversations\services\conversacionesService.ts">
        <metrics statements="118" coveredstatements="0" conditionals="79" coveredconditionals="0" methods="10" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="16" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="17" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="26" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="31" count="0" type="cond" truecount="0" falsecount="10"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="50" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="118" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="141" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="148" count="0" type="stmt"/>
        <line num="150" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="158" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="179" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="187" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="188" count="0" type="stmt"/>
        <line num="191" count="0" type="cond" truecount="0" falsecount="10"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="203" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="207" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="212" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="215" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="216" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="221" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="229" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="230" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="233" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="234" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
      </file>
      <file name="questionService.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\features\conversations\services\questionService.ts">
        <metrics statements="17" coveredstatements="0" conditionals="14" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="11" count="0" type="stmt"/>
        <line num="14" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="15" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="20" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="39" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.features.dashboard.services">
      <metrics statements="175" coveredstatements="0" conditionals="113" coveredconditionals="0" methods="30" coveredmethods="0"/>
      <file name="dashboardService.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\features\dashboard\services\dashboardService.ts">
        <metrics statements="56" coveredstatements="0" conditionals="33" coveredconditionals="0" methods="8" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="12" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="89" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="94" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="102" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="106" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="107" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="120" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="123" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
      </file>
      <file name="estadisticasService.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\features\dashboard\services\estadisticasService.ts">
        <metrics statements="119" coveredstatements="0" conditionals="80" coveredconditionals="0" methods="22" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="9" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="71" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="83" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="99" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="126" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="127" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="158" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="182" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="199" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="200" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="203" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="208" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="211" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="212" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="229" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="230" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="235" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="236" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="252" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="253" count="0" type="stmt"/>
        <line num="254" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="255" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="256" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="259" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="275" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="277" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="278" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="279" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="280" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="281" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="286" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="292" count="0" type="stmt"/>
        <line num="300" count="0" type="stmt"/>
        <line num="303" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="304" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="305" count="0" type="stmt"/>
        <line num="308" count="0" type="stmt"/>
        <line num="310" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.features.documents.services">
      <metrics statements="56" coveredstatements="0" conditionals="30" coveredconditionals="0" methods="4" coveredmethods="0"/>
      <file name="documentosService.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\features\documents\services\documentosService.ts">
        <metrics statements="56" coveredstatements="0" conditionals="30" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="16" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="71" count="0" type="cond" truecount="0" falsecount="10"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="93" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.features.flashcards.components">
      <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="types.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\features\flashcards\components\types.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.features.flashcards.services">
      <metrics statements="282" coveredstatements="0" conditionals="203" coveredconditionals="0" methods="36" coveredmethods="0"/>
      <file name="flashcardGenerator.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\features\flashcards\services\flashcardGenerator.ts">
        <metrics statements="22" coveredstatements="0" conditionals="11" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="12" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="19" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="37" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="43" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
      </file>
      <file name="flashcardsService.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\features\flashcards\services\flashcardsService.ts">
        <metrics statements="260" coveredstatements="0" conditionals="192" coveredconditionals="0" methods="35" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="10"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="41" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="56" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="79" count="0" type="cond" truecount="0" falsecount="8"/>
        <line num="80" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="132" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="136" count="0" type="cond" truecount="0" falsecount="10"/>
        <line num="141" count="0" type="stmt"/>
        <line num="144" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="148" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="151" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="160" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="165" count="0" type="stmt"/>
        <line num="166" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="180" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="186" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="189" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="190" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="199" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="201" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="233" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="238" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="239" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="243" count="0" type="stmt"/>
        <line num="244" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="245" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="252" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="253" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="275" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="289" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="290" count="0" type="stmt"/>
        <line num="291" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="295" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="296" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="297" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="307" count="0" type="stmt"/>
        <line num="308" count="0" type="stmt"/>
        <line num="310" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="317" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="320" count="0" type="stmt"/>
        <line num="321" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="322" count="0" type="stmt"/>
        <line num="324" count="0" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="329" count="0" type="stmt"/>
        <line num="336" count="0" type="stmt"/>
        <line num="338" count="0" type="stmt"/>
        <line num="339" count="0" type="stmt"/>
        <line num="340" count="0" type="stmt"/>
        <line num="341" count="0" type="stmt"/>
        <line num="342" count="0" type="stmt"/>
        <line num="344" count="0" type="stmt"/>
        <line num="345" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="346" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="347" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="348" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="349" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="350" count="0" type="stmt"/>
        <line num="353" count="0" type="stmt"/>
        <line num="354" count="0" type="stmt"/>
        <line num="355" count="0" type="stmt"/>
        <line num="356" count="0" type="stmt"/>
        <line num="358" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="359" count="0" type="stmt"/>
        <line num="360" count="0" type="stmt"/>
        <line num="361" count="0" type="stmt"/>
        <line num="362" count="0" type="stmt"/>
        <line num="364" count="0" type="stmt"/>
        <line num="365" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="366" count="0" type="stmt"/>
        <line num="367" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="368" count="0" type="stmt"/>
        <line num="370" count="0" type="stmt"/>
        <line num="372" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="373" count="0" type="stmt"/>
        <line num="374" count="0" type="stmt"/>
        <line num="375" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="376" count="0" type="stmt"/>
        <line num="377" count="0" type="stmt"/>
        <line num="379" count="0" type="stmt"/>
        <line num="380" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="384" count="0" type="stmt"/>
        <line num="385" count="0" type="stmt"/>
        <line num="386" count="0" type="stmt"/>
        <line num="388" count="0" type="stmt"/>
        <line num="389" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="391" count="0" type="stmt"/>
        <line num="399" count="0" type="stmt"/>
        <line num="402" count="0" type="stmt"/>
        <line num="411" count="0" type="stmt"/>
        <line num="413" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="414" count="0" type="stmt"/>
        <line num="415" count="0" type="stmt"/>
        <line num="418" count="0" type="stmt"/>
        <line num="426" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="429" count="0" type="stmt"/>
        <line num="431" count="0" type="stmt"/>
        <line num="435" count="0" type="stmt"/>
        <line num="440" count="0" type="stmt"/>
        <line num="449" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="450" count="0" type="stmt"/>
        <line num="451" count="0" type="stmt"/>
        <line num="453" count="0" type="cond" truecount="0" falsecount="10"/>
        <line num="458" count="0" type="stmt"/>
        <line num="466" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="467" count="0" type="stmt"/>
        <line num="468" count="0" type="stmt"/>
        <line num="470" count="0" type="stmt"/>
        <line num="475" count="0" type="stmt"/>
        <line num="476" count="0" type="stmt"/>
        <line num="481" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="482" count="0" type="stmt"/>
        <line num="484" count="0" type="stmt"/>
        <line num="486" count="0" type="stmt"/>
        <line num="492" count="0" type="stmt"/>
        <line num="494" count="0" type="stmt"/>
        <line num="495" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="496" count="0" type="stmt"/>
        <line num="499" count="0" type="stmt"/>
        <line num="500" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="501" count="0" type="stmt"/>
        <line num="504" count="0" type="stmt"/>
        <line num="507" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="508" count="0" type="stmt"/>
        <line num="510" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="511" count="0" type="stmt"/>
        <line num="513" count="0" type="stmt"/>
        <line num="515" count="0" type="stmt"/>
        <line num="521" count="0" type="stmt"/>
        <line num="523" count="0" type="stmt"/>
        <line num="524" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="525" count="0" type="stmt"/>
        <line num="528" count="0" type="stmt"/>
        <line num="529" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="530" count="0" type="stmt"/>
        <line num="532" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="534" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="535" count="0" type="stmt"/>
        <line num="536" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="537" count="0" type="stmt"/>
        <line num="540" count="0" type="stmt"/>
        <line num="541" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="542" count="0" type="stmt"/>
        <line num="545" count="0" type="stmt"/>
        <line num="546" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="547" count="0" type="stmt"/>
        <line num="551" count="0" type="stmt"/>
        <line num="554" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="555" count="0" type="stmt"/>
        <line num="557" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="558" count="0" type="stmt"/>
        <line num="560" count="0" type="stmt"/>
        <line num="562" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.features.planificacion.hooks">
      <metrics statements="80" coveredstatements="0" conditionals="48" coveredconditionals="0" methods="22" coveredmethods="0"/>
      <file name="usePlanCalendario.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\features\planificacion\hooks\usePlanCalendario.ts">
        <metrics statements="80" coveredstatements="0" conditionals="48" coveredconditionals="0" methods="22" coveredmethods="0"/>
        <line num="11" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="17" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="18" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="77" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="168" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="174" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="184" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="198" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="207" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="208" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.features.planificacion.services">
      <metrics statements="471" coveredstatements="0" conditionals="264" coveredconditionals="0" methods="46" coveredmethods="0"/>
      <file name="planEstudiosClientService.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\features\planificacion\services\planEstudiosClientService.ts">
        <metrics statements="74" coveredstatements="0" conditionals="48" coveredconditionals="0" methods="12" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="10" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="15" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="22" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="29" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="46" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="69" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="75" count="0" type="stmt"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="104" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="122" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="143" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="149" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="150" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
      </file>
      <file name="planEstudiosService.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\features\planificacion\services\planEstudiosService.ts">
        <metrics statements="110" coveredstatements="0" conditionals="60" coveredconditionals="0" methods="17" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="23" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="50" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="67" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="72" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="76" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="90" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="108" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="139" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="145" count="0" type="stmt"/>
        <line num="153" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="174" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="191" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="198" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="199" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="202" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="214" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="215" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="235" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="236" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="238" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="242" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="243" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
      </file>
      <file name="planGeneratorService.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\features\planificacion\services\planGeneratorService.ts">
        <metrics statements="209" coveredstatements="0" conditionals="116" coveredconditionals="0" methods="10" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="65" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="74" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="79" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="114" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="115" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="124" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="127" count="0" type="stmt"/>
        <line num="129" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="133" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="140" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="156" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="166" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="170" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="210" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="211" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="230" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="235" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="237" count="0" type="stmt"/>
        <line num="238" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="239" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="245" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="246" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="253" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="254" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="266" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="267" count="0" type="stmt"/>
        <line num="270" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="271" count="0" type="stmt"/>
        <line num="274" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="275" count="0" type="stmt"/>
        <line num="276" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="277" count="0" type="stmt"/>
        <line num="280" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="281" count="0" type="stmt"/>
        <line num="284" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="285" count="0" type="stmt"/>
        <line num="288" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="289" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="293" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
        <line num="300" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="301" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="305" count="0" type="stmt"/>
        <line num="310" count="0" type="stmt"/>
        <line num="312" count="0" type="stmt"/>
        <line num="313" count="0" type="stmt"/>
        <line num="315" count="0" type="stmt"/>
        <line num="316" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="319" count="0" type="stmt"/>
        <line num="321" count="0" type="stmt"/>
        <line num="322" count="0" type="stmt"/>
        <line num="324" count="0" type="stmt"/>
        <line num="325" count="0" type="stmt"/>
        <line num="327" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="328" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="329" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="330" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="331" count="0" type="stmt"/>
        <line num="332" count="0" type="stmt"/>
        <line num="334" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="335" count="0" type="stmt"/>
        <line num="338" count="0" type="stmt"/>
        <line num="340" count="0" type="stmt"/>
        <line num="341" count="0" type="stmt"/>
        <line num="342" count="0" type="stmt"/>
        <line num="345" count="0" type="stmt"/>
        <line num="346" count="0" type="stmt"/>
        <line num="347" count="0" type="stmt"/>
        <line num="349" count="0" type="stmt"/>
        <line num="351" count="0" type="stmt"/>
        <line num="352" count="0" type="stmt"/>
        <line num="353" count="0" type="stmt"/>
        <line num="354" count="0" type="stmt"/>
        <line num="356" count="0" type="stmt"/>
        <line num="357" count="0" type="stmt"/>
        <line num="364" count="0" type="stmt"/>
        <line num="365" count="0" type="cond" truecount="0" falsecount="11"/>
        <line num="367" count="0" type="stmt"/>
        <line num="368" count="0" type="stmt"/>
        <line num="370" count="0" type="stmt"/>
        <line num="371" count="0" type="stmt"/>
        <line num="373" count="0" type="stmt"/>
        <line num="374" count="0" type="stmt"/>
        <line num="376" count="0" type="stmt"/>
        <line num="377" count="0" type="stmt"/>
        <line num="379" count="0" type="stmt"/>
        <line num="380" count="0" type="stmt"/>
        <line num="382" count="0" type="stmt"/>
        <line num="383" count="0" type="stmt"/>
        <line num="385" count="0" type="stmt"/>
        <line num="386" count="0" type="stmt"/>
        <line num="388" count="0" type="stmt"/>
        <line num="389" count="0" type="stmt"/>
        <line num="391" count="0" type="stmt"/>
        <line num="392" count="0" type="stmt"/>
        <line num="394" count="0" type="stmt"/>
        <line num="395" count="0" type="stmt"/>
        <line num="398" count="0" type="stmt"/>
        <line num="399" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="400" count="0" type="stmt"/>
        <line num="402" count="0" type="stmt"/>
        <line num="403" count="0" type="stmt"/>
        <line num="405" count="0" type="stmt"/>
      </file>
      <file name="planificacionService.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\features\planificacion\services\planificacionService.ts">
        <metrics statements="78" coveredstatements="0" conditionals="40" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="15" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="16" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="22" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="41" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="46" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="70" count="0" type="stmt"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="90" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="110" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.features.planificacion.types">
      <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="calendarTypes.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\features\planificacion\types\calendarTypes.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.features.planificacion.utils">
      <metrics statements="198" coveredstatements="0" conditionals="184" coveredconditionals="0" methods="28" coveredmethods="0"/>
      <file name="calendarioPreferences.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\features\planificacion\utils\calendarioPreferences.ts">
        <metrics statements="78" coveredstatements="0" conditionals="61" coveredconditionals="0" methods="16" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="9" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="10" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="15" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="39" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="64" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="88" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="89" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="97" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="135" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="136" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="155" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="161" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="173" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="185" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="186" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="192" count="0" type="stmt"/>
        <line num="198" count="0" type="cond" truecount="0" falsecount="2"/>
      </file>
      <file name="planDateUtils.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\features\planificacion\utils\planDateUtils.ts">
        <metrics statements="120" coveredstatements="0" conditionals="123" coveredconditionals="0" methods="12" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="24" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="27" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="47" count="0" type="stmt"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="50" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="84" count="0" type="stmt"/>
        <line num="93" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="94" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="101" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="110" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="111" count="0" type="stmt"/>
        <line num="121" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="129" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="156" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="157" count="0" type="stmt"/>
        <line num="159" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="160" count="0" type="stmt"/>
        <line num="162" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="163" count="0" type="stmt"/>
        <line num="165" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="166" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="173" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="200" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="203" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="204" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="222" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="233" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="234" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="239" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="248" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="249" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="258" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="259" count="0" type="stmt"/>
        <line num="262" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="263" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="279" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="284" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="286" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="291" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.features.temario.services">
      <metrics statements="165" coveredstatements="0" conditionals="95" coveredconditionals="0" methods="25" coveredmethods="0"/>
      <file name="temarioService.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\features\temario\services\temarioService.ts">
        <metrics statements="111" coveredstatements="0" conditionals="57" coveredconditionals="0" methods="13" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="15" count="0" type="cond" truecount="0" falsecount="13"/>
        <line num="16" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="22" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="34" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="39" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="68" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="85" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="89" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="123" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="142" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="175" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="176" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="196" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="203" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
      </file>
      <file name="temariosPredefinidosService.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\features\temario\services\temariosPredefinidosService.ts">
        <metrics statements="54" coveredstatements="0" conditionals="38" coveredconditionals="0" methods="12" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="49" count="0" type="stmt"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="56" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="100" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="101" count="0" type="stmt"/>
        <line num="103" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="104" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="110" count="0" type="stmt"/>
        <line num="113" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="114" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="137" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="138" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="149" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.features.tests.services">
      <metrics statements="157" coveredstatements="5" conditionals="161" coveredconditionals="0" methods="36" coveredmethods="1"/>
      <file name="testGenerator.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\features\tests\services\testGenerator.ts">
        <metrics statements="27" coveredstatements="0" conditionals="21" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="12" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="19" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="37" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="43" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="8"/>
        <line num="48" count="0" type="stmt"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="57" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
      </file>
      <file name="testsService.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\features\tests\services\testsService.ts">
        <metrics statements="130" coveredstatements="5" conditionals="140" coveredconditionals="0" methods="34" coveredmethods="1"/>
        <line num="2" count="2" type="stmt"/>
        <line num="6" count="22" type="stmt"/>
        <line num="11" count="2" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="46" count="2" type="stmt"/>
        <line num="47" count="2" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="71" count="0" type="cond" truecount="0" falsecount="8"/>
        <line num="72" count="0" type="cond" truecount="0" falsecount="10"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="89" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="93" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="109" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="156" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="160" count="0" type="cond" truecount="0" falsecount="10"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="165" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="168" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="171" count="0" type="stmt"/>
        <line num="175" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="179" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="184" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="199" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="200" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="208" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="209" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="219" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="221" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="223" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="224" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="226" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="227" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="238" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="239" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="253" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="254" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="256" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="258" count="0" type="stmt"/>
        <line num="259" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="260" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="263" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="266" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="267" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="271" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="272" count="0" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="286" count="0" type="cond" truecount="0" falsecount="10"/>
        <line num="288" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.hooks">
      <metrics statements="356" coveredstatements="7" conditionals="229" coveredconditionals="0" methods="102" coveredmethods="1"/>
      <file name="useBackgroundGeneration.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\hooks\useBackgroundGeneration.ts">
        <metrics statements="61" coveredstatements="0" conditionals="30" coveredconditionals="0" methods="15" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="54" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="60" count="0" type="stmt"/>
        <line num="65" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="66" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="132" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="133" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="143" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="144" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="148" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="150" count="0" type="stmt"/>
        <line num="155" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="156" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="166" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="176" count="0" type="stmt"/>
      </file>
      <file name="useFreeAccount.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\hooks\useFreeAccount.ts">
        <metrics statements="88" coveredstatements="0" conditionals="75" coveredconditionals="0" methods="22" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="22" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="39" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="54" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="79" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="80" count="0" type="stmt"/>
        <line num="82" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="83" count="0" type="stmt"/>
        <line num="85" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="86" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="87" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="114" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="129" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="130" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="131" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="132" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="133" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="183" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="184" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="185" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="208" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="209" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="215" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="216" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="231" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="232" count="0" type="stmt"/>
        <line num="233" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="234" count="0" type="stmt"/>
        <line num="235" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="236" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
      </file>
      <file name="useMobileAuth.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\hooks\useMobileAuth.ts">
        <metrics statements="28" coveredstatements="0" conditionals="22" coveredconditionals="0" methods="12" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="28" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="46" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
      </file>
      <file name="usePlanEstudiosResults.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\hooks\usePlanEstudiosResults.ts">
        <metrics statements="19" coveredstatements="0" conditionals="17" coveredconditionals="0" methods="8" coveredmethods="0"/>
        <line num="4" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="12" count="0" type="stmt"/>
        <line num="14" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="15" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="20" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="34" count="0" type="stmt"/>
      </file>
      <file name="usePlanLimits.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\hooks\usePlanLimits.ts">
        <metrics statements="105" coveredstatements="7" conditionals="53" coveredconditionals="0" methods="33" coveredmethods="1"/>
        <line num="5" count="1" type="stmt"/>
        <line num="9" count="6" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="36" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="57" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="67" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="79" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="154" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="179" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="231" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="233" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="234" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="235" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
        <line num="263" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="265" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="266" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="267" count="0" type="stmt"/>
        <line num="268" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="269" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="271" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="274" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="275" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="291" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
      </file>
      <file name="useTaskResults.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\hooks\useTaskResults.ts">
        <metrics statements="31" coveredstatements="0" conditionals="24" coveredconditionals="0" methods="9" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="15" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="17" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="26" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="28" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="44" count="0" type="stmt"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
      </file>
      <file name="useUserPlan.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\hooks\useUserPlan.ts">
        <metrics statements="24" coveredstatements="0" conditionals="8" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="22" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="29" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.lib">
      <metrics statements="22" coveredstatements="7" conditionals="4" coveredconditionals="3" methods="3" coveredmethods="3"/>
      <file name="formSchemas.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\formSchemas.ts">
        <metrics statements="5" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="2" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
      </file>
      <file name="gemini.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\gemini.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
      <file name="supabase.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\supabase.ts">
        <metrics statements="7" coveredstatements="7" conditionals="4" coveredconditionals="3" methods="3" coveredmethods="3"/>
        <line num="4" count="2" type="stmt"/>
        <line num="7" count="2" type="stmt"/>
        <line num="9" count="2" type="stmt"/>
        <line num="10" count="96" type="cond" truecount="3" falsecount="1"/>
        <line num="11" count="96" type="stmt"/>
        <line num="14" count="23" type="stmt"/>
        <line num="19" count="2" type="stmt"/>
      </file>
      <file name="zodSchemas.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\zodSchemas.ts">
        <metrics statements="10" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.lib.ai">
      <metrics statements="63" coveredstatements="0" conditionals="28" coveredconditionals="0" methods="13" coveredmethods="0"/>
      <file name="tokenTracker.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\ai\tokenTracker.ts">
        <metrics statements="63" coveredstatements="0" conditionals="28" coveredconditionals="0" methods="13" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="68" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="90" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="93" count="0" type="stmt"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="96" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="124" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="125" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="135" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="148" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="149" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="159" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="160" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.lib.auth">
      <metrics statements="72" coveredstatements="0" conditionals="43" coveredconditionals="0" methods="9" coveredmethods="0"/>
      <file name="validateUserAccess.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\auth\validateUserAccess.ts">
        <metrics statements="72" coveredstatements="0" conditionals="43" coveredconditionals="0" methods="9" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="13" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="22" count="0" type="stmt"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="30" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="57" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="113" count="0" type="stmt"/>
        <line num="116" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="122" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="164" count="0" type="stmt"/>
        <line num="170" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="173" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="193" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="200" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="201" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="208" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="209" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.lib.gemini">
      <metrics statements="155" coveredstatements="0" conditionals="112" coveredconditionals="0" methods="8" coveredmethods="0"/>
      <file name="flashcardGenerator.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\gemini\flashcardGenerator.ts">
        <metrics statements="22" coveredstatements="0" conditionals="13" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="12" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="19" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="46" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
      </file>
      <file name="geminiClient.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\gemini\geminiClient.ts">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
      </file>
      <file name="mindMapGenerator.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\gemini\mindMapGenerator.ts">
        <metrics statements="31" coveredstatements="0" conditionals="26" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="11" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="16" count="0" type="stmt"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="39" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="46" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="65" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="66" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
      </file>
      <file name="questionService.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\gemini\questionService.ts">
        <metrics statements="20" coveredstatements="0" conditionals="14" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="15" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="46" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
      </file>
      <file name="resumenEditor.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\gemini\resumenEditor.ts">
        <metrics statements="18" coveredstatements="0" conditionals="10" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="9" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="10" count="0" type="stmt"/>
        <line num="13" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="14" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
      </file>
      <file name="resumenGenerator.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\gemini\resumenGenerator.ts">
        <metrics statements="28" coveredstatements="0" conditionals="24" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="11" count="0" type="stmt"/>
        <line num="14" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="15" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="23" count="0" type="stmt"/>
        <line num="26" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="47" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="53" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="61" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
      </file>
      <file name="testGenerator.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\gemini\testGenerator.ts">
        <metrics statements="32" coveredstatements="0" conditionals="25" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="12" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="19" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="55" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="8"/>
        <line num="60" count="0" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="69" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.lib.openai">
      <metrics statements="61" coveredstatements="0" conditionals="71" coveredconditionals="0" methods="6" coveredmethods="0"/>
      <file name="openaiClient.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\openai\openaiClient.ts">
        <metrics statements="61" coveredstatements="0" conditionals="71" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="6" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="17" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="24" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="31" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="39" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="40" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="80" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="81" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="86" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="88" count="0" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="94" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="cond" truecount="0" falsecount="8"/>
        <line num="104" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="105" count="0" type="stmt"/>
        <line num="108" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="121" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="122" count="0" type="stmt"/>
        <line num="126" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="127" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="134" count="0" type="cond" truecount="0" falsecount="2"/>
      </file>
    </package>
    <package name="src.lib.services">
      <metrics statements="893" coveredstatements="222" conditionals="777" coveredconditionals="171" methods="65" coveredmethods="33"/>
      <file name="freeAccountService.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\services\freeAccountService.ts">
        <metrics statements="119" coveredstatements="4" conditionals="112" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="4" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="26" count="0" type="stmt"/>
        <line num="32" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="33" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="64" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="71" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="72" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="113" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="145" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="149" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="150" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="163" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="175" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="200" count="0" type="stmt"/>
        <line num="202" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="209" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="210" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="229" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="230" count="0" type="stmt"/>
        <line num="235" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="236" count="0" type="stmt"/>
        <line num="241" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="242" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="244" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="245" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="271" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="272" count="0" type="stmt"/>
        <line num="274" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="275" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="296" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="297" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
        <line num="308" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="309" count="0" type="stmt"/>
        <line num="310" count="0" type="stmt"/>
        <line num="313" count="0" type="stmt"/>
        <line num="314" count="0" type="stmt"/>
        <line num="319" count="0" type="stmt"/>
        <line num="320" count="0" type="stmt"/>
      </file>
      <file name="limitHandler.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\services\limitHandler.ts">
        <metrics statements="193" coveredstatements="87" conditionals="157" coveredconditionals="63" methods="21" coveredmethods="15"/>
        <line num="4" count="2" type="stmt"/>
        <line num="7" count="2" type="stmt"/>
        <line num="10" count="19" type="stmt"/>
        <line num="13" count="2" type="stmt"/>
        <line num="14" count="2" type="stmt"/>
        <line num="15" count="2" type="stmt"/>
        <line num="17" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="15" type="cond" truecount="1" falsecount="1"/>
        <line num="25" count="15" type="cond" truecount="4" falsecount="1"/>
        <line num="26" count="0" type="stmt"/>
        <line num="28" count="15" type="cond" truecount="3" falsecount="2"/>
        <line num="29" count="0" type="stmt"/>
        <line num="33" count="15" type="stmt"/>
        <line num="34" count="15" type="cond" truecount="4" falsecount="0"/>
        <line num="35" count="14" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="40" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="41" count="1" type="stmt"/>
        <line num="42" count="1" type="cond" truecount="3" falsecount="1"/>
        <line num="43" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="44" count="1" type="cond" truecount="4" falsecount="1"/>
        <line num="45" count="0" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="52" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="53" count="1" type="stmt"/>
        <line num="55" count="1" type="stmt"/>
        <line num="61" count="13" type="stmt"/>
        <line num="63" count="13" type="stmt"/>
        <line num="64" count="13" type="stmt"/>
        <line num="65" count="12" type="cond" truecount="2" falsecount="0"/>
        <line num="66" count="1" type="stmt"/>
        <line num="68" count="11" type="stmt"/>
        <line num="70" count="11" type="stmt"/>
        <line num="71" count="11" type="cond" truecount="1" falsecount="1"/>
        <line num="72" count="0" type="stmt"/>
        <line num="75" count="11" type="stmt"/>
        <line num="76" count="11" type="cond" truecount="2" falsecount="0"/>
        <line num="77" count="2" type="stmt"/>
        <line num="79" count="11" type="stmt"/>
        <line num="81" count="1" type="stmt"/>
        <line num="82" count="1" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="92" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="100" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="101" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="107" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="112" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="123" count="11" type="stmt"/>
        <line num="124" count="11" type="cond" truecount="1" falsecount="1"/>
        <line num="125" count="11" type="stmt"/>
        <line num="127" count="11" type="stmt"/>
        <line num="128" count="11" type="stmt"/>
        <line num="129" count="11" type="stmt"/>
        <line num="130" count="11" type="stmt"/>
        <line num="131" count="11" type="cond" truecount="1" falsecount="1"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="11" type="cond" truecount="1" falsecount="1"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="11" type="cond" truecount="1" falsecount="1"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="148" count="11" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="168" count="11" type="cond" truecount="4" falsecount="0"/>
        <line num="169" count="2" type="stmt"/>
        <line num="182" count="9" type="cond" truecount="1" falsecount="1"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="186" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="187" count="0" type="stmt"/>
        <line num="198" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="199" count="0" type="stmt"/>
        <line num="212" count="9" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="221" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="222" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="223" count="0" type="stmt"/>
        <line num="233" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="234" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="235" count="0" type="stmt"/>
        <line num="246" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="247" count="0" type="stmt"/>
        <line num="248" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="249" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="250" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
        <line num="267" count="2" type="stmt"/>
        <line num="281" count="2" type="cond" truecount="5" falsecount="0"/>
        <line num="282" count="1" type="stmt"/>
        <line num="283" count="1" type="stmt"/>
        <line num="286" count="2" type="stmt"/>
        <line num="287" count="2" type="stmt"/>
        <line num="292" count="2" type="cond" truecount="1" falsecount="2"/>
        <line num="294" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="295" count="1" type="stmt"/>
        <line num="296" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="297" count="0" type="stmt"/>
        <line num="299" count="1" type="stmt"/>
        <line num="302" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="303" count="0" type="stmt"/>
        <line num="305" count="0" type="stmt"/>
        <line num="308" count="0" type="stmt"/>
        <line num="314" count="4" type="stmt"/>
        <line num="315" count="4" type="stmt"/>
        <line num="317" count="4" type="cond" truecount="1" falsecount="1"/>
        <line num="318" count="4" type="stmt"/>
        <line num="319" count="4" type="cond" truecount="2" falsecount="2"/>
        <line num="320" count="0" type="stmt"/>
        <line num="326" count="4" type="cond" truecount="2" falsecount="2"/>
        <line num="327" count="0" type="stmt"/>
        <line num="335" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="336" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="337" count="1" type="stmt"/>
        <line num="343" count="3" type="stmt"/>
        <line num="347" count="0" type="stmt"/>
        <line num="348" count="0" type="stmt"/>
        <line num="357" count="0" type="stmt"/>
        <line num="358" count="0" type="stmt"/>
        <line num="360" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="361" count="0" type="stmt"/>
        <line num="362" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="363" count="0" type="stmt"/>
        <line num="369" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="370" count="0" type="stmt"/>
        <line num="378" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="379" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="380" count="0" type="stmt"/>
        <line num="386" count="0" type="stmt"/>
        <line num="390" count="0" type="stmt"/>
        <line num="391" count="0" type="stmt"/>
        <line num="400" count="3" type="stmt"/>
        <line num="401" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="403" count="2" type="stmt"/>
        <line num="405" count="2" type="stmt"/>
        <line num="406" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="407" count="2" type="stmt"/>
        <line num="408" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="409" count="2" type="stmt"/>
        <line num="415" count="2" type="stmt"/>
        <line num="419" count="3" type="stmt"/>
        <line num="421" count="0" type="stmt"/>
        <line num="427" count="0" type="stmt"/>
        <line num="428" count="0" type="stmt"/>
        <line num="429" count="0" type="stmt"/>
        <line num="430" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="431" count="0" type="stmt"/>
        <line num="432" count="0" type="stmt"/>
        <line num="434" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="436" count="0" type="stmt"/>
        <line num="437" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="438" count="0" type="stmt"/>
        <line num="439" count="0" type="stmt"/>
        <line num="441" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="442" count="0" type="stmt"/>
        <line num="443" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="445" count="0" type="stmt"/>
        <line num="450" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="451" count="0" type="stmt"/>
        <line num="453" count="0" type="stmt"/>
        <line num="458" count="0" type="stmt"/>
        <line num="460" count="0" type="stmt"/>
      </file>
      <file name="permissionService.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\services\permissionService.ts">
        <metrics statements="110" coveredstatements="66" conditionals="144" coveredconditionals="75" methods="13" coveredmethods="11"/>
        <line num="4" count="2" type="stmt"/>
        <line num="7" count="2" type="stmt"/>
        <line num="10" count="16" type="stmt"/>
        <line num="13" count="2" type="stmt"/>
        <line num="14" count="2" type="stmt"/>
        <line num="15" count="2" type="stmt"/>
        <line num="17" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="13" type="cond" truecount="1" falsecount="1"/>
        <line num="25" count="13" type="cond" truecount="4" falsecount="1"/>
        <line num="26" count="0" type="stmt"/>
        <line num="28" count="13" type="cond" truecount="3" falsecount="2"/>
        <line num="29" count="0" type="stmt"/>
        <line num="33" count="13" type="stmt"/>
        <line num="34" count="13" type="cond" truecount="4" falsecount="0"/>
        <line num="35" count="12" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="40" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="41" count="1" type="stmt"/>
        <line num="42" count="1" type="cond" truecount="3" falsecount="1"/>
        <line num="43" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="44" count="1" type="cond" truecount="4" falsecount="1"/>
        <line num="45" count="0" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="52" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="53" count="1" type="stmt"/>
        <line num="55" count="1" type="stmt"/>
        <line num="61" count="13" type="stmt"/>
        <line num="63" count="13" type="stmt"/>
        <line num="65" count="13" type="stmt"/>
        <line num="66" count="11" type="cond" truecount="2" falsecount="0"/>
        <line num="67" count="1" type="stmt"/>
        <line num="73" count="10" type="cond" truecount="5" falsecount="0"/>
        <line num="74" count="1" type="stmt"/>
        <line num="82" count="9" type="cond" truecount="4" falsecount="0"/>
        <line num="83" count="5" type="cond" truecount="2" falsecount="0"/>
        <line num="84" count="2" type="stmt"/>
        <line num="95" count="7" type="cond" truecount="1" falsecount="1"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="108" count="7" type="cond" truecount="4" falsecount="0"/>
        <line num="109" count="6" type="stmt"/>
        <line num="110" count="6" type="cond" truecount="1" falsecount="1"/>
        <line num="111" count="0" type="stmt"/>
        <line num="122" count="7" type="stmt"/>
        <line num="124" count="7" type="cond" truecount="2" falsecount="0"/>
        <line num="125" count="7" type="stmt"/>
        <line num="131" count="2" type="stmt"/>
        <line num="133" count="2" type="stmt"/>
        <line num="134" count="2" type="stmt"/>
        <line num="143" count="1" type="stmt"/>
        <line num="144" count="1" type="stmt"/>
        <line num="145" count="3" type="stmt"/>
        <line num="147" count="1" type="stmt"/>
        <line num="152" count="6" type="stmt"/>
        <line num="154" count="6" type="cond" truecount="1" falsecount="1"/>
        <line num="155" count="6" type="stmt"/>
        <line num="162" count="6" type="cond" truecount="1" falsecount="1"/>
        <line num="163" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="164" count="0" type="stmt"/>
        <line num="172" count="6" type="stmt"/>
        <line num="180" count="7" type="stmt"/>
        <line num="181" count="7" type="cond" truecount="1" falsecount="1"/>
        <line num="182" count="7" type="stmt"/>
        <line num="193" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="194" count="1" type="stmt"/>
        <line num="197" count="1" type="cond" truecount="3" falsecount="1"/>
        <line num="198" count="1" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="214" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="225" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="226" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="233" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="234" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="240" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="241" count="0" type="stmt"/>
        <line num="247" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="248" count="0" type="stmt"/>
        <line num="256" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="257" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="258" count="0" type="stmt"/>
        <line num="269" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="270" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="282" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="283" count="0" type="stmt"/>
        <line num="284" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="285" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="304" count="0" type="stmt"/>
        <line num="313" count="4" type="stmt"/>
        <line num="359" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="360" count="4" type="stmt"/>
      </file>
      <file name="planValidation.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\services\planValidation.ts">
        <metrics statements="124" coveredstatements="65" conditionals="99" coveredconditionals="33" methods="11" coveredmethods="7"/>
        <line num="4" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="10" count="13" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="26" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="27" count="0" type="stmt"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="30" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="41" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="46" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="54" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="62" count="7" type="stmt"/>
        <line num="64" count="7" type="stmt"/>
        <line num="65" count="7" type="cond" truecount="1" falsecount="1"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="73" count="7" type="cond" truecount="4" falsecount="0"/>
        <line num="74" count="1" type="stmt"/>
        <line num="75" count="1" type="stmt"/>
        <line num="81" count="6" type="cond" truecount="2" falsecount="0"/>
        <line num="82" count="3" type="stmt"/>
        <line num="83" count="3" type="stmt"/>
        <line num="89" count="3" type="cond" truecount="1" falsecount="1"/>
        <line num="90" count="3" type="stmt"/>
        <line num="91" count="3" type="cond" truecount="1" falsecount="1"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="97" count="3" type="stmt"/>
        <line num="98" count="3" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="118" count="3" type="stmt"/>
        <line num="120" count="3" type="cond" truecount="1" falsecount="1"/>
        <line num="121" count="3" type="stmt"/>
        <line num="127" count="3" type="stmt"/>
        <line num="128" count="3" type="stmt"/>
        <line num="131" count="3" type="stmt"/>
        <line num="132" count="3" type="cond" truecount="1" falsecount="1"/>
        <line num="133" count="0" type="stmt"/>
        <line num="139" count="3" type="stmt"/>
        <line num="147" count="2" type="stmt"/>
        <line num="148" count="2" type="stmt"/>
        <line num="149" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="150" count="1" type="stmt"/>
        <line num="152" count="1" type="stmt"/>
        <line num="153" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="154" count="0" type="stmt"/>
        <line num="157" count="1" type="stmt"/>
        <line num="167" count="1" type="stmt"/>
        <line num="168" count="1" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="171" count="1" type="stmt"/>
        <line num="174" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="179" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="192" count="1" type="stmt"/>
        <line num="200" count="1" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="216" count="2" type="stmt"/>
        <line num="217" count="2" type="stmt"/>
        <line num="218" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="219" count="0" type="stmt"/>
        <line num="225" count="2" type="stmt"/>
        <line num="233" count="2" type="stmt"/>
        <line num="235" count="2" type="stmt"/>
        <line num="236" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="237" count="2" type="stmt"/>
        <line num="240" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="241" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="247" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="248" count="0" type="stmt"/>
        <line num="249" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="250" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="263" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="273" count="2" type="stmt"/>
        <line num="274" count="2" type="stmt"/>
        <line num="275" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="276" count="0" type="stmt"/>
        <line num="278" count="2" type="stmt"/>
        <line num="279" count="2" type="stmt"/>
        <line num="284" count="2" type="stmt"/>
        <line num="285" count="2" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="294" count="2" type="stmt"/>
        <line num="295" count="2" type="stmt"/>
        <line num="296" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="297" count="0" type="stmt"/>
        <line num="302" count="2" type="stmt"/>
        <line num="303" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="304" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="305" count="1" type="stmt"/>
        <line num="311" count="1" type="stmt"/>
        <line num="315" count="0" type="stmt"/>
        <line num="316" count="0" type="stmt"/>
      </file>
      <file name="stripeWebhookHandlers.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\services\stripeWebhookHandlers.ts">
        <metrics statements="203" coveredstatements="0" conditionals="183" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="12" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="16" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="17" count="0" type="stmt"/>
        <line num="24" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="25" count="0" type="stmt"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="30" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="53" count="0" type="stmt"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="58" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="78" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="85" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="126" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="137" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="158" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="198" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="200" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="201" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="202" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="206" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="221" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="222" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="226" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="227" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="230" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="231" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="275" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="277" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="278" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="279" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="287" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="288" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="300" count="0" type="stmt"/>
        <line num="309" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="310" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="319" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="320" count="0" type="stmt"/>
        <line num="321" count="0" type="stmt"/>
        <line num="327" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="332" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="333" count="0" type="stmt"/>
        <line num="334" count="0" type="stmt"/>
        <line num="340" count="0" type="stmt"/>
        <line num="346" count="0" type="stmt"/>
        <line num="356" count="0" type="stmt"/>
        <line num="357" count="0" type="stmt"/>
        <line num="368" count="0" type="stmt"/>
        <line num="369" count="0" type="stmt"/>
        <line num="372" count="0" type="stmt"/>
        <line num="380" count="0" type="stmt"/>
        <line num="381" count="0" type="stmt"/>
        <line num="392" count="0" type="stmt"/>
        <line num="394" count="0" type="stmt"/>
        <line num="396" count="0" type="stmt"/>
        <line num="397" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="398" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="399" count="0" type="stmt"/>
        <line num="405" count="0" type="stmt"/>
        <line num="413" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="414" count="0" type="stmt"/>
        <line num="417" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="418" count="0" type="stmt"/>
        <line num="419" count="0" type="stmt"/>
        <line num="421" count="0" type="stmt"/>
        <line num="434" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="436" count="0" type="stmt"/>
        <line num="437" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="438" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="440" count="0" type="stmt"/>
        <line num="441" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="442" count="0" type="stmt"/>
        <line num="443" count="0" type="stmt"/>
        <line num="456" count="0" type="stmt"/>
        <line num="457" count="0" type="stmt"/>
        <line num="461" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="462" count="0" type="stmt"/>
        <line num="464" count="0" type="stmt"/>
        <line num="466" count="0" type="stmt"/>
        <line num="475" count="0" type="stmt"/>
        <line num="476" count="0" type="stmt"/>
        <line num="487" count="0" type="stmt"/>
        <line num="488" count="0" type="stmt"/>
        <line num="490" count="0" type="stmt"/>
        <line num="491" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="492" count="0" type="stmt"/>
        <line num="498" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="499" count="0" type="stmt"/>
        <line num="508" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="509" count="0" type="stmt"/>
        <line num="510" count="0" type="stmt"/>
        <line num="515" count="0" type="stmt"/>
        <line num="523" count="0" type="stmt"/>
        <line num="524" count="0" type="stmt"/>
        <line num="535" count="0" type="stmt"/>
        <line num="536" count="0" type="stmt"/>
        <line num="538" count="0" type="stmt"/>
        <line num="539" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="540" count="0" type="stmt"/>
        <line num="546" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="547" count="0" type="stmt"/>
        <line num="549" count="0" type="stmt"/>
        <line num="562" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="563" count="0" type="stmt"/>
        <line num="564" count="0" type="stmt"/>
        <line num="571" count="0" type="stmt"/>
        <line num="579" count="0" type="stmt"/>
        <line num="581" count="0" type="stmt"/>
        <line num="582" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="584" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="585" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="587" count="0" type="stmt"/>
        <line num="588" count="0" type="stmt"/>
        <line num="591" count="0" type="stmt"/>
        <line num="594" count="0" type="stmt"/>
        <line num="604" count="0" type="stmt"/>
        <line num="605" count="0" type="stmt"/>
        <line num="616" count="0" type="stmt"/>
        <line num="617" count="0" type="stmt"/>
        <line num="619" count="0" type="stmt"/>
        <line num="620" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="621" count="0" type="stmt"/>
        <line num="627" count="0" type="stmt"/>
        <line num="628" count="0" type="stmt"/>
        <line num="629" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="630" count="0" type="stmt"/>
        <line num="631" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="634" count="0" type="stmt"/>
        <line num="637" count="0" type="stmt"/>
        <line num="639" count="0" type="stmt"/>
        <line num="643" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="644" count="0" type="stmt"/>
        <line num="646" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="647" count="0" type="stmt"/>
        <line num="648" count="0" type="stmt"/>
        <line num="649" count="0" type="stmt"/>
        <line num="652" count="0" type="stmt"/>
        <line num="653" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="654" count="0" type="stmt"/>
        <line num="656" count="0" type="stmt"/>
        <line num="664" count="0" type="stmt"/>
        <line num="665" count="0" type="stmt"/>
      </file>
      <file name="userManagement.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\services\userManagement.ts">
        <metrics statements="144" coveredstatements="0" conditionals="82" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="14" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="79" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="80" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="81" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="85" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="124" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="156" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="161" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="203" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="247" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="250" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="253" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="255" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="262" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="264" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="275" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="284" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="285" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="290" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="292" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="297" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="298" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="307" count="0" type="stmt"/>
        <line num="321" count="0" type="stmt"/>
        <line num="323" count="0" type="stmt"/>
        <line num="330" count="0" type="stmt"/>
        <line num="331" count="0" type="stmt"/>
        <line num="337" count="0" type="stmt"/>
        <line num="338" count="0" type="stmt"/>
        <line num="348" count="0" type="stmt"/>
        <line num="349" count="0" type="stmt"/>
        <line num="350" count="0" type="stmt"/>
        <line num="352" count="0" type="stmt"/>
        <line num="356" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="357" count="0" type="stmt"/>
        <line num="359" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="360" count="0" type="stmt"/>
        <line num="361" count="0" type="stmt"/>
        <line num="366" count="0" type="stmt"/>
        <line num="367" count="0" type="stmt"/>
        <line num="368" count="0" type="stmt"/>
        <line num="370" count="0" type="stmt"/>
        <line num="371" count="0" type="stmt"/>
        <line num="374" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="375" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="376" count="0" type="stmt"/>
        <line num="377" count="0" type="stmt"/>
        <line num="379" count="0" type="stmt"/>
        <line num="381" count="0" type="stmt"/>
        <line num="382" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="383" count="0" type="stmt"/>
        <line num="384" count="0" type="stmt"/>
        <line num="386" count="0" type="stmt"/>
        <line num="387" count="0" type="stmt"/>
        <line num="388" count="0" type="stmt"/>
        <line num="391" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="392" count="0" type="stmt"/>
        <line num="393" count="0" type="stmt"/>
        <line num="396" count="0" type="stmt"/>
        <line num="397" count="0" type="stmt"/>
        <line num="402" count="0" type="stmt"/>
        <line num="403" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.lib.services.email">
      <metrics statements="276" coveredstatements="0" conditionals="227" coveredconditionals="0" methods="44" coveredmethods="0"/>
      <file name="emailAnalytics.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\services\email\emailAnalytics.ts">
        <metrics statements="101" coveredstatements="0" conditionals="131" coveredconditionals="0" methods="16" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="11" count="0" type="stmt"/>
        <line num="13" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="14" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="18" count="0" type="stmt"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="22" count="0" type="stmt"/>
        <line num="24" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="26" count="0" type="stmt"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="29" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="51" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="54" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="58" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="65" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="66" count="0" type="stmt"/>
        <line num="68" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="69" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="78" count="0" type="stmt"/>
        <line num="80" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="82" count="0" type="stmt"/>
        <line num="86" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="88" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="112" count="0" type="stmt"/>
        <line num="114" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="115" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="118" count="0" type="stmt"/>
        <line num="120" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="121" count="0" type="stmt"/>
        <line num="123" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="124" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="134" count="0" type="stmt"/>
        <line num="136" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="137" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="141" count="0" type="stmt"/>
        <line num="143" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="144" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="145" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="147" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="150" count="0" type="stmt"/>
        <line num="153" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="156" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="182" count="0" type="stmt"/>
        <line num="184" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="185" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="189" count="0" type="stmt"/>
        <line num="192" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="195" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="203" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="204" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
      </file>
      <file name="emailLogger.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\services\email\emailLogger.ts">
        <metrics statements="64" coveredstatements="0" conditionals="55" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="17" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="18" count="0" type="stmt"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="22" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="26" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="52" count="0" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="59" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="63" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="82" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="83" count="0" type="stmt"/>
        <line num="85" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="86" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="90" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="97" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="98" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="121" count="0" type="stmt"/>
        <line num="123" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="134" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="135" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="148" count="0" type="stmt"/>
        <line num="150" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
      </file>
      <file name="emailNotificationService.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\services\email\emailNotificationService.ts">
        <metrics statements="32" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="13" coveredmethods="0"/>
        <line num="11" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
      </file>
      <file name="emailSender.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\services\email\emailSender.ts">
        <metrics statements="62" coveredstatements="0" conditionals="27" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="39" count="0" type="stmt"/>
        <line num="42" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="43" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="49" count="0" type="stmt"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="52" count="0" type="stmt"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="107" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
      </file>
      <file name="emailTemplates.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\services\email\emailTemplates.ts">
        <metrics statements="17" coveredstatements="0" conditionals="8" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
      </file>
      <file name="types.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\services\email\types.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.lib.stripe">
      <metrics statements="8" coveredstatements="0" conditionals="10" coveredconditionals="0" methods="3" coveredmethods="0"/>
      <file name="config.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\stripe\config.ts">
        <metrics statements="1" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="cond" truecount="0" falsecount="2"/>
      </file>
      <file name="plans.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\stripe\plans.ts">
        <metrics statements="7" coveredstatements="0" conditionals="8" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
        <line num="74" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="78" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="86" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.lib.supabase">
      <metrics statements="1327" coveredstatements="39" conditionals="1011" coveredconditionals="0" methods="211" coveredmethods="8"/>
      <file name="admin.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\supabase\admin.ts">
        <metrics statements="106" coveredstatements="0" conditionals="183" coveredconditionals="0" methods="16" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="61" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="96" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="107" count="0" type="cond" truecount="0" falsecount="8"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="118" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="123" count="0" type="cond" truecount="0" falsecount="8"/>
        <line num="124" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="cond" truecount="0" falsecount="8"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="156" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="186" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="204" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="217" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="228" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="229" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="239" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="248" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="249" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="260" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="261" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="270" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="271" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="281" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="282" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="285" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="289" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="291" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="292" count="0" type="stmt"/>
        <line num="293" count="0" type="stmt"/>
        <line num="295" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="296" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
        <line num="300" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="301" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
        <line num="310" count="0" type="stmt"/>
      </file>
      <file name="authService.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\supabase\authService.ts">
        <metrics statements="72" coveredstatements="4" conditionals="47" coveredconditionals="0" methods="15" coveredmethods="1"/>
        <line num="2" count="2" type="stmt"/>
        <line num="6" count="10" type="stmt"/>
        <line num="11" count="2" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="28" count="2" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="32" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="33" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="47" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="55" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="68" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="71" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="90" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="91" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="105" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="113" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="114" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="133" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="156" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="157" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="183" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="184" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
      </file>
      <file name="client.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\supabase\client.ts">
        <metrics statements="8" coveredstatements="6" conditionals="0" coveredconditionals="0" methods="4" coveredmethods="2"/>
        <line num="2" count="2" type="stmt"/>
        <line num="6" count="4" type="stmt"/>
        <line num="11" count="2" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="19" count="2" type="stmt"/>
        <line num="21" count="2" type="stmt"/>
        <line num="29" count="2" type="stmt"/>
      </file>
      <file name="conversacionesService.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\supabase\conversacionesService.ts">
        <metrics statements="126" coveredstatements="4" conditionals="75" coveredconditionals="0" methods="21" coveredmethods="1"/>
        <line num="2" count="2" type="stmt"/>
        <line num="6" count="20" type="stmt"/>
        <line num="11" count="2" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="43" count="2" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="55" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="68" count="0" type="cond" truecount="0" falsecount="10"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="85" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="89" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="108" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="123" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="145" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="166" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="172" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="173" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="179" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="198" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="199" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="206" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="207" count="0" type="stmt"/>
        <line num="210" count="0" type="cond" truecount="0" falsecount="10"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="220" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="221" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="224" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="227" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="230" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="236" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="237" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="244" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="245" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="248" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="249" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
      </file>
      <file name="dashboardService.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\supabase\dashboardService.ts">
        <metrics statements="66" coveredstatements="8" conditionals="33" coveredconditionals="0" methods="11" coveredmethods="1"/>
        <line num="2" count="2" type="stmt"/>
        <line num="6" count="4" type="stmt"/>
        <line num="11" count="2" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="19" count="2" type="stmt"/>
        <line num="20" count="2" type="stmt"/>
        <line num="21" count="2" type="stmt"/>
        <line num="22" count="2" type="stmt"/>
        <line num="23" count="2" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="28" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="103" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="108" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="116" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="120" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="121" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="134" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="137" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
      </file>
      <file name="documentosService.server.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\supabase\documentosService.server.ts">
        <metrics statements="14" coveredstatements="0" conditionals="20" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="13" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="24" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="10"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
      </file>
      <file name="documentosService.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\supabase\documentosService.ts">
        <metrics statements="65" coveredstatements="5" conditionals="30" coveredconditionals="0" methods="9" coveredmethods="1"/>
        <line num="2" count="2" type="stmt"/>
        <line num="6" count="8" type="stmt"/>
        <line num="11" count="2" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="25" count="2" type="stmt"/>
        <line num="26" count="2" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="85" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="89" count="0" type="cond" truecount="0" falsecount="10"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="109" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
      </file>
      <file name="estadisticasService.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\supabase\estadisticasService.ts">
        <metrics statements="128" coveredstatements="5" conditionals="80" coveredconditionals="0" methods="27" coveredmethods="1"/>
        <line num="2" count="2" type="stmt"/>
        <line num="6" count="8" type="stmt"/>
        <line num="11" count="2" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="25" count="2" type="stmt"/>
        <line num="26" count="2" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="31" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="93" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="103" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="119" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="146" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="147" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="178" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="202" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="206" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="217" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="221" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="224" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="227" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="228" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="245" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="246" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="251" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="252" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="268" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="269" count="0" type="stmt"/>
        <line num="270" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="271" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="272" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="275" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="291" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="293" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="294" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="295" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="296" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="297" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="302" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="308" count="0" type="stmt"/>
        <line num="316" count="0" type="stmt"/>
        <line num="319" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="320" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="321" count="0" type="stmt"/>
        <line num="324" count="0" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
        <line num="327" count="0" type="stmt"/>
      </file>
      <file name="flashcardsService.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\supabase\flashcardsService.ts">
        <metrics statements="283" coveredstatements="7" conditionals="186" coveredconditionals="0" methods="57" coveredmethods="1"/>
        <line num="2" count="2" type="stmt"/>
        <line num="6" count="42" type="stmt"/>
        <line num="11" count="2" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="76" count="2" type="stmt"/>
        <line num="77" count="2" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="98" count="0" type="cond" truecount="0" falsecount="10"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="112" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="119" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="123" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="124" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="147" count="0" type="cond" truecount="0" falsecount="8"/>
        <line num="148" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="196" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="200" count="0" type="cond" truecount="0" falsecount="10"/>
        <line num="203" count="0" type="stmt"/>
        <line num="206" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="207" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="210" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="212" count="2" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="215" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="216" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="219" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="224" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="231" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="232" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="235" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="239" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="242" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="243" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="251" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="252" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="254" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
        <line num="263" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="283" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="284" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="289" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="290" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="294" count="0" type="stmt"/>
        <line num="295" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="296" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="303" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="304" count="0" type="stmt"/>
        <line num="308" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="312" count="0" type="stmt"/>
        <line num="316" count="0" type="stmt"/>
        <line num="317" count="0" type="stmt"/>
        <line num="319" count="0" type="stmt"/>
        <line num="321" count="0" type="stmt"/>
        <line num="322" count="0" type="stmt"/>
        <line num="324" count="0" type="stmt"/>
        <line num="325" count="0" type="stmt"/>
        <line num="329" count="0" type="stmt"/>
        <line num="330" count="0" type="stmt"/>
        <line num="332" count="0" type="stmt"/>
        <line num="333" count="0" type="stmt"/>
        <line num="336" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="337" count="0" type="stmt"/>
        <line num="338" count="0" type="stmt"/>
        <line num="341" count="0" type="stmt"/>
        <line num="342" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="343" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="344" count="0" type="stmt"/>
        <line num="348" count="0" type="stmt"/>
        <line num="349" count="0" type="stmt"/>
        <line num="350" count="0" type="stmt"/>
        <line num="354" count="0" type="stmt"/>
        <line num="355" count="0" type="stmt"/>
        <line num="357" count="0" type="stmt"/>
        <line num="358" count="0" type="stmt"/>
        <line num="362" count="0" type="stmt"/>
        <line num="363" count="0" type="stmt"/>
        <line num="365" count="0" type="stmt"/>
        <line num="366" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="367" count="0" type="stmt"/>
        <line num="369" count="0" type="stmt"/>
        <line num="371" count="0" type="stmt"/>
        <line num="373" count="0" type="stmt"/>
        <line num="374" count="0" type="stmt"/>
        <line num="378" count="0" type="stmt"/>
        <line num="380" count="0" type="stmt"/>
        <line num="381" count="0" type="stmt"/>
        <line num="382" count="0" type="stmt"/>
        <line num="383" count="0" type="stmt"/>
        <line num="384" count="0" type="stmt"/>
        <line num="386" count="0" type="stmt"/>
        <line num="387" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="388" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="389" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="390" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="391" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="392" count="0" type="stmt"/>
        <line num="395" count="0" type="stmt"/>
        <line num="396" count="0" type="stmt"/>
        <line num="397" count="0" type="stmt"/>
        <line num="398" count="0" type="stmt"/>
        <line num="400" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="401" count="0" type="stmt"/>
        <line num="402" count="0" type="stmt"/>
        <line num="403" count="0" type="stmt"/>
        <line num="404" count="0" type="stmt"/>
        <line num="406" count="0" type="stmt"/>
        <line num="407" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="408" count="0" type="stmt"/>
        <line num="409" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="410" count="0" type="stmt"/>
        <line num="412" count="0" type="stmt"/>
        <line num="414" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="415" count="0" type="stmt"/>
        <line num="416" count="0" type="stmt"/>
        <line num="417" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="418" count="0" type="stmt"/>
        <line num="419" count="0" type="stmt"/>
        <line num="421" count="0" type="stmt"/>
        <line num="422" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="426" count="0" type="stmt"/>
        <line num="427" count="0" type="stmt"/>
        <line num="428" count="0" type="stmt"/>
        <line num="430" count="0" type="stmt"/>
        <line num="431" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="433" count="0" type="stmt"/>
        <line num="441" count="0" type="stmt"/>
        <line num="444" count="0" type="stmt"/>
        <line num="453" count="0" type="stmt"/>
        <line num="455" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="456" count="0" type="stmt"/>
        <line num="457" count="0" type="stmt"/>
        <line num="460" count="0" type="stmt"/>
        <line num="468" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="471" count="0" type="stmt"/>
        <line num="473" count="0" type="stmt"/>
        <line num="476" count="2" type="stmt"/>
        <line num="479" count="0" type="stmt"/>
        <line num="488" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="489" count="0" type="stmt"/>
        <line num="490" count="0" type="stmt"/>
        <line num="492" count="0" type="cond" truecount="0" falsecount="10"/>
        <line num="495" count="0" type="stmt"/>
        <line num="503" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="504" count="0" type="stmt"/>
        <line num="505" count="0" type="stmt"/>
        <line num="507" count="0" type="stmt"/>
        <line num="510" count="0" type="stmt"/>
        <line num="511" count="0" type="stmt"/>
        <line num="516" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="517" count="0" type="stmt"/>
        <line num="519" count="0" type="stmt"/>
        <line num="521" count="0" type="stmt"/>
        <line num="525" count="0" type="stmt"/>
        <line num="527" count="0" type="stmt"/>
        <line num="528" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="529" count="0" type="stmt"/>
        <line num="532" count="0" type="stmt"/>
        <line num="533" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="534" count="0" type="stmt"/>
        <line num="537" count="0" type="stmt"/>
        <line num="540" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="541" count="0" type="stmt"/>
        <line num="543" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="544" count="0" type="stmt"/>
        <line num="546" count="0" type="stmt"/>
        <line num="548" count="0" type="stmt"/>
        <line num="552" count="0" type="stmt"/>
        <line num="554" count="0" type="stmt"/>
        <line num="555" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="556" count="0" type="stmt"/>
        <line num="559" count="0" type="stmt"/>
        <line num="560" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="561" count="0" type="stmt"/>
        <line num="563" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="565" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="566" count="0" type="stmt"/>
        <line num="567" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="568" count="0" type="stmt"/>
        <line num="571" count="0" type="stmt"/>
        <line num="572" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="573" count="0" type="stmt"/>
        <line num="576" count="0" type="stmt"/>
        <line num="577" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="578" count="0" type="stmt"/>
        <line num="582" count="0" type="stmt"/>
        <line num="585" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="586" count="0" type="stmt"/>
        <line num="588" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="589" count="0" type="stmt"/>
        <line num="591" count="0" type="stmt"/>
        <line num="593" count="0" type="stmt"/>
      </file>
      <file name="resumenesService.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\supabase\resumenesService.ts">
        <metrics statements="100" coveredstatements="0" conditionals="46" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="15" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="62" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="98" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="102" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="128" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="174" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
      </file>
      <file name="server.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\supabase\server.ts">
        <metrics statements="6" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="20" count="0" type="stmt"/>
      </file>
      <file name="supabaseClient.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\supabase\supabaseClient.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
      <file name="testsService.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\supabase\testsService.ts">
        <metrics statements="99" coveredstatements="0" conditionals="132" coveredconditionals="0" methods="19" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="24" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="8"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="10"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="98" count="0" type="cond" truecount="0" falsecount="10"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="108" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="113" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="121" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="145" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="167" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="169" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="171" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="172" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="174" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="175" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="203" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="204" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="206" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="208" count="0" type="stmt"/>
        <line num="209" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="210" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="216" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="217" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="221" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="222" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="236" count="0" type="cond" truecount="0" falsecount="10"/>
        <line num="238" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
      </file>
      <file name="tokenUsageService.server.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\supabase\tokenUsageService.server.ts">
        <metrics statements="77" coveredstatements="0" conditionals="64" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="14" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="15" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="16" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="32" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="76" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="82" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="83" count="0" type="stmt"/>
        <line num="88" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="89" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="106" count="0" type="stmt"/>
        <line num="112" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="113" count="0" type="stmt"/>
        <line num="119" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="121" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="122" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="157" count="0" type="stmt"/>
        <line num="160" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="163" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="192" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
      </file>
      <file name="tokenUsageService.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\supabase\tokenUsageService.ts">
        <metrics statements="177" coveredstatements="0" conditionals="113" coveredconditionals="0" methods="14" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="13" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="14" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="15" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="62" count="0" type="stmt"/>
        <line num="71" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="72" count="0" type="stmt"/>
        <line num="76" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="77" count="0" type="stmt"/>
        <line num="82" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="83" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="97" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="102" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="106" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="128" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="129" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="139" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="140" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="170" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="189" count="0" type="stmt"/>
        <line num="196" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="197" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="198" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="199" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="224" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="239" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="241" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="242" count="0" type="stmt"/>
        <line num="248" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="249" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="258" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="259" count="0" type="stmt"/>
        <line num="261" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="262" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="283" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="284" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="287" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="288" count="0" type="stmt"/>
        <line num="290" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="291" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="292" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="293" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
        <line num="307" count="0" type="stmt"/>
        <line num="313" count="0" type="stmt"/>
        <line num="314" count="0" type="stmt"/>
        <line num="315" count="0" type="stmt"/>
        <line num="316" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="317" count="0" type="stmt"/>
        <line num="322" count="0" type="stmt"/>
        <line num="323" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="324" count="0" type="stmt"/>
        <line num="325" count="0" type="stmt"/>
        <line num="331" count="0" type="stmt"/>
        <line num="335" count="0" type="stmt"/>
        <line num="336" count="0" type="stmt"/>
        <line num="345" count="0" type="stmt"/>
        <line num="346" count="0" type="stmt"/>
        <line num="347" count="0" type="stmt"/>
        <line num="348" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="349" count="0" type="stmt"/>
        <line num="352" count="0" type="stmt"/>
        <line num="353" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="354" count="0" type="stmt"/>
        <line num="357" count="0" type="stmt"/>
        <line num="359" count="0" type="stmt"/>
        <line num="360" count="0" type="stmt"/>
        <line num="361" count="0" type="stmt"/>
        <line num="364" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="365" count="0" type="stmt"/>
        <line num="368" count="0" type="stmt"/>
        <line num="369" count="0" type="stmt"/>
        <line num="370" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="371" count="0" type="stmt"/>
        <line num="372" count="0" type="stmt"/>
        <line num="373" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="374" count="0" type="stmt"/>
        <line num="377" count="0" type="stmt"/>
        <line num="378" count="0" type="stmt"/>
        <line num="379" count="0" type="stmt"/>
        <line num="380" count="0" type="stmt"/>
        <line num="381" count="0" type="stmt"/>
        <line num="387" count="0" type="stmt"/>
        <line num="395" count="0" type="stmt"/>
        <line num="396" count="0" type="stmt"/>
        <line num="402" count="0" type="stmt"/>
        <line num="403" count="0" type="stmt"/>
        <line num="404" count="0" type="stmt"/>
        <line num="405" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="406" count="0" type="stmt"/>
        <line num="408" count="0" type="stmt"/>
        <line num="411" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="412" count="0" type="stmt"/>
        <line num="413" count="0" type="stmt"/>
        <line num="415" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="417" count="0" type="stmt"/>
        <line num="418" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.lib.utils">
      <metrics statements="304" coveredstatements="19" conditionals="292" coveredconditionals="10" methods="67" coveredmethods="7"/>
      <file name="dateUtils.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\utils\dateUtils.ts">
        <metrics statements="77" coveredstatements="0" conditionals="45" coveredconditionals="0" methods="18" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="56" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="61" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="65" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="66" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="74" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="82" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="98" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="99" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="111" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="115" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="140" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="142" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="145" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="178" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="179" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="184" count="0" type="stmt"/>
        <line num="189" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="194" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="195" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="211" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="212" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="225" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="226" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
      </file>
      <file name="emailTemplates.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\utils\emailTemplates.ts">
        <metrics statements="18" coveredstatements="0" conditionals="52" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="244" count="0" type="cond" truecount="0" falsecount="2"/>
      </file>
      <file name="planLimits.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\utils\planLimits.ts">
        <metrics statements="60" coveredstatements="12" conditionals="57" coveredconditionals="4" methods="18" coveredmethods="5"/>
        <line num="4" count="4" type="stmt"/>
        <line num="8" count="36" type="stmt"/>
        <line num="13" count="4" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="36" count="13" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="42" count="4" type="stmt"/>
        <line num="135" count="14" type="cond" truecount="1" falsecount="1"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="141" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="142" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="145" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="148" count="13" type="stmt"/>
        <line num="149" count="13" type="cond" truecount="1" falsecount="1"/>
        <line num="151" count="13" type="cond" truecount="2" falsecount="0"/>
        <line num="152" count="1" type="stmt"/>
        <line num="155" count="12" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="160" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="162" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="164" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="166" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="168" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="174" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="176" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="178" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="180" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="182" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="184" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="193" count="0" type="stmt"/>
        <line num="199" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="200" count="0" type="stmt"/>
        <line num="206" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="207" count="0" type="stmt"/>
        <line num="208" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="209" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="224" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="229" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="231" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
      </file>
      <file name="securityHelpers.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\utils\securityHelpers.ts">
        <metrics statements="89" coveredstatements="0" conditionals="75" coveredconditionals="0" methods="10" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="14" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="39" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="40" count="0" type="stmt"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="52" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="93" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="131" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="cond" truecount="0" falsecount="10"/>
        <line num="142" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="163" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="164" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="191" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="192" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="196" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="197" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="201" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="202" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="206" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="207" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="211" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="212" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="242" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="243" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="263" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="266" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="267" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="270" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="271" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="275" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="276" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="280" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="281" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
      </file>
      <file name="webhookLogger.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\lib\utils\webhookLogger.ts">
        <metrics statements="60" coveredstatements="7" conditionals="63" coveredconditionals="6" methods="15" coveredmethods="2"/>
        <line num="4" count="4" type="stmt"/>
        <line num="7" count="4" type="stmt"/>
        <line num="10" count="21" type="stmt"/>
        <line num="14" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="22" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="23" count="0" type="stmt"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="26" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="32" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="40" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="41" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="50" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="78" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="87" count="21" type="stmt"/>
        <line num="89" count="21" type="cond" truecount="2" falsecount="2"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="101" count="21" type="cond" truecount="2" falsecount="0"/>
        <line num="102" count="21" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="121" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="158" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="159" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="177" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="178" count="0" type="stmt"/>
        <line num="248" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="249" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.scripts">
      <metrics statements="149" coveredstatements="0" conditionals="67" coveredconditionals="0" methods="22" coveredmethods="0"/>
      <file name="validateSystem.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\scripts\validateSystem.ts">
        <metrics statements="149" coveredstatements="0" conditionals="67" coveredconditionals="0" methods="22" coveredmethods="0"/>
        <line num="4" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="5" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="33" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="128" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="140" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="150" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="186" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="188" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="195" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="199" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="210" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="214" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="228" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="229" count="0" type="stmt"/>
        <line num="231" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="232" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="244" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="245" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="248" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="251" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="257" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="258" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="275" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="287" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="288" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="290" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="291" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.utils">
      <metrics statements="45" coveredstatements="0" conditionals="30" coveredconditionals="0" methods="3" coveredmethods="0"/>
      <file name="markdownToHTML.ts" path="C:\Users\<USER>\Documents\augment-projects\OposI\v16\src\utils\markdownToHTML.ts">
        <metrics statements="45" coveredstatements="0" conditionals="30" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="13" count="0" type="stmt"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="26" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="35" count="0" type="stmt"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="41" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="50" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="51" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="54" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="76" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="82" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="83" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="93" count="0" type="stmt"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="96" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
