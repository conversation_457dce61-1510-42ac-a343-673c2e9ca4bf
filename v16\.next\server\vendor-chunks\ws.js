"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/ws";
exports.ids = ["vendor-chunks/ws"];
exports.modules = {

/***/ "(ssr)/./node_modules/ws/lib/buffer-util.js":
/*!********************************************!*\
  !*** ./node_modules/ws/lib/buffer-util.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst {\n  EMPTY_BUFFER\n} = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\nconst FastBuffer = Buffer[Symbol.species];\n\n/**\n * Merges an array of buffers into a new buffer.\n *\n * @param {Buffer[]} list The array of buffers to concat\n * @param {Number} totalLength The total length of buffers in the list\n * @return {Buffer} The resulting buffer\n * @public\n */\nfunction concat(list, totalLength) {\n  if (list.length === 0) return EMPTY_BUFFER;\n  if (list.length === 1) return list[0];\n  const target = Buffer.allocUnsafe(totalLength);\n  let offset = 0;\n  for (let i = 0; i < list.length; i++) {\n    const buf = list[i];\n    target.set(buf, offset);\n    offset += buf.length;\n  }\n  if (offset < totalLength) {\n    return new FastBuffer(target.buffer, target.byteOffset, offset);\n  }\n  return target;\n}\n\n/**\n * Masks a buffer using the given mask.\n *\n * @param {Buffer} source The buffer to mask\n * @param {Buffer} mask The mask to use\n * @param {Buffer} output The buffer where to store the result\n * @param {Number} offset The offset at which to start writing\n * @param {Number} length The number of bytes to mask.\n * @public\n */\nfunction _mask(source, mask, output, offset, length) {\n  for (let i = 0; i < length; i++) {\n    output[offset + i] = source[i] ^ mask[i & 3];\n  }\n}\n\n/**\n * Unmasks a buffer using the given mask.\n *\n * @param {Buffer} buffer The buffer to unmask\n * @param {Buffer} mask The mask to use\n * @public\n */\nfunction _unmask(buffer, mask) {\n  for (let i = 0; i < buffer.length; i++) {\n    buffer[i] ^= mask[i & 3];\n  }\n}\n\n/**\n * Converts a buffer to an `ArrayBuffer`.\n *\n * @param {Buffer} buf The buffer to convert\n * @return {ArrayBuffer} Converted buffer\n * @public\n */\nfunction toArrayBuffer(buf) {\n  if (buf.length === buf.buffer.byteLength) {\n    return buf.buffer;\n  }\n  return buf.buffer.slice(buf.byteOffset, buf.byteOffset + buf.length);\n}\n\n/**\n * Converts `data` to a `Buffer`.\n *\n * @param {*} data The data to convert\n * @return {Buffer} The buffer\n * @throws {TypeError}\n * @public\n */\nfunction toBuffer(data) {\n  toBuffer.readOnly = true;\n  if (Buffer.isBuffer(data)) return data;\n  let buf;\n  if (data instanceof ArrayBuffer) {\n    buf = new FastBuffer(data);\n  } else if (ArrayBuffer.isView(data)) {\n    buf = new FastBuffer(data.buffer, data.byteOffset, data.byteLength);\n  } else {\n    buf = Buffer.from(data);\n    toBuffer.readOnly = false;\n  }\n  return buf;\n}\nmodule.exports = {\n  concat,\n  mask: _mask,\n  toArrayBuffer,\n  toBuffer,\n  unmask: _unmask\n};\n\n/* istanbul ignore else  */\nif (!process.env.WS_NO_BUFFER_UTIL) {\n  try {\n    const bufferUtil = __webpack_require__(/*! bufferutil */ \"?32c4\");\n    module.exports.mask = function (source, mask, output, offset, length) {\n      if (length < 48) _mask(source, mask, output, offset, length);else bufferUtil.mask(source, mask, output, offset, length);\n    };\n    module.exports.unmask = function (buffer, mask) {\n      if (buffer.length < 32) _unmask(buffer, mask);else bufferUtil.unmask(buffer, mask);\n    };\n  } catch (e) {\n    // Continue regardless of the error.\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/buffer-util.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/constants.js":
/*!******************************************!*\
  !*** ./node_modules/ws/lib/constants.js ***!
  \******************************************/
/***/ ((module) => {

eval("\n\nconst BINARY_TYPES = ['nodebuffer', 'arraybuffer', 'fragments'];\nconst hasBlob = typeof Blob !== 'undefined';\nif (hasBlob) BINARY_TYPES.push('blob');\nmodule.exports = {\n  BINARY_TYPES,\n  EMPTY_BUFFER: Buffer.alloc(0),\n  GUID: '258EAFA5-E914-47DA-95CA-C5AB0DC85B11',\n  hasBlob,\n  kForOnEventAttribute: Symbol('kIsForOnEventAttribute'),\n  kListener: Symbol('kListener'),\n  kStatusCode: Symbol('status-code'),\n  kWebSocket: Symbol('websocket'),\n  NOOP: () => {}\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd3MvbGliL2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYixNQUFNQSxZQUFZLEdBQUcsQ0FBQyxZQUFZLEVBQUUsYUFBYSxFQUFFLFdBQVcsQ0FBQztBQUMvRCxNQUFNQyxPQUFPLEdBQUcsT0FBT0MsSUFBSSxLQUFLLFdBQVc7QUFFM0MsSUFBSUQsT0FBTyxFQUFFRCxZQUFZLENBQUNHLElBQUksQ0FBQyxNQUFNLENBQUM7QUFFdENDLE1BQU0sQ0FBQ0MsT0FBTyxHQUFHO0VBQ2ZMLFlBQVk7RUFDWk0sWUFBWSxFQUFFQyxNQUFNLENBQUNDLEtBQUssQ0FBQyxDQUFDLENBQUM7RUFDN0JDLElBQUksRUFBRSxzQ0FBc0M7RUFDNUNSLE9BQU87RUFDUFMsb0JBQW9CLEVBQUVDLE1BQU0sQ0FBQyx3QkFBd0IsQ0FBQztFQUN0REMsU0FBUyxFQUFFRCxNQUFNLENBQUMsV0FBVyxDQUFDO0VBQzlCRSxXQUFXLEVBQUVGLE1BQU0sQ0FBQyxhQUFhLENBQUM7RUFDbENHLFVBQVUsRUFBRUgsTUFBTSxDQUFDLFdBQVcsQ0FBQztFQUMvQkksSUFBSSxFQUFFQSxDQUFBLEtBQU0sQ0FBQztBQUNmLENBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbmFhdGFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcT3Bvc0lcXHYxNlxcbm9kZV9tb2R1bGVzXFx3c1xcbGliXFxjb25zdGFudHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5jb25zdCBCSU5BUllfVFlQRVMgPSBbJ25vZGVidWZmZXInLCAnYXJyYXlidWZmZXInLCAnZnJhZ21lbnRzJ107XG5jb25zdCBoYXNCbG9iID0gdHlwZW9mIEJsb2IgIT09ICd1bmRlZmluZWQnO1xuXG5pZiAoaGFzQmxvYikgQklOQVJZX1RZUEVTLnB1c2goJ2Jsb2InKTtcblxubW9kdWxlLmV4cG9ydHMgPSB7XG4gIEJJTkFSWV9UWVBFUyxcbiAgRU1QVFlfQlVGRkVSOiBCdWZmZXIuYWxsb2MoMCksXG4gIEdVSUQ6ICcyNThFQUZBNS1FOTE0LTQ3REEtOTVDQS1DNUFCMERDODVCMTEnLFxuICBoYXNCbG9iLFxuICBrRm9yT25FdmVudEF0dHJpYnV0ZTogU3ltYm9sKCdrSXNGb3JPbkV2ZW50QXR0cmlidXRlJyksXG4gIGtMaXN0ZW5lcjogU3ltYm9sKCdrTGlzdGVuZXInKSxcbiAga1N0YXR1c0NvZGU6IFN5bWJvbCgnc3RhdHVzLWNvZGUnKSxcbiAga1dlYlNvY2tldDogU3ltYm9sKCd3ZWJzb2NrZXQnKSxcbiAgTk9PUDogKCkgPT4ge31cbn07XG4iXSwibmFtZXMiOlsiQklOQVJZX1RZUEVTIiwiaGFzQmxvYiIsIkJsb2IiLCJwdXNoIiwibW9kdWxlIiwiZXhwb3J0cyIsIkVNUFRZX0JVRkZFUiIsIkJ1ZmZlciIsImFsbG9jIiwiR1VJRCIsImtGb3JPbkV2ZW50QXR0cmlidXRlIiwiU3ltYm9sIiwia0xpc3RlbmVyIiwia1N0YXR1c0NvZGUiLCJrV2ViU29ja2V0IiwiTk9PUCJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/event-target.js":
/*!*********************************************!*\
  !*** ./node_modules/ws/lib/event-target.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst {\n  kForOnEventAttribute,\n  kListener\n} = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\nconst kCode = Symbol('kCode');\nconst kData = Symbol('kData');\nconst kError = Symbol('kError');\nconst kMessage = Symbol('kMessage');\nconst kReason = Symbol('kReason');\nconst kTarget = Symbol('kTarget');\nconst kType = Symbol('kType');\nconst kWasClean = Symbol('kWasClean');\n\n/**\n * Class representing an event.\n */\nclass Event {\n  /**\n   * Create a new `Event`.\n   *\n   * @param {String} type The name of the event\n   * @throws {TypeError} If the `type` argument is not specified\n   */\n  constructor(type) {\n    this[kTarget] = null;\n    this[kType] = type;\n  }\n\n  /**\n   * @type {*}\n   */\n  get target() {\n    return this[kTarget];\n  }\n\n  /**\n   * @type {String}\n   */\n  get type() {\n    return this[kType];\n  }\n}\nObject.defineProperty(Event.prototype, 'target', {\n  enumerable: true\n});\nObject.defineProperty(Event.prototype, 'type', {\n  enumerable: true\n});\n\n/**\n * Class representing a close event.\n *\n * @extends Event\n */\nclass CloseEvent extends Event {\n  /**\n   * Create a new `CloseEvent`.\n   *\n   * @param {String} type The name of the event\n   * @param {Object} [options] A dictionary object that allows for setting\n   *     attributes via object members of the same name\n   * @param {Number} [options.code=0] The status code explaining why the\n   *     connection was closed\n   * @param {String} [options.reason=''] A human-readable string explaining why\n   *     the connection was closed\n   * @param {Boolean} [options.wasClean=false] Indicates whether or not the\n   *     connection was cleanly closed\n   */\n  constructor(type, options = {}) {\n    super(type);\n    this[kCode] = options.code === undefined ? 0 : options.code;\n    this[kReason] = options.reason === undefined ? '' : options.reason;\n    this[kWasClean] = options.wasClean === undefined ? false : options.wasClean;\n  }\n\n  /**\n   * @type {Number}\n   */\n  get code() {\n    return this[kCode];\n  }\n\n  /**\n   * @type {String}\n   */\n  get reason() {\n    return this[kReason];\n  }\n\n  /**\n   * @type {Boolean}\n   */\n  get wasClean() {\n    return this[kWasClean];\n  }\n}\nObject.defineProperty(CloseEvent.prototype, 'code', {\n  enumerable: true\n});\nObject.defineProperty(CloseEvent.prototype, 'reason', {\n  enumerable: true\n});\nObject.defineProperty(CloseEvent.prototype, 'wasClean', {\n  enumerable: true\n});\n\n/**\n * Class representing an error event.\n *\n * @extends Event\n */\nclass ErrorEvent extends Event {\n  /**\n   * Create a new `ErrorEvent`.\n   *\n   * @param {String} type The name of the event\n   * @param {Object} [options] A dictionary object that allows for setting\n   *     attributes via object members of the same name\n   * @param {*} [options.error=null] The error that generated this event\n   * @param {String} [options.message=''] The error message\n   */\n  constructor(type, options = {}) {\n    super(type);\n    this[kError] = options.error === undefined ? null : options.error;\n    this[kMessage] = options.message === undefined ? '' : options.message;\n  }\n\n  /**\n   * @type {*}\n   */\n  get error() {\n    return this[kError];\n  }\n\n  /**\n   * @type {String}\n   */\n  get message() {\n    return this[kMessage];\n  }\n}\nObject.defineProperty(ErrorEvent.prototype, 'error', {\n  enumerable: true\n});\nObject.defineProperty(ErrorEvent.prototype, 'message', {\n  enumerable: true\n});\n\n/**\n * Class representing a message event.\n *\n * @extends Event\n */\nclass MessageEvent extends Event {\n  /**\n   * Create a new `MessageEvent`.\n   *\n   * @param {String} type The name of the event\n   * @param {Object} [options] A dictionary object that allows for setting\n   *     attributes via object members of the same name\n   * @param {*} [options.data=null] The message content\n   */\n  constructor(type, options = {}) {\n    super(type);\n    this[kData] = options.data === undefined ? null : options.data;\n  }\n\n  /**\n   * @type {*}\n   */\n  get data() {\n    return this[kData];\n  }\n}\nObject.defineProperty(MessageEvent.prototype, 'data', {\n  enumerable: true\n});\n\n/**\n * This provides methods for emulating the `EventTarget` interface. It's not\n * meant to be used directly.\n *\n * @mixin\n */\nconst EventTarget = {\n  /**\n   * Register an event listener.\n   *\n   * @param {String} type A string representing the event type to listen for\n   * @param {(Function|Object)} handler The listener to add\n   * @param {Object} [options] An options object specifies characteristics about\n   *     the event listener\n   * @param {Boolean} [options.once=false] A `Boolean` indicating that the\n   *     listener should be invoked at most once after being added. If `true`,\n   *     the listener would be automatically removed when invoked.\n   * @public\n   */\n  addEventListener(type, handler, options = {}) {\n    for (const listener of this.listeners(type)) {\n      if (!options[kForOnEventAttribute] && listener[kListener] === handler && !listener[kForOnEventAttribute]) {\n        return;\n      }\n    }\n    let wrapper;\n    if (type === 'message') {\n      wrapper = function onMessage(data, isBinary) {\n        const event = new MessageEvent('message', {\n          data: isBinary ? data : data.toString()\n        });\n        event[kTarget] = this;\n        callListener(handler, this, event);\n      };\n    } else if (type === 'close') {\n      wrapper = function onClose(code, message) {\n        const event = new CloseEvent('close', {\n          code,\n          reason: message.toString(),\n          wasClean: this._closeFrameReceived && this._closeFrameSent\n        });\n        event[kTarget] = this;\n        callListener(handler, this, event);\n      };\n    } else if (type === 'error') {\n      wrapper = function onError(error) {\n        const event = new ErrorEvent('error', {\n          error,\n          message: error.message\n        });\n        event[kTarget] = this;\n        callListener(handler, this, event);\n      };\n    } else if (type === 'open') {\n      wrapper = function onOpen() {\n        const event = new Event('open');\n        event[kTarget] = this;\n        callListener(handler, this, event);\n      };\n    } else {\n      return;\n    }\n    wrapper[kForOnEventAttribute] = !!options[kForOnEventAttribute];\n    wrapper[kListener] = handler;\n    if (options.once) {\n      this.once(type, wrapper);\n    } else {\n      this.on(type, wrapper);\n    }\n  },\n  /**\n   * Remove an event listener.\n   *\n   * @param {String} type A string representing the event type to remove\n   * @param {(Function|Object)} handler The listener to remove\n   * @public\n   */\n  removeEventListener(type, handler) {\n    for (const listener of this.listeners(type)) {\n      if (listener[kListener] === handler && !listener[kForOnEventAttribute]) {\n        this.removeListener(type, listener);\n        break;\n      }\n    }\n  }\n};\nmodule.exports = {\n  CloseEvent,\n  ErrorEvent,\n  Event,\n  EventTarget,\n  MessageEvent\n};\n\n/**\n * Call an event listener\n *\n * @param {(Function|Object)} listener The listener to call\n * @param {*} thisArg The value to use as `this`` when calling the listener\n * @param {Event} event The event to pass to the listener\n * @private\n */\nfunction callListener(listener, thisArg, event) {\n  if (typeof listener === 'object' && listener.handleEvent) {\n    listener.handleEvent.call(listener, event);\n  } else {\n    listener.call(thisArg, event);\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/event-target.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/extension.js":
/*!******************************************!*\
  !*** ./node_modules/ws/lib/extension.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst {\n  tokenChars\n} = __webpack_require__(/*! ./validation */ \"(ssr)/./node_modules/ws/lib/validation.js\");\n\n/**\n * Adds an offer to the map of extension offers or a parameter to the map of\n * parameters.\n *\n * @param {Object} dest The map of extension offers or parameters\n * @param {String} name The extension or parameter name\n * @param {(Object|Boolean|String)} elem The extension parameters or the\n *     parameter value\n * @private\n */\nfunction push(dest, name, elem) {\n  if (dest[name] === undefined) dest[name] = [elem];else dest[name].push(elem);\n}\n\n/**\n * Parses the `Sec-WebSocket-Extensions` header into an object.\n *\n * @param {String} header The field value of the header\n * @return {Object} The parsed object\n * @public\n */\nfunction parse(header) {\n  const offers = Object.create(null);\n  let params = Object.create(null);\n  let mustUnescape = false;\n  let isEscaping = false;\n  let inQuotes = false;\n  let extensionName;\n  let paramName;\n  let start = -1;\n  let code = -1;\n  let end = -1;\n  let i = 0;\n  for (; i < header.length; i++) {\n    code = header.charCodeAt(i);\n    if (extensionName === undefined) {\n      if (end === -1 && tokenChars[code] === 1) {\n        if (start === -1) start = i;\n      } else if (i !== 0 && (code === 0x20 /* ' ' */ || code === 0x09) /* '\\t' */) {\n        if (end === -1 && start !== -1) end = i;\n      } else if (code === 0x3b /* ';' */ || code === 0x2c /* ',' */) {\n        if (start === -1) {\n          throw new SyntaxError(`Unexpected character at index ${i}`);\n        }\n        if (end === -1) end = i;\n        const name = header.slice(start, end);\n        if (code === 0x2c) {\n          push(offers, name, params);\n          params = Object.create(null);\n        } else {\n          extensionName = name;\n        }\n        start = end = -1;\n      } else {\n        throw new SyntaxError(`Unexpected character at index ${i}`);\n      }\n    } else if (paramName === undefined) {\n      if (end === -1 && tokenChars[code] === 1) {\n        if (start === -1) start = i;\n      } else if (code === 0x20 || code === 0x09) {\n        if (end === -1 && start !== -1) end = i;\n      } else if (code === 0x3b || code === 0x2c) {\n        if (start === -1) {\n          throw new SyntaxError(`Unexpected character at index ${i}`);\n        }\n        if (end === -1) end = i;\n        push(params, header.slice(start, end), true);\n        if (code === 0x2c) {\n          push(offers, extensionName, params);\n          params = Object.create(null);\n          extensionName = undefined;\n        }\n        start = end = -1;\n      } else if (code === 0x3d /* '=' */ && start !== -1 && end === -1) {\n        paramName = header.slice(start, i);\n        start = end = -1;\n      } else {\n        throw new SyntaxError(`Unexpected character at index ${i}`);\n      }\n    } else {\n      //\n      // The value of a quoted-string after unescaping must conform to the\n      // token ABNF, so only token characters are valid.\n      // Ref: https://tools.ietf.org/html/rfc6455#section-9.1\n      //\n      if (isEscaping) {\n        if (tokenChars[code] !== 1) {\n          throw new SyntaxError(`Unexpected character at index ${i}`);\n        }\n        if (start === -1) start = i;else if (!mustUnescape) mustUnescape = true;\n        isEscaping = false;\n      } else if (inQuotes) {\n        if (tokenChars[code] === 1) {\n          if (start === -1) start = i;\n        } else if (code === 0x22 /* '\"' */ && start !== -1) {\n          inQuotes = false;\n          end = i;\n        } else if (code === 0x5c /* '\\' */) {\n          isEscaping = true;\n        } else {\n          throw new SyntaxError(`Unexpected character at index ${i}`);\n        }\n      } else if (code === 0x22 && header.charCodeAt(i - 1) === 0x3d) {\n        inQuotes = true;\n      } else if (end === -1 && tokenChars[code] === 1) {\n        if (start === -1) start = i;\n      } else if (start !== -1 && (code === 0x20 || code === 0x09)) {\n        if (end === -1) end = i;\n      } else if (code === 0x3b || code === 0x2c) {\n        if (start === -1) {\n          throw new SyntaxError(`Unexpected character at index ${i}`);\n        }\n        if (end === -1) end = i;\n        let value = header.slice(start, end);\n        if (mustUnescape) {\n          value = value.replace(/\\\\/g, '');\n          mustUnescape = false;\n        }\n        push(params, paramName, value);\n        if (code === 0x2c) {\n          push(offers, extensionName, params);\n          params = Object.create(null);\n          extensionName = undefined;\n        }\n        paramName = undefined;\n        start = end = -1;\n      } else {\n        throw new SyntaxError(`Unexpected character at index ${i}`);\n      }\n    }\n  }\n  if (start === -1 || inQuotes || code === 0x20 || code === 0x09) {\n    throw new SyntaxError('Unexpected end of input');\n  }\n  if (end === -1) end = i;\n  const token = header.slice(start, end);\n  if (extensionName === undefined) {\n    push(offers, token, params);\n  } else {\n    if (paramName === undefined) {\n      push(params, token, true);\n    } else if (mustUnescape) {\n      push(params, paramName, token.replace(/\\\\/g, ''));\n    } else {\n      push(params, paramName, token);\n    }\n    push(offers, extensionName, params);\n  }\n  return offers;\n}\n\n/**\n * Builds the `Sec-WebSocket-Extensions` header field value.\n *\n * @param {Object} extensions The map of extensions and parameters to format\n * @return {String} A string representing the given object\n * @public\n */\nfunction format(extensions) {\n  return Object.keys(extensions).map(extension => {\n    let configurations = extensions[extension];\n    if (!Array.isArray(configurations)) configurations = [configurations];\n    return configurations.map(params => {\n      return [extension].concat(Object.keys(params).map(k => {\n        let values = params[k];\n        if (!Array.isArray(values)) values = [values];\n        return values.map(v => v === true ? k : `${k}=${v}`).join('; ');\n      })).join('; ');\n    }).join(', ');\n  }).join(', ');\n}\nmodule.exports = {\n  format,\n  parse\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/extension.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/limiter.js":
/*!****************************************!*\
  !*** ./node_modules/ws/lib/limiter.js ***!
  \****************************************/
/***/ ((module) => {

eval("\n\nconst kDone = Symbol('kDone');\nconst kRun = Symbol('kRun');\n\n/**\n * A very simple job queue with adjustable concurrency. Adapted from\n * https://github.com/STRML/async-limiter\n */\nclass Limiter {\n  /**\n   * Creates a new `Limiter`.\n   *\n   * @param {Number} [concurrency=Infinity] The maximum number of jobs allowed\n   *     to run concurrently\n   */\n  constructor(concurrency) {\n    this[kDone] = () => {\n      this.pending--;\n      this[kRun]();\n    };\n    this.concurrency = concurrency || Infinity;\n    this.jobs = [];\n    this.pending = 0;\n  }\n\n  /**\n   * Adds a job to the queue.\n   *\n   * @param {Function} job The job to run\n   * @public\n   */\n  add(job) {\n    this.jobs.push(job);\n    this[kRun]();\n  }\n\n  /**\n   * Removes a job from the queue and runs it if possible.\n   *\n   * @private\n   */\n  [kRun]() {\n    if (this.pending === this.concurrency) return;\n    if (this.jobs.length) {\n      const job = this.jobs.shift();\n      this.pending++;\n      job(this[kDone]);\n    }\n  }\n}\nmodule.exports = Limiter;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/limiter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/permessage-deflate.js":
/*!***************************************************!*\
  !*** ./node_modules/ws/lib/permessage-deflate.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return typeof key === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (typeof input !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (typeof res !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nconst zlib = __webpack_require__(/*! zlib */ \"zlib\");\nconst bufferUtil = __webpack_require__(/*! ./buffer-util */ \"(ssr)/./node_modules/ws/lib/buffer-util.js\");\nconst Limiter = __webpack_require__(/*! ./limiter */ \"(ssr)/./node_modules/ws/lib/limiter.js\");\nconst {\n  kStatusCode\n} = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\nconst FastBuffer = Buffer[Symbol.species];\nconst TRAILER = Buffer.from([0x00, 0x00, 0xff, 0xff]);\nconst kPerMessageDeflate = Symbol('permessage-deflate');\nconst kTotalLength = Symbol('total-length');\nconst kCallback = Symbol('callback');\nconst kBuffers = Symbol('buffers');\nconst kError = Symbol('error');\n\n//\n// We limit zlib concurrency, which prevents severe memory fragmentation\n// as documented in https://github.com/nodejs/node/issues/8871#issuecomment-250915913\n// and https://github.com/websockets/ws/issues/1202\n//\n// Intentionally global; it's the global thread pool that's an issue.\n//\nlet zlibLimiter;\n\n/**\n * permessage-deflate implementation.\n */\nclass PerMessageDeflate {\n  /**\n   * Creates a PerMessageDeflate instance.\n   *\n   * @param {Object} [options] Configuration options\n   * @param {(Boolean|Number)} [options.clientMaxWindowBits] Advertise support\n   *     for, or request, a custom client window size\n   * @param {Boolean} [options.clientNoContextTakeover=false] Advertise/\n   *     acknowledge disabling of client context takeover\n   * @param {Number} [options.concurrencyLimit=10] The number of concurrent\n   *     calls to zlib\n   * @param {(Boolean|Number)} [options.serverMaxWindowBits] Request/confirm the\n   *     use of a custom server window size\n   * @param {Boolean} [options.serverNoContextTakeover=false] Request/accept\n   *     disabling of server context takeover\n   * @param {Number} [options.threshold=1024] Size (in bytes) below which\n   *     messages should not be compressed if context takeover is disabled\n   * @param {Object} [options.zlibDeflateOptions] Options to pass to zlib on\n   *     deflate\n   * @param {Object} [options.zlibInflateOptions] Options to pass to zlib on\n   *     inflate\n   * @param {Boolean} [isServer=false] Create the instance in either server or\n   *     client mode\n   * @param {Number} [maxPayload=0] The maximum allowed message length\n   */\n  constructor(options, isServer, maxPayload) {\n    this._maxPayload = maxPayload | 0;\n    this._options = options || {};\n    this._threshold = this._options.threshold !== undefined ? this._options.threshold : 1024;\n    this._isServer = !!isServer;\n    this._deflate = null;\n    this._inflate = null;\n    this.params = null;\n    if (!zlibLimiter) {\n      const concurrency = this._options.concurrencyLimit !== undefined ? this._options.concurrencyLimit : 10;\n      zlibLimiter = new Limiter(concurrency);\n    }\n  }\n\n  /**\n   * @type {String}\n   */\n  static get extensionName() {\n    return 'permessage-deflate';\n  }\n\n  /**\n   * Create an extension negotiation offer.\n   *\n   * @return {Object} Extension parameters\n   * @public\n   */\n  offer() {\n    const params = {};\n    if (this._options.serverNoContextTakeover) {\n      params.server_no_context_takeover = true;\n    }\n    if (this._options.clientNoContextTakeover) {\n      params.client_no_context_takeover = true;\n    }\n    if (this._options.serverMaxWindowBits) {\n      params.server_max_window_bits = this._options.serverMaxWindowBits;\n    }\n    if (this._options.clientMaxWindowBits) {\n      params.client_max_window_bits = this._options.clientMaxWindowBits;\n    } else if (this._options.clientMaxWindowBits == null) {\n      params.client_max_window_bits = true;\n    }\n    return params;\n  }\n\n  /**\n   * Accept an extension negotiation offer/response.\n   *\n   * @param {Array} configurations The extension negotiation offers/reponse\n   * @return {Object} Accepted configuration\n   * @public\n   */\n  accept(configurations) {\n    configurations = this.normalizeParams(configurations);\n    this.params = this._isServer ? this.acceptAsServer(configurations) : this.acceptAsClient(configurations);\n    return this.params;\n  }\n\n  /**\n   * Releases all resources used by the extension.\n   *\n   * @public\n   */\n  cleanup() {\n    if (this._inflate) {\n      this._inflate.close();\n      this._inflate = null;\n    }\n    if (this._deflate) {\n      const callback = this._deflate[kCallback];\n      this._deflate.close();\n      this._deflate = null;\n      if (callback) {\n        callback(new Error('The deflate stream was closed while data was being processed'));\n      }\n    }\n  }\n\n  /**\n   *  Accept an extension negotiation offer.\n   *\n   * @param {Array} offers The extension negotiation offers\n   * @return {Object} Accepted configuration\n   * @private\n   */\n  acceptAsServer(offers) {\n    const opts = this._options;\n    const accepted = offers.find(params => {\n      if (opts.serverNoContextTakeover === false && params.server_no_context_takeover || params.server_max_window_bits && (opts.serverMaxWindowBits === false || typeof opts.serverMaxWindowBits === 'number' && opts.serverMaxWindowBits > params.server_max_window_bits) || typeof opts.clientMaxWindowBits === 'number' && !params.client_max_window_bits) {\n        return false;\n      }\n      return true;\n    });\n    if (!accepted) {\n      throw new Error('None of the extension offers can be accepted');\n    }\n    if (opts.serverNoContextTakeover) {\n      accepted.server_no_context_takeover = true;\n    }\n    if (opts.clientNoContextTakeover) {\n      accepted.client_no_context_takeover = true;\n    }\n    if (typeof opts.serverMaxWindowBits === 'number') {\n      accepted.server_max_window_bits = opts.serverMaxWindowBits;\n    }\n    if (typeof opts.clientMaxWindowBits === 'number') {\n      accepted.client_max_window_bits = opts.clientMaxWindowBits;\n    } else if (accepted.client_max_window_bits === true || opts.clientMaxWindowBits === false) {\n      delete accepted.client_max_window_bits;\n    }\n    return accepted;\n  }\n\n  /**\n   * Accept the extension negotiation response.\n   *\n   * @param {Array} response The extension negotiation response\n   * @return {Object} Accepted configuration\n   * @private\n   */\n  acceptAsClient(response) {\n    const params = response[0];\n    if (this._options.clientNoContextTakeover === false && params.client_no_context_takeover) {\n      throw new Error('Unexpected parameter \"client_no_context_takeover\"');\n    }\n    if (!params.client_max_window_bits) {\n      if (typeof this._options.clientMaxWindowBits === 'number') {\n        params.client_max_window_bits = this._options.clientMaxWindowBits;\n      }\n    } else if (this._options.clientMaxWindowBits === false || typeof this._options.clientMaxWindowBits === 'number' && params.client_max_window_bits > this._options.clientMaxWindowBits) {\n      throw new Error('Unexpected or invalid parameter \"client_max_window_bits\"');\n    }\n    return params;\n  }\n\n  /**\n   * Normalize parameters.\n   *\n   * @param {Array} configurations The extension negotiation offers/reponse\n   * @return {Array} The offers/response with normalized parameters\n   * @private\n   */\n  normalizeParams(configurations) {\n    configurations.forEach(params => {\n      Object.keys(params).forEach(key => {\n        let value = params[key];\n        if (value.length > 1) {\n          throw new Error(`Parameter \"${key}\" must have only a single value`);\n        }\n        value = value[0];\n        if (key === 'client_max_window_bits') {\n          if (value !== true) {\n            const num = +value;\n            if (!Number.isInteger(num) || num < 8 || num > 15) {\n              throw new TypeError(`Invalid value for parameter \"${key}\": ${value}`);\n            }\n            value = num;\n          } else if (!this._isServer) {\n            throw new TypeError(`Invalid value for parameter \"${key}\": ${value}`);\n          }\n        } else if (key === 'server_max_window_bits') {\n          const num = +value;\n          if (!Number.isInteger(num) || num < 8 || num > 15) {\n            throw new TypeError(`Invalid value for parameter \"${key}\": ${value}`);\n          }\n          value = num;\n        } else if (key === 'client_no_context_takeover' || key === 'server_no_context_takeover') {\n          if (value !== true) {\n            throw new TypeError(`Invalid value for parameter \"${key}\": ${value}`);\n          }\n        } else {\n          throw new Error(`Unknown parameter \"${key}\"`);\n        }\n        params[key] = value;\n      });\n    });\n    return configurations;\n  }\n\n  /**\n   * Decompress data. Concurrency limited.\n   *\n   * @param {Buffer} data Compressed data\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @public\n   */\n  decompress(data, fin, callback) {\n    zlibLimiter.add(done => {\n      this._decompress(data, fin, (err, result) => {\n        done();\n        callback(err, result);\n      });\n    });\n  }\n\n  /**\n   * Compress data. Concurrency limited.\n   *\n   * @param {(Buffer|String)} data Data to compress\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @public\n   */\n  compress(data, fin, callback) {\n    zlibLimiter.add(done => {\n      this._compress(data, fin, (err, result) => {\n        done();\n        callback(err, result);\n      });\n    });\n  }\n\n  /**\n   * Decompress data.\n   *\n   * @param {Buffer} data Compressed data\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @private\n   */\n  _decompress(data, fin, callback) {\n    const endpoint = this._isServer ? 'client' : 'server';\n    if (!this._inflate) {\n      const key = `${endpoint}_max_window_bits`;\n      const windowBits = typeof this.params[key] !== 'number' ? zlib.Z_DEFAULT_WINDOWBITS : this.params[key];\n      this._inflate = zlib.createInflateRaw(_objectSpread(_objectSpread({}, this._options.zlibInflateOptions), {}, {\n        windowBits\n      }));\n      this._inflate[kPerMessageDeflate] = this;\n      this._inflate[kTotalLength] = 0;\n      this._inflate[kBuffers] = [];\n      this._inflate.on('error', inflateOnError);\n      this._inflate.on('data', inflateOnData);\n    }\n    this._inflate[kCallback] = callback;\n    this._inflate.write(data);\n    if (fin) this._inflate.write(TRAILER);\n    this._inflate.flush(() => {\n      const err = this._inflate[kError];\n      if (err) {\n        this._inflate.close();\n        this._inflate = null;\n        callback(err);\n        return;\n      }\n      const data = bufferUtil.concat(this._inflate[kBuffers], this._inflate[kTotalLength]);\n      if (this._inflate._readableState.endEmitted) {\n        this._inflate.close();\n        this._inflate = null;\n      } else {\n        this._inflate[kTotalLength] = 0;\n        this._inflate[kBuffers] = [];\n        if (fin && this.params[`${endpoint}_no_context_takeover`]) {\n          this._inflate.reset();\n        }\n      }\n      callback(null, data);\n    });\n  }\n\n  /**\n   * Compress data.\n   *\n   * @param {(Buffer|String)} data Data to compress\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @private\n   */\n  _compress(data, fin, callback) {\n    const endpoint = this._isServer ? 'server' : 'client';\n    if (!this._deflate) {\n      const key = `${endpoint}_max_window_bits`;\n      const windowBits = typeof this.params[key] !== 'number' ? zlib.Z_DEFAULT_WINDOWBITS : this.params[key];\n      this._deflate = zlib.createDeflateRaw(_objectSpread(_objectSpread({}, this._options.zlibDeflateOptions), {}, {\n        windowBits\n      }));\n      this._deflate[kTotalLength] = 0;\n      this._deflate[kBuffers] = [];\n      this._deflate.on('data', deflateOnData);\n    }\n    this._deflate[kCallback] = callback;\n    this._deflate.write(data);\n    this._deflate.flush(zlib.Z_SYNC_FLUSH, () => {\n      if (!this._deflate) {\n        //\n        // The deflate stream was closed while data was being processed.\n        //\n        return;\n      }\n      let data = bufferUtil.concat(this._deflate[kBuffers], this._deflate[kTotalLength]);\n      if (fin) {\n        data = new FastBuffer(data.buffer, data.byteOffset, data.length - 4);\n      }\n\n      //\n      // Ensure that the callback will not be called again in\n      // `PerMessageDeflate#cleanup()`.\n      //\n      this._deflate[kCallback] = null;\n      this._deflate[kTotalLength] = 0;\n      this._deflate[kBuffers] = [];\n      if (fin && this.params[`${endpoint}_no_context_takeover`]) {\n        this._deflate.reset();\n      }\n      callback(null, data);\n    });\n  }\n}\nmodule.exports = PerMessageDeflate;\n\n/**\n * The listener of the `zlib.DeflateRaw` stream `'data'` event.\n *\n * @param {Buffer} chunk A chunk of data\n * @private\n */\nfunction deflateOnData(chunk) {\n  this[kBuffers].push(chunk);\n  this[kTotalLength] += chunk.length;\n}\n\n/**\n * The listener of the `zlib.InflateRaw` stream `'data'` event.\n *\n * @param {Buffer} chunk A chunk of data\n * @private\n */\nfunction inflateOnData(chunk) {\n  this[kTotalLength] += chunk.length;\n  if (this[kPerMessageDeflate]._maxPayload < 1 || this[kTotalLength] <= this[kPerMessageDeflate]._maxPayload) {\n    this[kBuffers].push(chunk);\n    return;\n  }\n  this[kError] = new RangeError('Max payload size exceeded');\n  this[kError].code = 'WS_ERR_UNSUPPORTED_MESSAGE_LENGTH';\n  this[kError][kStatusCode] = 1009;\n  this.removeListener('data', inflateOnData);\n\n  //\n  // The choice to employ `zlib.reset()` over `zlib.close()` is dictated by the\n  // fact that in Node.js versions prior to 13.10.0, the callback for\n  // `zlib.flush()` is not called if `zlib.close()` is used. Utilizing\n  // `zlib.reset()` ensures that either the callback is invoked or an error is\n  // emitted.\n  //\n  this.reset();\n}\n\n/**\n * The listener of the `zlib.InflateRaw` stream `'error'` event.\n *\n * @param {Error} err The emitted error\n * @private\n */\nfunction inflateOnError(err) {\n  //\n  // There is no need to call `Zlib#close()` as the handle is automatically\n  // closed when an error is emitted.\n  //\n  this[kPerMessageDeflate]._inflate = null;\n  if (this[kError]) {\n    this[kCallback](this[kError]);\n    return;\n  }\n  err[kStatusCode] = 1007;\n  this[kCallback](err);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/permessage-deflate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/receiver.js":
/*!*****************************************!*\
  !*** ./node_modules/ws/lib/receiver.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst {\n  Writable\n} = __webpack_require__(/*! stream */ \"stream\");\nconst PerMessageDeflate = __webpack_require__(/*! ./permessage-deflate */ \"(ssr)/./node_modules/ws/lib/permessage-deflate.js\");\nconst {\n  BINARY_TYPES,\n  EMPTY_BUFFER,\n  kStatusCode,\n  kWebSocket\n} = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\nconst {\n  concat,\n  toArrayBuffer,\n  unmask\n} = __webpack_require__(/*! ./buffer-util */ \"(ssr)/./node_modules/ws/lib/buffer-util.js\");\nconst {\n  isValidStatusCode,\n  isValidUTF8\n} = __webpack_require__(/*! ./validation */ \"(ssr)/./node_modules/ws/lib/validation.js\");\nconst FastBuffer = Buffer[Symbol.species];\nconst GET_INFO = 0;\nconst GET_PAYLOAD_LENGTH_16 = 1;\nconst GET_PAYLOAD_LENGTH_64 = 2;\nconst GET_MASK = 3;\nconst GET_DATA = 4;\nconst INFLATING = 5;\nconst DEFER_EVENT = 6;\n\n/**\n * HyBi Receiver implementation.\n *\n * @extends Writable\n */\nclass Receiver extends Writable {\n  /**\n   * Creates a Receiver instance.\n   *\n   * @param {Object} [options] Options object\n   * @param {Boolean} [options.allowSynchronousEvents=true] Specifies whether\n   *     any of the `'message'`, `'ping'`, and `'pong'` events can be emitted\n   *     multiple times in the same tick\n   * @param {String} [options.binaryType=nodebuffer] The type for binary data\n   * @param {Object} [options.extensions] An object containing the negotiated\n   *     extensions\n   * @param {Boolean} [options.isServer=false] Specifies whether to operate in\n   *     client or server mode\n   * @param {Number} [options.maxPayload=0] The maximum allowed message length\n   * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or\n   *     not to skip UTF-8 validation for text and close messages\n   */\n  constructor(options = {}) {\n    super();\n    this._allowSynchronousEvents = options.allowSynchronousEvents !== undefined ? options.allowSynchronousEvents : true;\n    this._binaryType = options.binaryType || BINARY_TYPES[0];\n    this._extensions = options.extensions || {};\n    this._isServer = !!options.isServer;\n    this._maxPayload = options.maxPayload | 0;\n    this._skipUTF8Validation = !!options.skipUTF8Validation;\n    this[kWebSocket] = undefined;\n    this._bufferedBytes = 0;\n    this._buffers = [];\n    this._compressed = false;\n    this._payloadLength = 0;\n    this._mask = undefined;\n    this._fragmented = 0;\n    this._masked = false;\n    this._fin = false;\n    this._opcode = 0;\n    this._totalPayloadLength = 0;\n    this._messageLength = 0;\n    this._fragments = [];\n    this._errored = false;\n    this._loop = false;\n    this._state = GET_INFO;\n  }\n\n  /**\n   * Implements `Writable.prototype._write()`.\n   *\n   * @param {Buffer} chunk The chunk of data to write\n   * @param {String} encoding The character encoding of `chunk`\n   * @param {Function} cb Callback\n   * @private\n   */\n  _write(chunk, encoding, cb) {\n    if (this._opcode === 0x08 && this._state == GET_INFO) return cb();\n    this._bufferedBytes += chunk.length;\n    this._buffers.push(chunk);\n    this.startLoop(cb);\n  }\n\n  /**\n   * Consumes `n` bytes from the buffered data.\n   *\n   * @param {Number} n The number of bytes to consume\n   * @return {Buffer} The consumed bytes\n   * @private\n   */\n  consume(n) {\n    this._bufferedBytes -= n;\n    if (n === this._buffers[0].length) return this._buffers.shift();\n    if (n < this._buffers[0].length) {\n      const buf = this._buffers[0];\n      this._buffers[0] = new FastBuffer(buf.buffer, buf.byteOffset + n, buf.length - n);\n      return new FastBuffer(buf.buffer, buf.byteOffset, n);\n    }\n    const dst = Buffer.allocUnsafe(n);\n    do {\n      const buf = this._buffers[0];\n      const offset = dst.length - n;\n      if (n >= buf.length) {\n        dst.set(this._buffers.shift(), offset);\n      } else {\n        dst.set(new Uint8Array(buf.buffer, buf.byteOffset, n), offset);\n        this._buffers[0] = new FastBuffer(buf.buffer, buf.byteOffset + n, buf.length - n);\n      }\n      n -= buf.length;\n    } while (n > 0);\n    return dst;\n  }\n\n  /**\n   * Starts the parsing loop.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */\n  startLoop(cb) {\n    this._loop = true;\n    do {\n      switch (this._state) {\n        case GET_INFO:\n          this.getInfo(cb);\n          break;\n        case GET_PAYLOAD_LENGTH_16:\n          this.getPayloadLength16(cb);\n          break;\n        case GET_PAYLOAD_LENGTH_64:\n          this.getPayloadLength64(cb);\n          break;\n        case GET_MASK:\n          this.getMask();\n          break;\n        case GET_DATA:\n          this.getData(cb);\n          break;\n        case INFLATING:\n        case DEFER_EVENT:\n          this._loop = false;\n          return;\n      }\n    } while (this._loop);\n    if (!this._errored) cb();\n  }\n\n  /**\n   * Reads the first two bytes of a frame.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */\n  getInfo(cb) {\n    if (this._bufferedBytes < 2) {\n      this._loop = false;\n      return;\n    }\n    const buf = this.consume(2);\n    if ((buf[0] & 0x30) !== 0x00) {\n      const error = this.createError(RangeError, 'RSV2 and RSV3 must be clear', true, 1002, 'WS_ERR_UNEXPECTED_RSV_2_3');\n      cb(error);\n      return;\n    }\n    const compressed = (buf[0] & 0x40) === 0x40;\n    if (compressed && !this._extensions[PerMessageDeflate.extensionName]) {\n      const error = this.createError(RangeError, 'RSV1 must be clear', true, 1002, 'WS_ERR_UNEXPECTED_RSV_1');\n      cb(error);\n      return;\n    }\n    this._fin = (buf[0] & 0x80) === 0x80;\n    this._opcode = buf[0] & 0x0f;\n    this._payloadLength = buf[1] & 0x7f;\n    if (this._opcode === 0x00) {\n      if (compressed) {\n        const error = this.createError(RangeError, 'RSV1 must be clear', true, 1002, 'WS_ERR_UNEXPECTED_RSV_1');\n        cb(error);\n        return;\n      }\n      if (!this._fragmented) {\n        const error = this.createError(RangeError, 'invalid opcode 0', true, 1002, 'WS_ERR_INVALID_OPCODE');\n        cb(error);\n        return;\n      }\n      this._opcode = this._fragmented;\n    } else if (this._opcode === 0x01 || this._opcode === 0x02) {\n      if (this._fragmented) {\n        const error = this.createError(RangeError, `invalid opcode ${this._opcode}`, true, 1002, 'WS_ERR_INVALID_OPCODE');\n        cb(error);\n        return;\n      }\n      this._compressed = compressed;\n    } else if (this._opcode > 0x07 && this._opcode < 0x0b) {\n      if (!this._fin) {\n        const error = this.createError(RangeError, 'FIN must be set', true, 1002, 'WS_ERR_EXPECTED_FIN');\n        cb(error);\n        return;\n      }\n      if (compressed) {\n        const error = this.createError(RangeError, 'RSV1 must be clear', true, 1002, 'WS_ERR_UNEXPECTED_RSV_1');\n        cb(error);\n        return;\n      }\n      if (this._payloadLength > 0x7d || this._opcode === 0x08 && this._payloadLength === 1) {\n        const error = this.createError(RangeError, `invalid payload length ${this._payloadLength}`, true, 1002, 'WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH');\n        cb(error);\n        return;\n      }\n    } else {\n      const error = this.createError(RangeError, `invalid opcode ${this._opcode}`, true, 1002, 'WS_ERR_INVALID_OPCODE');\n      cb(error);\n      return;\n    }\n    if (!this._fin && !this._fragmented) this._fragmented = this._opcode;\n    this._masked = (buf[1] & 0x80) === 0x80;\n    if (this._isServer) {\n      if (!this._masked) {\n        const error = this.createError(RangeError, 'MASK must be set', true, 1002, 'WS_ERR_EXPECTED_MASK');\n        cb(error);\n        return;\n      }\n    } else if (this._masked) {\n      const error = this.createError(RangeError, 'MASK must be clear', true, 1002, 'WS_ERR_UNEXPECTED_MASK');\n      cb(error);\n      return;\n    }\n    if (this._payloadLength === 126) this._state = GET_PAYLOAD_LENGTH_16;else if (this._payloadLength === 127) this._state = GET_PAYLOAD_LENGTH_64;else this.haveLength(cb);\n  }\n\n  /**\n   * Gets extended payload length (7+16).\n   *\n   * @param {Function} cb Callback\n   * @private\n   */\n  getPayloadLength16(cb) {\n    if (this._bufferedBytes < 2) {\n      this._loop = false;\n      return;\n    }\n    this._payloadLength = this.consume(2).readUInt16BE(0);\n    this.haveLength(cb);\n  }\n\n  /**\n   * Gets extended payload length (7+64).\n   *\n   * @param {Function} cb Callback\n   * @private\n   */\n  getPayloadLength64(cb) {\n    if (this._bufferedBytes < 8) {\n      this._loop = false;\n      return;\n    }\n    const buf = this.consume(8);\n    const num = buf.readUInt32BE(0);\n\n    //\n    // The maximum safe integer in JavaScript is 2^53 - 1. An error is returned\n    // if payload length is greater than this number.\n    //\n    if (num > Math.pow(2, 53 - 32) - 1) {\n      const error = this.createError(RangeError, 'Unsupported WebSocket frame: payload length > 2^53 - 1', false, 1009, 'WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH');\n      cb(error);\n      return;\n    }\n    this._payloadLength = num * Math.pow(2, 32) + buf.readUInt32BE(4);\n    this.haveLength(cb);\n  }\n\n  /**\n   * Payload length has been read.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */\n  haveLength(cb) {\n    if (this._payloadLength && this._opcode < 0x08) {\n      this._totalPayloadLength += this._payloadLength;\n      if (this._totalPayloadLength > this._maxPayload && this._maxPayload > 0) {\n        const error = this.createError(RangeError, 'Max payload size exceeded', false, 1009, 'WS_ERR_UNSUPPORTED_MESSAGE_LENGTH');\n        cb(error);\n        return;\n      }\n    }\n    if (this._masked) this._state = GET_MASK;else this._state = GET_DATA;\n  }\n\n  /**\n   * Reads mask bytes.\n   *\n   * @private\n   */\n  getMask() {\n    if (this._bufferedBytes < 4) {\n      this._loop = false;\n      return;\n    }\n    this._mask = this.consume(4);\n    this._state = GET_DATA;\n  }\n\n  /**\n   * Reads data bytes.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */\n  getData(cb) {\n    let data = EMPTY_BUFFER;\n    if (this._payloadLength) {\n      if (this._bufferedBytes < this._payloadLength) {\n        this._loop = false;\n        return;\n      }\n      data = this.consume(this._payloadLength);\n      if (this._masked && (this._mask[0] | this._mask[1] | this._mask[2] | this._mask[3]) !== 0) {\n        unmask(data, this._mask);\n      }\n    }\n    if (this._opcode > 0x07) {\n      this.controlMessage(data, cb);\n      return;\n    }\n    if (this._compressed) {\n      this._state = INFLATING;\n      this.decompress(data, cb);\n      return;\n    }\n    if (data.length) {\n      //\n      // This message is not compressed so its length is the sum of the payload\n      // length of all fragments.\n      //\n      this._messageLength = this._totalPayloadLength;\n      this._fragments.push(data);\n    }\n    this.dataMessage(cb);\n  }\n\n  /**\n   * Decompresses data.\n   *\n   * @param {Buffer} data Compressed data\n   * @param {Function} cb Callback\n   * @private\n   */\n  decompress(data, cb) {\n    const perMessageDeflate = this._extensions[PerMessageDeflate.extensionName];\n    perMessageDeflate.decompress(data, this._fin, (err, buf) => {\n      if (err) return cb(err);\n      if (buf.length) {\n        this._messageLength += buf.length;\n        if (this._messageLength > this._maxPayload && this._maxPayload > 0) {\n          const error = this.createError(RangeError, 'Max payload size exceeded', false, 1009, 'WS_ERR_UNSUPPORTED_MESSAGE_LENGTH');\n          cb(error);\n          return;\n        }\n        this._fragments.push(buf);\n      }\n      this.dataMessage(cb);\n      if (this._state === GET_INFO) this.startLoop(cb);\n    });\n  }\n\n  /**\n   * Handles a data message.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */\n  dataMessage(cb) {\n    if (!this._fin) {\n      this._state = GET_INFO;\n      return;\n    }\n    const messageLength = this._messageLength;\n    const fragments = this._fragments;\n    this._totalPayloadLength = 0;\n    this._messageLength = 0;\n    this._fragmented = 0;\n    this._fragments = [];\n    if (this._opcode === 2) {\n      let data;\n      if (this._binaryType === 'nodebuffer') {\n        data = concat(fragments, messageLength);\n      } else if (this._binaryType === 'arraybuffer') {\n        data = toArrayBuffer(concat(fragments, messageLength));\n      } else if (this._binaryType === 'blob') {\n        data = new Blob(fragments);\n      } else {\n        data = fragments;\n      }\n      if (this._allowSynchronousEvents) {\n        this.emit('message', data, true);\n        this._state = GET_INFO;\n      } else {\n        this._state = DEFER_EVENT;\n        setImmediate(() => {\n          this.emit('message', data, true);\n          this._state = GET_INFO;\n          this.startLoop(cb);\n        });\n      }\n    } else {\n      const buf = concat(fragments, messageLength);\n      if (!this._skipUTF8Validation && !isValidUTF8(buf)) {\n        const error = this.createError(Error, 'invalid UTF-8 sequence', true, 1007, 'WS_ERR_INVALID_UTF8');\n        cb(error);\n        return;\n      }\n      if (this._state === INFLATING || this._allowSynchronousEvents) {\n        this.emit('message', buf, false);\n        this._state = GET_INFO;\n      } else {\n        this._state = DEFER_EVENT;\n        setImmediate(() => {\n          this.emit('message', buf, false);\n          this._state = GET_INFO;\n          this.startLoop(cb);\n        });\n      }\n    }\n  }\n\n  /**\n   * Handles a control message.\n   *\n   * @param {Buffer} data Data to handle\n   * @return {(Error|RangeError|undefined)} A possible error\n   * @private\n   */\n  controlMessage(data, cb) {\n    if (this._opcode === 0x08) {\n      if (data.length === 0) {\n        this._loop = false;\n        this.emit('conclude', 1005, EMPTY_BUFFER);\n        this.end();\n      } else {\n        const code = data.readUInt16BE(0);\n        if (!isValidStatusCode(code)) {\n          const error = this.createError(RangeError, `invalid status code ${code}`, true, 1002, 'WS_ERR_INVALID_CLOSE_CODE');\n          cb(error);\n          return;\n        }\n        const buf = new FastBuffer(data.buffer, data.byteOffset + 2, data.length - 2);\n        if (!this._skipUTF8Validation && !isValidUTF8(buf)) {\n          const error = this.createError(Error, 'invalid UTF-8 sequence', true, 1007, 'WS_ERR_INVALID_UTF8');\n          cb(error);\n          return;\n        }\n        this._loop = false;\n        this.emit('conclude', code, buf);\n        this.end();\n      }\n      this._state = GET_INFO;\n      return;\n    }\n    if (this._allowSynchronousEvents) {\n      this.emit(this._opcode === 0x09 ? 'ping' : 'pong', data);\n      this._state = GET_INFO;\n    } else {\n      this._state = DEFER_EVENT;\n      setImmediate(() => {\n        this.emit(this._opcode === 0x09 ? 'ping' : 'pong', data);\n        this._state = GET_INFO;\n        this.startLoop(cb);\n      });\n    }\n  }\n\n  /**\n   * Builds an error object.\n   *\n   * @param {function(new:Error|RangeError)} ErrorCtor The error constructor\n   * @param {String} message The error message\n   * @param {Boolean} prefix Specifies whether or not to add a default prefix to\n   *     `message`\n   * @param {Number} statusCode The status code\n   * @param {String} errorCode The exposed error code\n   * @return {(Error|RangeError)} The error\n   * @private\n   */\n  createError(ErrorCtor, message, prefix, statusCode, errorCode) {\n    this._loop = false;\n    this._errored = true;\n    const err = new ErrorCtor(prefix ? `Invalid WebSocket frame: ${message}` : message);\n    Error.captureStackTrace(err, this.createError);\n    err.code = errorCode;\n    err[kStatusCode] = statusCode;\n    return err;\n  }\n}\nmodule.exports = Receiver;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/receiver.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/sender.js":
/*!***************************************!*\
  !*** ./node_modules/ws/lib/sender.js ***!
  \***************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/* eslint no-unused-vars: [\"error\", { \"varsIgnorePattern\": \"^Duplex\" }] */ \nconst { Duplex } = __webpack_require__(/*! stream */ \"stream\");\nconst { randomFillSync } = __webpack_require__(/*! crypto */ \"crypto\");\nconst PerMessageDeflate = __webpack_require__(/*! ./permessage-deflate */ \"(ssr)/./node_modules/ws/lib/permessage-deflate.js\");\nconst { EMPTY_BUFFER, kWebSocket, NOOP } = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\nconst { isBlob, isValidStatusCode } = __webpack_require__(/*! ./validation */ \"(ssr)/./node_modules/ws/lib/validation.js\");\nconst { mask: applyMask, toBuffer } = __webpack_require__(/*! ./buffer-util */ \"(ssr)/./node_modules/ws/lib/buffer-util.js\");\nconst kByteLength = Symbol('kByteLength');\nconst maskBuffer = Buffer.alloc(4);\nconst RANDOM_POOL_SIZE = 8 * 1024;\nlet randomPool;\nlet randomPoolPointer = RANDOM_POOL_SIZE;\nconst DEFAULT = 0;\nconst DEFLATING = 1;\nconst GET_BLOB_DATA = 2;\n/**\n * HyBi Sender implementation.\n */ class Sender {\n    /**\n   * Creates a Sender instance.\n   *\n   * @param {Duplex} socket The connection socket\n   * @param {Object} [extensions] An object containing the negotiated extensions\n   * @param {Function} [generateMask] The function used to generate the masking\n   *     key\n   */ constructor(socket, extensions, generateMask){\n        this._extensions = extensions || {};\n        if (generateMask) {\n            this._generateMask = generateMask;\n            this._maskBuffer = Buffer.alloc(4);\n        }\n        this._socket = socket;\n        this._firstFragment = true;\n        this._compress = false;\n        this._bufferedBytes = 0;\n        this._queue = [];\n        this._state = DEFAULT;\n        this.onerror = NOOP;\n        this[kWebSocket] = undefined;\n    }\n    /**\n   * Frames a piece of data according to the HyBi WebSocket protocol.\n   *\n   * @param {(Buffer|String)} data The data to frame\n   * @param {Object} options Options object\n   * @param {Boolean} [options.fin=false] Specifies whether or not to set the\n   *     FIN bit\n   * @param {Function} [options.generateMask] The function used to generate the\n   *     masking key\n   * @param {Boolean} [options.mask=false] Specifies whether or not to mask\n   *     `data`\n   * @param {Buffer} [options.maskBuffer] The buffer used to store the masking\n   *     key\n   * @param {Number} options.opcode The opcode\n   * @param {Boolean} [options.readOnly=false] Specifies whether `data` can be\n   *     modified\n   * @param {Boolean} [options.rsv1=false] Specifies whether or not to set the\n   *     RSV1 bit\n   * @return {(Buffer|String)[]} The framed data\n   * @public\n   */ static frame(data, options) {\n        let mask;\n        let merge = false;\n        let offset = 2;\n        let skipMasking = false;\n        if (options.mask) {\n            mask = options.maskBuffer || maskBuffer;\n            if (options.generateMask) {\n                options.generateMask(mask);\n            } else {\n                if (randomPoolPointer === RANDOM_POOL_SIZE) {\n                    /* istanbul ignore else  */ if (randomPool === undefined) {\n                        //\n                        // This is lazily initialized because server-sent frames must not\n                        // be masked so it may never be used.\n                        //\n                        randomPool = Buffer.alloc(RANDOM_POOL_SIZE);\n                    }\n                    randomFillSync(randomPool, 0, RANDOM_POOL_SIZE);\n                    randomPoolPointer = 0;\n                }\n                mask[0] = randomPool[randomPoolPointer++];\n                mask[1] = randomPool[randomPoolPointer++];\n                mask[2] = randomPool[randomPoolPointer++];\n                mask[3] = randomPool[randomPoolPointer++];\n            }\n            skipMasking = (mask[0] | mask[1] | mask[2] | mask[3]) === 0;\n            offset = 6;\n        }\n        let dataLength;\n        if (typeof data === 'string') {\n            if ((!options.mask || skipMasking) && options[kByteLength] !== undefined) {\n                dataLength = options[kByteLength];\n            } else {\n                data = Buffer.from(data);\n                dataLength = data.length;\n            }\n        } else {\n            dataLength = data.length;\n            merge = options.mask && options.readOnly && !skipMasking;\n        }\n        let payloadLength = dataLength;\n        if (dataLength >= 65536) {\n            offset += 8;\n            payloadLength = 127;\n        } else if (dataLength > 125) {\n            offset += 2;\n            payloadLength = 126;\n        }\n        const target = Buffer.allocUnsafe(merge ? dataLength + offset : offset);\n        target[0] = options.fin ? options.opcode | 0x80 : options.opcode;\n        if (options.rsv1) target[0] |= 0x40;\n        target[1] = payloadLength;\n        if (payloadLength === 126) {\n            target.writeUInt16BE(dataLength, 2);\n        } else if (payloadLength === 127) {\n            target[2] = target[3] = 0;\n            target.writeUIntBE(dataLength, 4, 6);\n        }\n        if (!options.mask) return [\n            target,\n            data\n        ];\n        target[1] |= 0x80;\n        target[offset - 4] = mask[0];\n        target[offset - 3] = mask[1];\n        target[offset - 2] = mask[2];\n        target[offset - 1] = mask[3];\n        if (skipMasking) return [\n            target,\n            data\n        ];\n        if (merge) {\n            applyMask(data, mask, target, offset, dataLength);\n            return [\n                target\n            ];\n        }\n        applyMask(data, mask, data, 0, dataLength);\n        return [\n            target,\n            data\n        ];\n    }\n    /**\n   * Sends a close message to the other peer.\n   *\n   * @param {Number} [code] The status code component of the body\n   * @param {(String|Buffer)} [data] The message component of the body\n   * @param {Boolean} [mask=false] Specifies whether or not to mask the message\n   * @param {Function} [cb] Callback\n   * @public\n   */ close(code, data, mask, cb) {\n        let buf;\n        if (code === undefined) {\n            buf = EMPTY_BUFFER;\n        } else if (typeof code !== 'number' || !isValidStatusCode(code)) {\n            throw new TypeError('First argument must be a valid error code number');\n        } else if (data === undefined || !data.length) {\n            buf = Buffer.allocUnsafe(2);\n            buf.writeUInt16BE(code, 0);\n        } else {\n            const length = Buffer.byteLength(data);\n            if (length > 123) {\n                throw new RangeError('The message must not be greater than 123 bytes');\n            }\n            buf = Buffer.allocUnsafe(2 + length);\n            buf.writeUInt16BE(code, 0);\n            if (typeof data === 'string') {\n                buf.write(data, 2);\n            } else {\n                buf.set(data, 2);\n            }\n        }\n        const options = {\n            [kByteLength]: buf.length,\n            fin: true,\n            generateMask: this._generateMask,\n            mask,\n            maskBuffer: this._maskBuffer,\n            opcode: 0x08,\n            readOnly: false,\n            rsv1: false\n        };\n        if (this._state !== DEFAULT) {\n            this.enqueue([\n                this.dispatch,\n                buf,\n                false,\n                options,\n                cb\n            ]);\n        } else {\n            this.sendFrame(Sender.frame(buf, options), cb);\n        }\n    }\n    /**\n   * Sends a ping message to the other peer.\n   *\n   * @param {*} data The message to send\n   * @param {Boolean} [mask=false] Specifies whether or not to mask `data`\n   * @param {Function} [cb] Callback\n   * @public\n   */ ping(data, mask, cb) {\n        let byteLength;\n        let readOnly;\n        if (typeof data === 'string') {\n            byteLength = Buffer.byteLength(data);\n            readOnly = false;\n        } else if (isBlob(data)) {\n            byteLength = data.size;\n            readOnly = false;\n        } else {\n            data = toBuffer(data);\n            byteLength = data.length;\n            readOnly = toBuffer.readOnly;\n        }\n        if (byteLength > 125) {\n            throw new RangeError('The data size must not be greater than 125 bytes');\n        }\n        const options = {\n            [kByteLength]: byteLength,\n            fin: true,\n            generateMask: this._generateMask,\n            mask,\n            maskBuffer: this._maskBuffer,\n            opcode: 0x09,\n            readOnly,\n            rsv1: false\n        };\n        if (isBlob(data)) {\n            if (this._state !== DEFAULT) {\n                this.enqueue([\n                    this.getBlobData,\n                    data,\n                    false,\n                    options,\n                    cb\n                ]);\n            } else {\n                this.getBlobData(data, false, options, cb);\n            }\n        } else if (this._state !== DEFAULT) {\n            this.enqueue([\n                this.dispatch,\n                data,\n                false,\n                options,\n                cb\n            ]);\n        } else {\n            this.sendFrame(Sender.frame(data, options), cb);\n        }\n    }\n    /**\n   * Sends a pong message to the other peer.\n   *\n   * @param {*} data The message to send\n   * @param {Boolean} [mask=false] Specifies whether or not to mask `data`\n   * @param {Function} [cb] Callback\n   * @public\n   */ pong(data, mask, cb) {\n        let byteLength;\n        let readOnly;\n        if (typeof data === 'string') {\n            byteLength = Buffer.byteLength(data);\n            readOnly = false;\n        } else if (isBlob(data)) {\n            byteLength = data.size;\n            readOnly = false;\n        } else {\n            data = toBuffer(data);\n            byteLength = data.length;\n            readOnly = toBuffer.readOnly;\n        }\n        if (byteLength > 125) {\n            throw new RangeError('The data size must not be greater than 125 bytes');\n        }\n        const options = {\n            [kByteLength]: byteLength,\n            fin: true,\n            generateMask: this._generateMask,\n            mask,\n            maskBuffer: this._maskBuffer,\n            opcode: 0x0a,\n            readOnly,\n            rsv1: false\n        };\n        if (isBlob(data)) {\n            if (this._state !== DEFAULT) {\n                this.enqueue([\n                    this.getBlobData,\n                    data,\n                    false,\n                    options,\n                    cb\n                ]);\n            } else {\n                this.getBlobData(data, false, options, cb);\n            }\n        } else if (this._state !== DEFAULT) {\n            this.enqueue([\n                this.dispatch,\n                data,\n                false,\n                options,\n                cb\n            ]);\n        } else {\n            this.sendFrame(Sender.frame(data, options), cb);\n        }\n    }\n    /**\n   * Sends a data message to the other peer.\n   *\n   * @param {*} data The message to send\n   * @param {Object} options Options object\n   * @param {Boolean} [options.binary=false] Specifies whether `data` is binary\n   *     or text\n   * @param {Boolean} [options.compress=false] Specifies whether or not to\n   *     compress `data`\n   * @param {Boolean} [options.fin=false] Specifies whether the fragment is the\n   *     last one\n   * @param {Boolean} [options.mask=false] Specifies whether or not to mask\n   *     `data`\n   * @param {Function} [cb] Callback\n   * @public\n   */ send(data, options, cb) {\n        const perMessageDeflate = this._extensions[PerMessageDeflate.extensionName];\n        let opcode = options.binary ? 2 : 1;\n        let rsv1 = options.compress;\n        let byteLength;\n        let readOnly;\n        if (typeof data === 'string') {\n            byteLength = Buffer.byteLength(data);\n            readOnly = false;\n        } else if (isBlob(data)) {\n            byteLength = data.size;\n            readOnly = false;\n        } else {\n            data = toBuffer(data);\n            byteLength = data.length;\n            readOnly = toBuffer.readOnly;\n        }\n        if (this._firstFragment) {\n            this._firstFragment = false;\n            if (rsv1 && perMessageDeflate && perMessageDeflate.params[perMessageDeflate._isServer ? 'server_no_context_takeover' : 'client_no_context_takeover']) {\n                rsv1 = byteLength >= perMessageDeflate._threshold;\n            }\n            this._compress = rsv1;\n        } else {\n            rsv1 = false;\n            opcode = 0;\n        }\n        if (options.fin) this._firstFragment = true;\n        const opts = {\n            [kByteLength]: byteLength,\n            fin: options.fin,\n            generateMask: this._generateMask,\n            mask: options.mask,\n            maskBuffer: this._maskBuffer,\n            opcode,\n            readOnly,\n            rsv1\n        };\n        if (isBlob(data)) {\n            if (this._state !== DEFAULT) {\n                this.enqueue([\n                    this.getBlobData,\n                    data,\n                    this._compress,\n                    opts,\n                    cb\n                ]);\n            } else {\n                this.getBlobData(data, this._compress, opts, cb);\n            }\n        } else if (this._state !== DEFAULT) {\n            this.enqueue([\n                this.dispatch,\n                data,\n                this._compress,\n                opts,\n                cb\n            ]);\n        } else {\n            this.dispatch(data, this._compress, opts, cb);\n        }\n    }\n    /**\n   * Gets the contents of a blob as binary data.\n   *\n   * @param {Blob} blob The blob\n   * @param {Boolean} [compress=false] Specifies whether or not to compress\n   *     the data\n   * @param {Object} options Options object\n   * @param {Boolean} [options.fin=false] Specifies whether or not to set the\n   *     FIN bit\n   * @param {Function} [options.generateMask] The function used to generate the\n   *     masking key\n   * @param {Boolean} [options.mask=false] Specifies whether or not to mask\n   *     `data`\n   * @param {Buffer} [options.maskBuffer] The buffer used to store the masking\n   *     key\n   * @param {Number} options.opcode The opcode\n   * @param {Boolean} [options.readOnly=false] Specifies whether `data` can be\n   *     modified\n   * @param {Boolean} [options.rsv1=false] Specifies whether or not to set the\n   *     RSV1 bit\n   * @param {Function} [cb] Callback\n   * @private\n   */ getBlobData(blob, compress, options, cb) {\n        this._bufferedBytes += options[kByteLength];\n        this._state = GET_BLOB_DATA;\n        blob.arrayBuffer().then((arrayBuffer)=>{\n            if (this._socket.destroyed) {\n                const err = new Error('The socket was closed while the blob was being read');\n                //\n                // `callCallbacks` is called in the next tick to ensure that errors\n                // that might be thrown in the callbacks behave like errors thrown\n                // outside the promise chain.\n                //\n                process.nextTick(callCallbacks, this, err, cb);\n                return;\n            }\n            this._bufferedBytes -= options[kByteLength];\n            const data = toBuffer(arrayBuffer);\n            if (!compress) {\n                this._state = DEFAULT;\n                this.sendFrame(Sender.frame(data, options), cb);\n                this.dequeue();\n            } else {\n                this.dispatch(data, compress, options, cb);\n            }\n        }).catch((err)=>{\n            //\n            // `onError` is called in the next tick for the same reason that\n            // `callCallbacks` above is.\n            //\n            process.nextTick(onError, this, err, cb);\n        });\n    }\n    /**\n   * Dispatches a message.\n   *\n   * @param {(Buffer|String)} data The message to send\n   * @param {Boolean} [compress=false] Specifies whether or not to compress\n   *     `data`\n   * @param {Object} options Options object\n   * @param {Boolean} [options.fin=false] Specifies whether or not to set the\n   *     FIN bit\n   * @param {Function} [options.generateMask] The function used to generate the\n   *     masking key\n   * @param {Boolean} [options.mask=false] Specifies whether or not to mask\n   *     `data`\n   * @param {Buffer} [options.maskBuffer] The buffer used to store the masking\n   *     key\n   * @param {Number} options.opcode The opcode\n   * @param {Boolean} [options.readOnly=false] Specifies whether `data` can be\n   *     modified\n   * @param {Boolean} [options.rsv1=false] Specifies whether or not to set the\n   *     RSV1 bit\n   * @param {Function} [cb] Callback\n   * @private\n   */ dispatch(data, compress, options, cb) {\n        if (!compress) {\n            this.sendFrame(Sender.frame(data, options), cb);\n            return;\n        }\n        const perMessageDeflate = this._extensions[PerMessageDeflate.extensionName];\n        this._bufferedBytes += options[kByteLength];\n        this._state = DEFLATING;\n        perMessageDeflate.compress(data, options.fin, (_, buf)=>{\n            if (this._socket.destroyed) {\n                const err = new Error('The socket was closed while data was being compressed');\n                callCallbacks(this, err, cb);\n                return;\n            }\n            this._bufferedBytes -= options[kByteLength];\n            this._state = DEFAULT;\n            options.readOnly = false;\n            this.sendFrame(Sender.frame(buf, options), cb);\n            this.dequeue();\n        });\n    }\n    /**\n   * Executes queued send operations.\n   *\n   * @private\n   */ dequeue() {\n        while(this._state === DEFAULT && this._queue.length){\n            const params = this._queue.shift();\n            this._bufferedBytes -= params[3][kByteLength];\n            Reflect.apply(params[0], this, params.slice(1));\n        }\n    }\n    /**\n   * Enqueues a send operation.\n   *\n   * @param {Array} params Send operation parameters.\n   * @private\n   */ enqueue(params) {\n        this._bufferedBytes += params[3][kByteLength];\n        this._queue.push(params);\n    }\n    /**\n   * Sends a frame.\n   *\n   * @param {(Buffer | String)[]} list The frame to send\n   * @param {Function} [cb] Callback\n   * @private\n   */ sendFrame(list, cb) {\n        if (list.length === 2) {\n            this._socket.cork();\n            this._socket.write(list[0]);\n            this._socket.write(list[1], cb);\n            this._socket.uncork();\n        } else {\n            this._socket.write(list[0], cb);\n        }\n    }\n}\nmodule.exports = Sender;\n/**\n * Calls queued callbacks with an error.\n *\n * @param {Sender} sender The `Sender` instance\n * @param {Error} err The error to call the callbacks with\n * @param {Function} [cb] The first callback\n * @private\n */ function callCallbacks(sender, err, cb) {\n    if (typeof cb === 'function') cb(err);\n    for(let i = 0; i < sender._queue.length; i++){\n        const params = sender._queue[i];\n        const callback = params[params.length - 1];\n        if (typeof callback === 'function') callback(err);\n    }\n}\n/**\n * Handles a `Sender` error.\n *\n * @param {Sender} sender The `Sender` instance\n * @param {Error} err The error\n * @param {Function} [cb] The first pending callback\n * @private\n */ function onError(sender, err, cb) {\n    callCallbacks(sender, err, cb);\n    sender.onerror(err);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/sender.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/stream.js":
/*!***************************************!*\
  !*** ./node_modules/ws/lib/stream.js ***!
  \***************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/* eslint no-unused-vars: [\"error\", { \"varsIgnorePattern\": \"^WebSocket$\" }] */\n\n\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return typeof key === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (typeof input !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (typeof res !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nconst WebSocket = __webpack_require__(/*! ./websocket */ \"(ssr)/./node_modules/ws/lib/websocket.js\");\nconst {\n  Duplex\n} = __webpack_require__(/*! stream */ \"stream\");\n\n/**\n * Emits the `'close'` event on a stream.\n *\n * @param {Duplex} stream The stream.\n * @private\n */\nfunction emitClose(stream) {\n  stream.emit('close');\n}\n\n/**\n * The listener of the `'end'` event.\n *\n * @private\n */\nfunction duplexOnEnd() {\n  if (!this.destroyed && this._writableState.finished) {\n    this.destroy();\n  }\n}\n\n/**\n * The listener of the `'error'` event.\n *\n * @param {Error} err The error\n * @private\n */\nfunction duplexOnError(err) {\n  this.removeListener('error', duplexOnError);\n  this.destroy();\n  if (this.listenerCount('error') === 0) {\n    // Do not suppress the throwing behavior.\n    this.emit('error', err);\n  }\n}\n\n/**\n * Wraps a `WebSocket` in a duplex stream.\n *\n * @param {WebSocket} ws The `WebSocket` to wrap\n * @param {Object} [options] The options for the `Duplex` constructor\n * @return {Duplex} The duplex stream\n * @public\n */\nfunction createWebSocketStream(ws, options) {\n  let terminateOnDestroy = true;\n  const duplex = new Duplex(_objectSpread(_objectSpread({}, options), {}, {\n    autoDestroy: false,\n    emitClose: false,\n    objectMode: false,\n    writableObjectMode: false\n  }));\n  ws.on('message', function message(msg, isBinary) {\n    const data = !isBinary && duplex._readableState.objectMode ? msg.toString() : msg;\n    if (!duplex.push(data)) ws.pause();\n  });\n  ws.once('error', function error(err) {\n    if (duplex.destroyed) return;\n\n    // Prevent `ws.terminate()` from being called by `duplex._destroy()`.\n    //\n    // - If the `'error'` event is emitted before the `'open'` event, then\n    //   `ws.terminate()` is a noop as no socket is assigned.\n    // - Otherwise, the error is re-emitted by the listener of the `'error'`\n    //   event of the `Receiver` object. The listener already closes the\n    //   connection by calling `ws.close()`. This allows a close frame to be\n    //   sent to the other peer. If `ws.terminate()` is called right after this,\n    //   then the close frame might not be sent.\n    terminateOnDestroy = false;\n    duplex.destroy(err);\n  });\n  ws.once('close', function close() {\n    if (duplex.destroyed) return;\n    duplex.push(null);\n  });\n  duplex._destroy = function (err, callback) {\n    if (ws.readyState === ws.CLOSED) {\n      callback(err);\n      process.nextTick(emitClose, duplex);\n      return;\n    }\n    let called = false;\n    ws.once('error', function error(err) {\n      called = true;\n      callback(err);\n    });\n    ws.once('close', function close() {\n      if (!called) callback(err);\n      process.nextTick(emitClose, duplex);\n    });\n    if (terminateOnDestroy) ws.terminate();\n  };\n  duplex._final = function (callback) {\n    if (ws.readyState === ws.CONNECTING) {\n      ws.once('open', function open() {\n        duplex._final(callback);\n      });\n      return;\n    }\n\n    // If the value of the `_socket` property is `null` it means that `ws` is a\n    // client websocket and the handshake failed. In fact, when this happens, a\n    // socket is never assigned to the websocket. Wait for the `'error'` event\n    // that will be emitted by the websocket.\n    if (ws._socket === null) return;\n    if (ws._socket._writableState.finished) {\n      callback();\n      if (duplex._readableState.endEmitted) duplex.destroy();\n    } else {\n      ws._socket.once('finish', function finish() {\n        // `duplex` is not destroyed here because the `'end'` event will be\n        // emitted on `duplex` after this `'finish'` event. The EOF signaling\n        // `null` chunk is, in fact, pushed when the websocket emits `'close'`.\n        callback();\n      });\n      ws.close();\n    }\n  };\n  duplex._read = function () {\n    if (ws.isPaused) ws.resume();\n  };\n  duplex._write = function (chunk, encoding, callback) {\n    if (ws.readyState === ws.CONNECTING) {\n      ws.once('open', function open() {\n        duplex._write(chunk, encoding, callback);\n      });\n      return;\n    }\n    ws.send(chunk, callback);\n  };\n  duplex.on('end', duplexOnEnd);\n  duplex.on('error', duplexOnError);\n  return duplex;\n}\nmodule.exports = createWebSocketStream;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/stream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/subprotocol.js":
/*!********************************************!*\
  !*** ./node_modules/ws/lib/subprotocol.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst {\n  tokenChars\n} = __webpack_require__(/*! ./validation */ \"(ssr)/./node_modules/ws/lib/validation.js\");\n\n/**\n * Parses the `Sec-WebSocket-Protocol` header into a set of subprotocol names.\n *\n * @param {String} header The field value of the header\n * @return {Set} The subprotocol names\n * @public\n */\nfunction parse(header) {\n  const protocols = new Set();\n  let start = -1;\n  let end = -1;\n  let i = 0;\n  for (i; i < header.length; i++) {\n    const code = header.charCodeAt(i);\n    if (end === -1 && tokenChars[code] === 1) {\n      if (start === -1) start = i;\n    } else if (i !== 0 && (code === 0x20 /* ' ' */ || code === 0x09) /* '\\t' */) {\n      if (end === -1 && start !== -1) end = i;\n    } else if (code === 0x2c /* ',' */) {\n      if (start === -1) {\n        throw new SyntaxError(`Unexpected character at index ${i}`);\n      }\n      if (end === -1) end = i;\n      const protocol = header.slice(start, end);\n      if (protocols.has(protocol)) {\n        throw new SyntaxError(`The \"${protocol}\" subprotocol is duplicated`);\n      }\n      protocols.add(protocol);\n      start = end = -1;\n    } else {\n      throw new SyntaxError(`Unexpected character at index ${i}`);\n    }\n  }\n  if (start === -1 || end !== -1) {\n    throw new SyntaxError('Unexpected end of input');\n  }\n  const protocol = header.slice(start, i);\n  if (protocols.has(protocol)) {\n    throw new SyntaxError(`The \"${protocol}\" subprotocol is duplicated`);\n  }\n  protocols.add(protocol);\n  return protocols;\n}\nmodule.exports = {\n  parse\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/subprotocol.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/validation.js":
/*!*******************************************!*\
  !*** ./node_modules/ws/lib/validation.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst {\n  isUtf8\n} = __webpack_require__(/*! buffer */ \"buffer\");\nconst {\n  hasBlob\n} = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\n\n//\n// Allowed token characters:\n//\n// '!', '#', '$', '%', '&', ''', '*', '+', '-',\n// '.', 0-9, A-Z, '^', '_', '`', a-z, '|', '~'\n//\n// tokenChars[32] === 0 // ' '\n// tokenChars[33] === 1 // '!'\n// tokenChars[34] === 0 // '\"'\n// ...\n//\n// prettier-ignore\nconst tokenChars = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n// 0 - 15\n0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n// 16 - 31\n0, 1, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 1, 1, 0,\n// 32 - 47\n1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0,\n// 48 - 63\n0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n// 64 - 79\n1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1,\n// 80 - 95\n1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n// 96 - 111\n1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 1, 0 // 112 - 127\n];\n\n/**\n * Checks if a status code is allowed in a close frame.\n *\n * @param {Number} code The status code\n * @return {Boolean} `true` if the status code is valid, else `false`\n * @public\n */\nfunction isValidStatusCode(code) {\n  return code >= 1000 && code <= 1014 && code !== 1004 && code !== 1005 && code !== 1006 || code >= 3000 && code <= 4999;\n}\n\n/**\n * Checks if a given buffer contains only correct UTF-8.\n * Ported from https://www.cl.cam.ac.uk/%7Emgk25/ucs/utf8_check.c by\n * Markus Kuhn.\n *\n * @param {Buffer} buf The buffer to check\n * @return {Boolean} `true` if `buf` contains only correct UTF-8, else `false`\n * @public\n */\nfunction _isValidUTF8(buf) {\n  const len = buf.length;\n  let i = 0;\n  while (i < len) {\n    if ((buf[i] & 0x80) === 0) {\n      // 0xxxxxxx\n      i++;\n    } else if ((buf[i] & 0xe0) === 0xc0) {\n      // 110xxxxx 10xxxxxx\n      if (i + 1 === len || (buf[i + 1] & 0xc0) !== 0x80 || (buf[i] & 0xfe) === 0xc0 // Overlong\n      ) {\n        return false;\n      }\n      i += 2;\n    } else if ((buf[i] & 0xf0) === 0xe0) {\n      // 1110xxxx 10xxxxxx 10xxxxxx\n      if (i + 2 >= len || (buf[i + 1] & 0xc0) !== 0x80 || (buf[i + 2] & 0xc0) !== 0x80 || buf[i] === 0xe0 && (buf[i + 1] & 0xe0) === 0x80 ||\n      // Overlong\n      buf[i] === 0xed && (buf[i + 1] & 0xe0) === 0xa0 // Surrogate (U+D800 - U+DFFF)\n      ) {\n        return false;\n      }\n      i += 3;\n    } else if ((buf[i] & 0xf8) === 0xf0) {\n      // 11110xxx 10xxxxxx 10xxxxxx 10xxxxxx\n      if (i + 3 >= len || (buf[i + 1] & 0xc0) !== 0x80 || (buf[i + 2] & 0xc0) !== 0x80 || (buf[i + 3] & 0xc0) !== 0x80 || buf[i] === 0xf0 && (buf[i + 1] & 0xf0) === 0x80 ||\n      // Overlong\n      buf[i] === 0xf4 && buf[i + 1] > 0x8f || buf[i] > 0xf4 // > U+10FFFF\n      ) {\n        return false;\n      }\n      i += 4;\n    } else {\n      return false;\n    }\n  }\n  return true;\n}\n\n/**\n * Determines whether a value is a `Blob`.\n *\n * @param {*} value The value to be tested\n * @return {Boolean} `true` if `value` is a `Blob`, else `false`\n * @private\n */\nfunction isBlob(value) {\n  return hasBlob && typeof value === 'object' && typeof value.arrayBuffer === 'function' && typeof value.type === 'string' && typeof value.stream === 'function' && (value[Symbol.toStringTag] === 'Blob' || value[Symbol.toStringTag] === 'File');\n}\nmodule.exports = {\n  isBlob,\n  isValidStatusCode,\n  isValidUTF8: _isValidUTF8,\n  tokenChars\n};\nif (isUtf8) {\n  module.exports.isValidUTF8 = function (buf) {\n    return buf.length < 24 ? _isValidUTF8(buf) : isUtf8(buf);\n  };\n} /* istanbul ignore else  */else if (!process.env.WS_NO_UTF_8_VALIDATE) {\n  try {\n    const isValidUTF8 = __webpack_require__(/*! utf-8-validate */ \"?66e9\");\n    module.exports.isValidUTF8 = function (buf) {\n      return buf.length < 32 ? _isValidUTF8(buf) : isValidUTF8(buf);\n    };\n  } catch (e) {\n    // Continue regardless of the error.\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd3MvbGliL3ZhbGlkYXRpb24uanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsTUFBTTtFQUFFQTtBQUFPLENBQUMsR0FBR0MsbUJBQU8sQ0FBQyxzQkFBUSxDQUFDO0FBRXBDLE1BQU07RUFBRUM7QUFBUSxDQUFDLEdBQUdELG1CQUFPLENBQUMsNkRBQWEsQ0FBQzs7QUFFMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTUUsVUFBVSxHQUFHLENBQ2pCLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUM7QUFBRTtBQUNoRCxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDO0FBQUU7QUFDaEQsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQztBQUFFO0FBQ2hELENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUM7QUFBRTtBQUNoRCxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDO0FBQUU7QUFDaEQsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQztBQUFFO0FBQ2hELENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUM7QUFBRTtBQUNoRCxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUM7QUFBQSxDQUNoRDs7QUFFRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVNDLGlCQUFpQkEsQ0FBQ0MsSUFBSSxFQUFFO0VBQy9CLE9BQ0dBLElBQUksSUFBSSxJQUFJLElBQ1hBLElBQUksSUFBSSxJQUFJLElBQ1pBLElBQUksS0FBSyxJQUFJLElBQ2JBLElBQUksS0FBSyxJQUFJLElBQ2JBLElBQUksS0FBSyxJQUFJLElBQ2RBLElBQUksSUFBSSxJQUFJLElBQUlBLElBQUksSUFBSSxJQUFLO0FBRWxDOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVNDLFlBQVlBLENBQUNDLEdBQUcsRUFBRTtFQUN6QixNQUFNQyxHQUFHLEdBQUdELEdBQUcsQ0FBQ0UsTUFBTTtFQUN0QixJQUFJQyxDQUFDLEdBQUcsQ0FBQztFQUVULE9BQU9BLENBQUMsR0FBR0YsR0FBRyxFQUFFO0lBQ2QsSUFBSSxDQUFDRCxHQUFHLENBQUNHLENBQUMsQ0FBQyxHQUFHLElBQUksTUFBTSxDQUFDLEVBQUU7TUFDekI7TUFDQUEsQ0FBQyxFQUFFO0lBQ0wsQ0FBQyxNQUFNLElBQUksQ0FBQ0gsR0FBRyxDQUFDRyxDQUFDLENBQUMsR0FBRyxJQUFJLE1BQU0sSUFBSSxFQUFFO01BQ25DO01BQ0EsSUFDRUEsQ0FBQyxHQUFHLENBQUMsS0FBS0YsR0FBRyxJQUNiLENBQUNELEdBQUcsQ0FBQ0csQ0FBQyxHQUFHLENBQUMsQ0FBQyxHQUFHLElBQUksTUFBTSxJQUFJLElBQzVCLENBQUNILEdBQUcsQ0FBQ0csQ0FBQyxDQUFDLEdBQUcsSUFBSSxNQUFNLElBQUksQ0FBQztNQUFBLEVBQ3pCO1FBQ0EsT0FBTyxLQUFLO01BQ2Q7TUFFQUEsQ0FBQyxJQUFJLENBQUM7SUFDUixDQUFDLE1BQU0sSUFBSSxDQUFDSCxHQUFHLENBQUNHLENBQUMsQ0FBQyxHQUFHLElBQUksTUFBTSxJQUFJLEVBQUU7TUFDbkM7TUFDQSxJQUNFQSxDQUFDLEdBQUcsQ0FBQyxJQUFJRixHQUFHLElBQ1osQ0FBQ0QsR0FBRyxDQUFDRyxDQUFDLEdBQUcsQ0FBQyxDQUFDLEdBQUcsSUFBSSxNQUFNLElBQUksSUFDNUIsQ0FBQ0gsR0FBRyxDQUFDRyxDQUFDLEdBQUcsQ0FBQyxDQUFDLEdBQUcsSUFBSSxNQUFNLElBQUksSUFDM0JILEdBQUcsQ0FBQ0csQ0FBQyxDQUFDLEtBQUssSUFBSSxJQUFJLENBQUNILEdBQUcsQ0FBQ0csQ0FBQyxHQUFHLENBQUMsQ0FBQyxHQUFHLElBQUksTUFBTSxJQUFLO01BQUk7TUFDcERILEdBQUcsQ0FBQ0csQ0FBQyxDQUFDLEtBQUssSUFBSSxJQUFJLENBQUNILEdBQUcsQ0FBQ0csQ0FBQyxHQUFHLENBQUMsQ0FBQyxHQUFHLElBQUksTUFBTSxJQUFLLENBQUM7TUFBQSxFQUNsRDtRQUNBLE9BQU8sS0FBSztNQUNkO01BRUFBLENBQUMsSUFBSSxDQUFDO0lBQ1IsQ0FBQyxNQUFNLElBQUksQ0FBQ0gsR0FBRyxDQUFDRyxDQUFDLENBQUMsR0FBRyxJQUFJLE1BQU0sSUFBSSxFQUFFO01BQ25DO01BQ0EsSUFDRUEsQ0FBQyxHQUFHLENBQUMsSUFBSUYsR0FBRyxJQUNaLENBQUNELEdBQUcsQ0FBQ0csQ0FBQyxHQUFHLENBQUMsQ0FBQyxHQUFHLElBQUksTUFBTSxJQUFJLElBQzVCLENBQUNILEdBQUcsQ0FBQ0csQ0FBQyxHQUFHLENBQUMsQ0FBQyxHQUFHLElBQUksTUFBTSxJQUFJLElBQzVCLENBQUNILEdBQUcsQ0FBQ0csQ0FBQyxHQUFHLENBQUMsQ0FBQyxHQUFHLElBQUksTUFBTSxJQUFJLElBQzNCSCxHQUFHLENBQUNHLENBQUMsQ0FBQyxLQUFLLElBQUksSUFBSSxDQUFDSCxHQUFHLENBQUNHLENBQUMsR0FBRyxDQUFDLENBQUMsR0FBRyxJQUFJLE1BQU0sSUFBSztNQUFJO01BQ3BESCxHQUFHLENBQUNHLENBQUMsQ0FBQyxLQUFLLElBQUksSUFBSUgsR0FBRyxDQUFDRyxDQUFDLEdBQUcsQ0FBQyxDQUFDLEdBQUcsSUFBSyxJQUN0Q0gsR0FBRyxDQUFDRyxDQUFDLENBQUMsR0FBRyxJQUFJLENBQUM7TUFBQSxFQUNkO1FBQ0EsT0FBTyxLQUFLO01BQ2Q7TUFFQUEsQ0FBQyxJQUFJLENBQUM7SUFDUixDQUFDLE1BQU07TUFDTCxPQUFPLEtBQUs7SUFDZDtFQUNGO0VBRUEsT0FBTyxJQUFJO0FBQ2I7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTQyxNQUFNQSxDQUFDQyxLQUFLLEVBQUU7RUFDckIsT0FDRVYsT0FBTyxJQUNQLE9BQU9VLEtBQUssS0FBSyxRQUFRLElBQ3pCLE9BQU9BLEtBQUssQ0FBQ0MsV0FBVyxLQUFLLFVBQVUsSUFDdkMsT0FBT0QsS0FBSyxDQUFDRSxJQUFJLEtBQUssUUFBUSxJQUM5QixPQUFPRixLQUFLLENBQUNHLE1BQU0sS0FBSyxVQUFVLEtBQ2pDSCxLQUFLLENBQUNJLE1BQU0sQ0FBQ0MsV0FBVyxDQUFDLEtBQUssTUFBTSxJQUNuQ0wsS0FBSyxDQUFDSSxNQUFNLENBQUNDLFdBQVcsQ0FBQyxLQUFLLE1BQU0sQ0FBQztBQUUzQztBQUVBQyxNQUFNLENBQUNDLE9BQU8sR0FBRztFQUNmUixNQUFNO0VBQ05QLGlCQUFpQjtFQUNqQmdCLFdBQVcsRUFBRWQsWUFBWTtFQUN6Qkg7QUFDRixDQUFDO0FBRUQsSUFBSUgsTUFBTSxFQUFFO0VBQ1ZrQiwwQkFBMEIsR0FBRyxVQUFVWCxHQUFHLEVBQUU7SUFDMUMsT0FBT0EsR0FBRyxDQUFDRSxNQUFNLEdBQUcsRUFBRSxHQUFHSCxZQUFZLENBQUNDLEdBQUcsQ0FBQyxHQUFHUCxNQUFNLENBQUNPLEdBQUcsQ0FBQztFQUMxRCxDQUFDO0FBQ0gsQ0FBQyxDQUFDLGdDQUFpQyxJQUFJLENBQUNjLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDQyxvQkFBb0IsRUFBRTtFQUN4RSxJQUFJO0lBQ0YsTUFBTUgsV0FBVyxHQUFHbkIsbUJBQU8sQ0FBQyw2QkFBZ0IsQ0FBQztJQUU3Q2lCLDBCQUEwQixHQUFHLFVBQVVYLEdBQUcsRUFBRTtNQUMxQyxPQUFPQSxHQUFHLENBQUNFLE1BQU0sR0FBRyxFQUFFLEdBQUdILFlBQVksQ0FBQ0MsR0FBRyxDQUFDLEdBQUdhLFdBQVcsQ0FBQ2IsR0FBRyxDQUFDO0lBQy9ELENBQUM7RUFDSCxDQUFDLENBQUMsT0FBT2lCLENBQUMsRUFBRTtJQUNWO0VBQUE7QUFFSiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxuYWF0YVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxPcG9zSVxcdjE2XFxub2RlX21vZHVsZXNcXHdzXFxsaWJcXHZhbGlkYXRpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5jb25zdCB7IGlzVXRmOCB9ID0gcmVxdWlyZSgnYnVmZmVyJyk7XG5cbmNvbnN0IHsgaGFzQmxvYiB9ID0gcmVxdWlyZSgnLi9jb25zdGFudHMnKTtcblxuLy9cbi8vIEFsbG93ZWQgdG9rZW4gY2hhcmFjdGVyczpcbi8vXG4vLyAnIScsICcjJywgJyQnLCAnJScsICcmJywgJycnLCAnKicsICcrJywgJy0nLFxuLy8gJy4nLCAwLTksIEEtWiwgJ14nLCAnXycsICdgJywgYS16LCAnfCcsICd+J1xuLy9cbi8vIHRva2VuQ2hhcnNbMzJdID09PSAwIC8vICcgJ1xuLy8gdG9rZW5DaGFyc1szM10gPT09IDEgLy8gJyEnXG4vLyB0b2tlbkNoYXJzWzM0XSA9PT0gMCAvLyAnXCInXG4vLyAuLi5cbi8vXG4vLyBwcmV0dGllci1pZ25vcmVcbmNvbnN0IHRva2VuQ2hhcnMgPSBbXG4gIDAsIDAsIDAsIDAsIDAsIDAsIDAsIDAsIDAsIDAsIDAsIDAsIDAsIDAsIDAsIDAsIC8vIDAgLSAxNVxuICAwLCAwLCAwLCAwLCAwLCAwLCAwLCAwLCAwLCAwLCAwLCAwLCAwLCAwLCAwLCAwLCAvLyAxNiAtIDMxXG4gIDAsIDEsIDAsIDEsIDEsIDEsIDEsIDEsIDAsIDAsIDEsIDEsIDAsIDEsIDEsIDAsIC8vIDMyIC0gNDdcbiAgMSwgMSwgMSwgMSwgMSwgMSwgMSwgMSwgMSwgMSwgMCwgMCwgMCwgMCwgMCwgMCwgLy8gNDggLSA2M1xuICAwLCAxLCAxLCAxLCAxLCAxLCAxLCAxLCAxLCAxLCAxLCAxLCAxLCAxLCAxLCAxLCAvLyA2NCAtIDc5XG4gIDEsIDEsIDEsIDEsIDEsIDEsIDEsIDEsIDEsIDEsIDEsIDAsIDAsIDAsIDEsIDEsIC8vIDgwIC0gOTVcbiAgMSwgMSwgMSwgMSwgMSwgMSwgMSwgMSwgMSwgMSwgMSwgMSwgMSwgMSwgMSwgMSwgLy8gOTYgLSAxMTFcbiAgMSwgMSwgMSwgMSwgMSwgMSwgMSwgMSwgMSwgMSwgMSwgMCwgMSwgMCwgMSwgMCAvLyAxMTIgLSAxMjdcbl07XG5cbi8qKlxuICogQ2hlY2tzIGlmIGEgc3RhdHVzIGNvZGUgaXMgYWxsb3dlZCBpbiBhIGNsb3NlIGZyYW1lLlxuICpcbiAqIEBwYXJhbSB7TnVtYmVyfSBjb2RlIFRoZSBzdGF0dXMgY29kZVxuICogQHJldHVybiB7Qm9vbGVhbn0gYHRydWVgIGlmIHRoZSBzdGF0dXMgY29kZSBpcyB2YWxpZCwgZWxzZSBgZmFsc2VgXG4gKiBAcHVibGljXG4gKi9cbmZ1bmN0aW9uIGlzVmFsaWRTdGF0dXNDb2RlKGNvZGUpIHtcbiAgcmV0dXJuIChcbiAgICAoY29kZSA+PSAxMDAwICYmXG4gICAgICBjb2RlIDw9IDEwMTQgJiZcbiAgICAgIGNvZGUgIT09IDEwMDQgJiZcbiAgICAgIGNvZGUgIT09IDEwMDUgJiZcbiAgICAgIGNvZGUgIT09IDEwMDYpIHx8XG4gICAgKGNvZGUgPj0gMzAwMCAmJiBjb2RlIDw9IDQ5OTkpXG4gICk7XG59XG5cbi8qKlxuICogQ2hlY2tzIGlmIGEgZ2l2ZW4gYnVmZmVyIGNvbnRhaW5zIG9ubHkgY29ycmVjdCBVVEYtOC5cbiAqIFBvcnRlZCBmcm9tIGh0dHBzOi8vd3d3LmNsLmNhbS5hYy51ay8lN0VtZ2syNS91Y3MvdXRmOF9jaGVjay5jIGJ5XG4gKiBNYXJrdXMgS3Vobi5cbiAqXG4gKiBAcGFyYW0ge0J1ZmZlcn0gYnVmIFRoZSBidWZmZXIgdG8gY2hlY2tcbiAqIEByZXR1cm4ge0Jvb2xlYW59IGB0cnVlYCBpZiBgYnVmYCBjb250YWlucyBvbmx5IGNvcnJlY3QgVVRGLTgsIGVsc2UgYGZhbHNlYFxuICogQHB1YmxpY1xuICovXG5mdW5jdGlvbiBfaXNWYWxpZFVURjgoYnVmKSB7XG4gIGNvbnN0IGxlbiA9IGJ1Zi5sZW5ndGg7XG4gIGxldCBpID0gMDtcblxuICB3aGlsZSAoaSA8IGxlbikge1xuICAgIGlmICgoYnVmW2ldICYgMHg4MCkgPT09IDApIHtcbiAgICAgIC8vIDB4eHh4eHh4XG4gICAgICBpKys7XG4gICAgfSBlbHNlIGlmICgoYnVmW2ldICYgMHhlMCkgPT09IDB4YzApIHtcbiAgICAgIC8vIDExMHh4eHh4IDEweHh4eHh4XG4gICAgICBpZiAoXG4gICAgICAgIGkgKyAxID09PSBsZW4gfHxcbiAgICAgICAgKGJ1ZltpICsgMV0gJiAweGMwKSAhPT0gMHg4MCB8fFxuICAgICAgICAoYnVmW2ldICYgMHhmZSkgPT09IDB4YzAgLy8gT3ZlcmxvbmdcbiAgICAgICkge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICB9XG5cbiAgICAgIGkgKz0gMjtcbiAgICB9IGVsc2UgaWYgKChidWZbaV0gJiAweGYwKSA9PT0gMHhlMCkge1xuICAgICAgLy8gMTExMHh4eHggMTB4eHh4eHggMTB4eHh4eHhcbiAgICAgIGlmIChcbiAgICAgICAgaSArIDIgPj0gbGVuIHx8XG4gICAgICAgIChidWZbaSArIDFdICYgMHhjMCkgIT09IDB4ODAgfHxcbiAgICAgICAgKGJ1ZltpICsgMl0gJiAweGMwKSAhPT0gMHg4MCB8fFxuICAgICAgICAoYnVmW2ldID09PSAweGUwICYmIChidWZbaSArIDFdICYgMHhlMCkgPT09IDB4ODApIHx8IC8vIE92ZXJsb25nXG4gICAgICAgIChidWZbaV0gPT09IDB4ZWQgJiYgKGJ1ZltpICsgMV0gJiAweGUwKSA9PT0gMHhhMCkgLy8gU3Vycm9nYXRlIChVK0Q4MDAgLSBVK0RGRkYpXG4gICAgICApIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgfVxuXG4gICAgICBpICs9IDM7XG4gICAgfSBlbHNlIGlmICgoYnVmW2ldICYgMHhmOCkgPT09IDB4ZjApIHtcbiAgICAgIC8vIDExMTEweHh4IDEweHh4eHh4IDEweHh4eHh4IDEweHh4eHh4XG4gICAgICBpZiAoXG4gICAgICAgIGkgKyAzID49IGxlbiB8fFxuICAgICAgICAoYnVmW2kgKyAxXSAmIDB4YzApICE9PSAweDgwIHx8XG4gICAgICAgIChidWZbaSArIDJdICYgMHhjMCkgIT09IDB4ODAgfHxcbiAgICAgICAgKGJ1ZltpICsgM10gJiAweGMwKSAhPT0gMHg4MCB8fFxuICAgICAgICAoYnVmW2ldID09PSAweGYwICYmIChidWZbaSArIDFdICYgMHhmMCkgPT09IDB4ODApIHx8IC8vIE92ZXJsb25nXG4gICAgICAgIChidWZbaV0gPT09IDB4ZjQgJiYgYnVmW2kgKyAxXSA+IDB4OGYpIHx8XG4gICAgICAgIGJ1ZltpXSA+IDB4ZjQgLy8gPiBVKzEwRkZGRlxuICAgICAgKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgIH1cblxuICAgICAgaSArPSA0O1xuICAgIH0gZWxzZSB7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIHRydWU7XG59XG5cbi8qKlxuICogRGV0ZXJtaW5lcyB3aGV0aGVyIGEgdmFsdWUgaXMgYSBgQmxvYmAuXG4gKlxuICogQHBhcmFtIHsqfSB2YWx1ZSBUaGUgdmFsdWUgdG8gYmUgdGVzdGVkXG4gKiBAcmV0dXJuIHtCb29sZWFufSBgdHJ1ZWAgaWYgYHZhbHVlYCBpcyBhIGBCbG9iYCwgZWxzZSBgZmFsc2VgXG4gKiBAcHJpdmF0ZVxuICovXG5mdW5jdGlvbiBpc0Jsb2IodmFsdWUpIHtcbiAgcmV0dXJuIChcbiAgICBoYXNCbG9iICYmXG4gICAgdHlwZW9mIHZhbHVlID09PSAnb2JqZWN0JyAmJlxuICAgIHR5cGVvZiB2YWx1ZS5hcnJheUJ1ZmZlciA9PT0gJ2Z1bmN0aW9uJyAmJlxuICAgIHR5cGVvZiB2YWx1ZS50eXBlID09PSAnc3RyaW5nJyAmJlxuICAgIHR5cGVvZiB2YWx1ZS5zdHJlYW0gPT09ICdmdW5jdGlvbicgJiZcbiAgICAodmFsdWVbU3ltYm9sLnRvU3RyaW5nVGFnXSA9PT0gJ0Jsb2InIHx8XG4gICAgICB2YWx1ZVtTeW1ib2wudG9TdHJpbmdUYWddID09PSAnRmlsZScpXG4gICk7XG59XG5cbm1vZHVsZS5leHBvcnRzID0ge1xuICBpc0Jsb2IsXG4gIGlzVmFsaWRTdGF0dXNDb2RlLFxuICBpc1ZhbGlkVVRGODogX2lzVmFsaWRVVEY4LFxuICB0b2tlbkNoYXJzXG59O1xuXG5pZiAoaXNVdGY4KSB7XG4gIG1vZHVsZS5leHBvcnRzLmlzVmFsaWRVVEY4ID0gZnVuY3Rpb24gKGJ1Zikge1xuICAgIHJldHVybiBidWYubGVuZ3RoIDwgMjQgPyBfaXNWYWxpZFVURjgoYnVmKSA6IGlzVXRmOChidWYpO1xuICB9O1xufSAvKiBpc3RhbmJ1bCBpZ25vcmUgZWxzZSAgKi8gZWxzZSBpZiAoIXByb2Nlc3MuZW52LldTX05PX1VURl84X1ZBTElEQVRFKSB7XG4gIHRyeSB7XG4gICAgY29uc3QgaXNWYWxpZFVURjggPSByZXF1aXJlKCd1dGYtOC12YWxpZGF0ZScpO1xuXG4gICAgbW9kdWxlLmV4cG9ydHMuaXNWYWxpZFVURjggPSBmdW5jdGlvbiAoYnVmKSB7XG4gICAgICByZXR1cm4gYnVmLmxlbmd0aCA8IDMyID8gX2lzVmFsaWRVVEY4KGJ1ZikgOiBpc1ZhbGlkVVRGOChidWYpO1xuICAgIH07XG4gIH0gY2F0Y2ggKGUpIHtcbiAgICAvLyBDb250aW51ZSByZWdhcmRsZXNzIG9mIHRoZSBlcnJvci5cbiAgfVxufVxuIl0sIm5hbWVzIjpbImlzVXRmOCIsInJlcXVpcmUiLCJoYXNCbG9iIiwidG9rZW5DaGFycyIsImlzVmFsaWRTdGF0dXNDb2RlIiwiY29kZSIsIl9pc1ZhbGlkVVRGOCIsImJ1ZiIsImxlbiIsImxlbmd0aCIsImkiLCJpc0Jsb2IiLCJ2YWx1ZSIsImFycmF5QnVmZmVyIiwidHlwZSIsInN0cmVhbSIsIlN5bWJvbCIsInRvU3RyaW5nVGFnIiwibW9kdWxlIiwiZXhwb3J0cyIsImlzVmFsaWRVVEY4IiwicHJvY2VzcyIsImVudiIsIldTX05PX1VURl84X1ZBTElEQVRFIiwiZSJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/validation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/websocket-server.js":
/*!*************************************************!*\
  !*** ./node_modules/ws/lib/websocket-server.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/* eslint no-unused-vars: [\"error\", { \"varsIgnorePattern\": \"^Duplex$\", \"caughtErrors\": \"none\" }] */\n\n\n\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return typeof key === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (typeof input !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (typeof res !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nconst EventEmitter = __webpack_require__(/*! events */ \"events\");\nconst http = __webpack_require__(/*! http */ \"http\");\nconst {\n  Duplex\n} = __webpack_require__(/*! stream */ \"stream\");\nconst {\n  createHash\n} = __webpack_require__(/*! crypto */ \"crypto\");\nconst extension = __webpack_require__(/*! ./extension */ \"(ssr)/./node_modules/ws/lib/extension.js\");\nconst PerMessageDeflate = __webpack_require__(/*! ./permessage-deflate */ \"(ssr)/./node_modules/ws/lib/permessage-deflate.js\");\nconst subprotocol = __webpack_require__(/*! ./subprotocol */ \"(ssr)/./node_modules/ws/lib/subprotocol.js\");\nconst WebSocket = __webpack_require__(/*! ./websocket */ \"(ssr)/./node_modules/ws/lib/websocket.js\");\nconst {\n  GUID,\n  kWebSocket\n} = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\nconst keyRegex = /^[+/0-9A-Za-z]{22}==$/;\nconst RUNNING = 0;\nconst CLOSING = 1;\nconst CLOSED = 2;\n\n/**\n * Class representing a WebSocket server.\n *\n * @extends EventEmitter\n */\nclass WebSocketServer extends EventEmitter {\n  /**\n   * Create a `WebSocketServer` instance.\n   *\n   * @param {Object} options Configuration options\n   * @param {Boolean} [options.allowSynchronousEvents=true] Specifies whether\n   *     any of the `'message'`, `'ping'`, and `'pong'` events can be emitted\n   *     multiple times in the same tick\n   * @param {Boolean} [options.autoPong=true] Specifies whether or not to\n   *     automatically send a pong in response to a ping\n   * @param {Number} [options.backlog=511] The maximum length of the queue of\n   *     pending connections\n   * @param {Boolean} [options.clientTracking=true] Specifies whether or not to\n   *     track clients\n   * @param {Function} [options.handleProtocols] A hook to handle protocols\n   * @param {String} [options.host] The hostname where to bind the server\n   * @param {Number} [options.maxPayload=104857600] The maximum allowed message\n   *     size\n   * @param {Boolean} [options.noServer=false] Enable no server mode\n   * @param {String} [options.path] Accept only connections matching this path\n   * @param {(Boolean|Object)} [options.perMessageDeflate=false] Enable/disable\n   *     permessage-deflate\n   * @param {Number} [options.port] The port where to bind the server\n   * @param {(http.Server|https.Server)} [options.server] A pre-created HTTP/S\n   *     server to use\n   * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or\n   *     not to skip UTF-8 validation for text and close messages\n   * @param {Function} [options.verifyClient] A hook to reject connections\n   * @param {Function} [options.WebSocket=WebSocket] Specifies the `WebSocket`\n   *     class to use. It must be the `WebSocket` class or class that extends it\n   * @param {Function} [callback] A listener for the `listening` event\n   */\n  constructor(options, callback) {\n    super();\n    options = _objectSpread({\n      allowSynchronousEvents: true,\n      autoPong: true,\n      maxPayload: 100 * 1024 * 1024,\n      skipUTF8Validation: false,\n      perMessageDeflate: false,\n      handleProtocols: null,\n      clientTracking: true,\n      verifyClient: null,\n      noServer: false,\n      backlog: null,\n      // use default (511 as implemented in net.js)\n      server: null,\n      host: null,\n      path: null,\n      port: null,\n      WebSocket\n    }, options);\n    if (options.port == null && !options.server && !options.noServer || options.port != null && (options.server || options.noServer) || options.server && options.noServer) {\n      throw new TypeError('One and only one of the \"port\", \"server\", or \"noServer\" options ' + 'must be specified');\n    }\n    if (options.port != null) {\n      this._server = http.createServer((req, res) => {\n        const body = http.STATUS_CODES[426];\n        res.writeHead(426, {\n          'Content-Length': body.length,\n          'Content-Type': 'text/plain'\n        });\n        res.end(body);\n      });\n      this._server.listen(options.port, options.host, options.backlog, callback);\n    } else if (options.server) {\n      this._server = options.server;\n    }\n    if (this._server) {\n      const emitConnection = this.emit.bind(this, 'connection');\n      this._removeListeners = addListeners(this._server, {\n        listening: this.emit.bind(this, 'listening'),\n        error: this.emit.bind(this, 'error'),\n        upgrade: (req, socket, head) => {\n          this.handleUpgrade(req, socket, head, emitConnection);\n        }\n      });\n    }\n    if (options.perMessageDeflate === true) options.perMessageDeflate = {};\n    if (options.clientTracking) {\n      this.clients = new Set();\n      this._shouldEmitClose = false;\n    }\n    this.options = options;\n    this._state = RUNNING;\n  }\n\n  /**\n   * Returns the bound address, the address family name, and port of the server\n   * as reported by the operating system if listening on an IP socket.\n   * If the server is listening on a pipe or UNIX domain socket, the name is\n   * returned as a string.\n   *\n   * @return {(Object|String|null)} The address of the server\n   * @public\n   */\n  address() {\n    if (this.options.noServer) {\n      throw new Error('The server is operating in \"noServer\" mode');\n    }\n    if (!this._server) return null;\n    return this._server.address();\n  }\n\n  /**\n   * Stop the server from accepting new connections and emit the `'close'` event\n   * when all existing connections are closed.\n   *\n   * @param {Function} [cb] A one-time listener for the `'close'` event\n   * @public\n   */\n  close(cb) {\n    if (this._state === CLOSED) {\n      if (cb) {\n        this.once('close', () => {\n          cb(new Error('The server is not running'));\n        });\n      }\n      process.nextTick(emitClose, this);\n      return;\n    }\n    if (cb) this.once('close', cb);\n    if (this._state === CLOSING) return;\n    this._state = CLOSING;\n    if (this.options.noServer || this.options.server) {\n      if (this._server) {\n        this._removeListeners();\n        this._removeListeners = this._server = null;\n      }\n      if (this.clients) {\n        if (!this.clients.size) {\n          process.nextTick(emitClose, this);\n        } else {\n          this._shouldEmitClose = true;\n        }\n      } else {\n        process.nextTick(emitClose, this);\n      }\n    } else {\n      const server = this._server;\n      this._removeListeners();\n      this._removeListeners = this._server = null;\n\n      //\n      // The HTTP/S server was created internally. Close it, and rely on its\n      // `'close'` event.\n      //\n      server.close(() => {\n        emitClose(this);\n      });\n    }\n  }\n\n  /**\n   * See if a given request should be handled by this server instance.\n   *\n   * @param {http.IncomingMessage} req Request object to inspect\n   * @return {Boolean} `true` if the request is valid, else `false`\n   * @public\n   */\n  shouldHandle(req) {\n    if (this.options.path) {\n      const index = req.url.indexOf('?');\n      const pathname = index !== -1 ? req.url.slice(0, index) : req.url;\n      if (pathname !== this.options.path) return false;\n    }\n    return true;\n  }\n\n  /**\n   * Handle a HTTP Upgrade request.\n   *\n   * @param {http.IncomingMessage} req The request object\n   * @param {Duplex} socket The network socket between the server and client\n   * @param {Buffer} head The first packet of the upgraded stream\n   * @param {Function} cb Callback\n   * @public\n   */\n  handleUpgrade(req, socket, head, cb) {\n    socket.on('error', socketOnError);\n    const key = req.headers['sec-websocket-key'];\n    const upgrade = req.headers.upgrade;\n    const version = +req.headers['sec-websocket-version'];\n    if (req.method !== 'GET') {\n      const message = 'Invalid HTTP method';\n      abortHandshakeOrEmitwsClientError(this, req, socket, 405, message);\n      return;\n    }\n    if (upgrade === undefined || upgrade.toLowerCase() !== 'websocket') {\n      const message = 'Invalid Upgrade header';\n      abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n      return;\n    }\n    if (key === undefined || !keyRegex.test(key)) {\n      const message = 'Missing or invalid Sec-WebSocket-Key header';\n      abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n      return;\n    }\n    if (version !== 8 && version !== 13) {\n      const message = 'Missing or invalid Sec-WebSocket-Version header';\n      abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n      return;\n    }\n    if (!this.shouldHandle(req)) {\n      abortHandshake(socket, 400);\n      return;\n    }\n    const secWebSocketProtocol = req.headers['sec-websocket-protocol'];\n    let protocols = new Set();\n    if (secWebSocketProtocol !== undefined) {\n      try {\n        protocols = subprotocol.parse(secWebSocketProtocol);\n      } catch (err) {\n        const message = 'Invalid Sec-WebSocket-Protocol header';\n        abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n        return;\n      }\n    }\n    const secWebSocketExtensions = req.headers['sec-websocket-extensions'];\n    const extensions = {};\n    if (this.options.perMessageDeflate && secWebSocketExtensions !== undefined) {\n      const perMessageDeflate = new PerMessageDeflate(this.options.perMessageDeflate, true, this.options.maxPayload);\n      try {\n        const offers = extension.parse(secWebSocketExtensions);\n        if (offers[PerMessageDeflate.extensionName]) {\n          perMessageDeflate.accept(offers[PerMessageDeflate.extensionName]);\n          extensions[PerMessageDeflate.extensionName] = perMessageDeflate;\n        }\n      } catch (err) {\n        const message = 'Invalid or unacceptable Sec-WebSocket-Extensions header';\n        abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n        return;\n      }\n    }\n\n    //\n    // Optionally call external client verification handler.\n    //\n    if (this.options.verifyClient) {\n      const info = {\n        origin: req.headers[`${version === 8 ? 'sec-websocket-origin' : 'origin'}`],\n        secure: !!(req.socket.authorized || req.socket.encrypted),\n        req\n      };\n      if (this.options.verifyClient.length === 2) {\n        this.options.verifyClient(info, (verified, code, message, headers) => {\n          if (!verified) {\n            return abortHandshake(socket, code || 401, message, headers);\n          }\n          this.completeUpgrade(extensions, key, protocols, req, socket, head, cb);\n        });\n        return;\n      }\n      if (!this.options.verifyClient(info)) return abortHandshake(socket, 401);\n    }\n    this.completeUpgrade(extensions, key, protocols, req, socket, head, cb);\n  }\n\n  /**\n   * Upgrade the connection to WebSocket.\n   *\n   * @param {Object} extensions The accepted extensions\n   * @param {String} key The value of the `Sec-WebSocket-Key` header\n   * @param {Set} protocols The subprotocols\n   * @param {http.IncomingMessage} req The request object\n   * @param {Duplex} socket The network socket between the server and client\n   * @param {Buffer} head The first packet of the upgraded stream\n   * @param {Function} cb Callback\n   * @throws {Error} If called more than once with the same socket\n   * @private\n   */\n  completeUpgrade(extensions, key, protocols, req, socket, head, cb) {\n    //\n    // Destroy the socket if the client has already sent a FIN packet.\n    //\n    if (!socket.readable || !socket.writable) return socket.destroy();\n    if (socket[kWebSocket]) {\n      throw new Error('server.handleUpgrade() was called more than once with the same ' + 'socket, possibly due to a misconfiguration');\n    }\n    if (this._state > RUNNING) return abortHandshake(socket, 503);\n    const digest = createHash('sha1').update(key + GUID).digest('base64');\n    const headers = ['HTTP/1.1 101 Switching Protocols', 'Upgrade: websocket', 'Connection: Upgrade', `Sec-WebSocket-Accept: ${digest}`];\n    const ws = new this.options.WebSocket(null, undefined, this.options);\n    if (protocols.size) {\n      //\n      // Optionally call external protocol selection handler.\n      //\n      const protocol = this.options.handleProtocols ? this.options.handleProtocols(protocols, req) : protocols.values().next().value;\n      if (protocol) {\n        headers.push(`Sec-WebSocket-Protocol: ${protocol}`);\n        ws._protocol = protocol;\n      }\n    }\n    if (extensions[PerMessageDeflate.extensionName]) {\n      const params = extensions[PerMessageDeflate.extensionName].params;\n      const value = extension.format({\n        [PerMessageDeflate.extensionName]: [params]\n      });\n      headers.push(`Sec-WebSocket-Extensions: ${value}`);\n      ws._extensions = extensions;\n    }\n\n    //\n    // Allow external modification/inspection of handshake headers.\n    //\n    this.emit('headers', headers, req);\n    socket.write(headers.concat('\\r\\n').join('\\r\\n'));\n    socket.removeListener('error', socketOnError);\n    ws.setSocket(socket, head, {\n      allowSynchronousEvents: this.options.allowSynchronousEvents,\n      maxPayload: this.options.maxPayload,\n      skipUTF8Validation: this.options.skipUTF8Validation\n    });\n    if (this.clients) {\n      this.clients.add(ws);\n      ws.on('close', () => {\n        this.clients.delete(ws);\n        if (this._shouldEmitClose && !this.clients.size) {\n          process.nextTick(emitClose, this);\n        }\n      });\n    }\n    cb(ws, req);\n  }\n}\nmodule.exports = WebSocketServer;\n\n/**\n * Add event listeners on an `EventEmitter` using a map of <event, listener>\n * pairs.\n *\n * @param {EventEmitter} server The event emitter\n * @param {Object.<String, Function>} map The listeners to add\n * @return {Function} A function that will remove the added listeners when\n *     called\n * @private\n */\nfunction addListeners(server, map) {\n  for (const event of Object.keys(map)) server.on(event, map[event]);\n  return function removeListeners() {\n    for (const event of Object.keys(map)) {\n      server.removeListener(event, map[event]);\n    }\n  };\n}\n\n/**\n * Emit a `'close'` event on an `EventEmitter`.\n *\n * @param {EventEmitter} server The event emitter\n * @private\n */\nfunction emitClose(server) {\n  server._state = CLOSED;\n  server.emit('close');\n}\n\n/**\n * Handle socket errors.\n *\n * @private\n */\nfunction socketOnError() {\n  this.destroy();\n}\n\n/**\n * Close the connection when preconditions are not fulfilled.\n *\n * @param {Duplex} socket The socket of the upgrade request\n * @param {Number} code The HTTP response status code\n * @param {String} [message] The HTTP response body\n * @param {Object} [headers] Additional HTTP response headers\n * @private\n */\nfunction abortHandshake(socket, code, message, headers) {\n  //\n  // The socket is writable unless the user destroyed or ended it before calling\n  // `server.handleUpgrade()` or in the `verifyClient` function, which is a user\n  // error. Handling this does not make much sense as the worst that can happen\n  // is that some of the data written by the user might be discarded due to the\n  // call to `socket.end()` below, which triggers an `'error'` event that in\n  // turn causes the socket to be destroyed.\n  //\n  message = message || http.STATUS_CODES[code];\n  headers = _objectSpread({\n    Connection: 'close',\n    'Content-Type': 'text/html',\n    'Content-Length': Buffer.byteLength(message)\n  }, headers);\n  socket.once('finish', socket.destroy);\n  socket.end(`HTTP/1.1 ${code} ${http.STATUS_CODES[code]}\\r\\n` + Object.keys(headers).map(h => `${h}: ${headers[h]}`).join('\\r\\n') + '\\r\\n\\r\\n' + message);\n}\n\n/**\n * Emit a `'wsClientError'` event on a `WebSocketServer` if there is at least\n * one listener for it, otherwise call `abortHandshake()`.\n *\n * @param {WebSocketServer} server The WebSocket server\n * @param {http.IncomingMessage} req The request object\n * @param {Duplex} socket The socket of the upgrade request\n * @param {Number} code The HTTP response status code\n * @param {String} message The HTTP response body\n * @private\n */\nfunction abortHandshakeOrEmitwsClientError(server, req, socket, code, message) {\n  if (server.listenerCount('wsClientError')) {\n    const err = new Error(message);\n    Error.captureStackTrace(err, abortHandshakeOrEmitwsClientError);\n    server.emit('wsClientError', err, socket, req);\n  } else {\n    abortHandshake(socket, code, message);\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/websocket-server.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/websocket.js":
/*!******************************************!*\
  !*** ./node_modules/ws/lib/websocket.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/* eslint no-unused-vars: [\"error\", { \"varsIgnorePattern\": \"^Duplex|Readable$\", \"caughtErrors\": \"none\" }] */\n\n\n\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return typeof key === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (typeof input !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (typeof res !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nconst EventEmitter = __webpack_require__(/*! events */ \"events\");\nconst https = __webpack_require__(/*! https */ \"https\");\nconst http = __webpack_require__(/*! http */ \"http\");\nconst net = __webpack_require__(/*! net */ \"net\");\nconst tls = __webpack_require__(/*! tls */ \"tls\");\nconst {\n  randomBytes,\n  createHash\n} = __webpack_require__(/*! crypto */ \"crypto\");\nconst {\n  Duplex,\n  Readable\n} = __webpack_require__(/*! stream */ \"stream\");\nconst {\n  URL\n} = __webpack_require__(/*! url */ \"url\");\nconst PerMessageDeflate = __webpack_require__(/*! ./permessage-deflate */ \"(ssr)/./node_modules/ws/lib/permessage-deflate.js\");\nconst Receiver = __webpack_require__(/*! ./receiver */ \"(ssr)/./node_modules/ws/lib/receiver.js\");\nconst Sender = __webpack_require__(/*! ./sender */ \"(ssr)/./node_modules/ws/lib/sender.js\");\nconst {\n  isBlob\n} = __webpack_require__(/*! ./validation */ \"(ssr)/./node_modules/ws/lib/validation.js\");\nconst {\n  BINARY_TYPES,\n  EMPTY_BUFFER,\n  GUID,\n  kForOnEventAttribute,\n  kListener,\n  kStatusCode,\n  kWebSocket,\n  NOOP\n} = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\nconst {\n  EventTarget: {\n    addEventListener,\n    removeEventListener\n  }\n} = __webpack_require__(/*! ./event-target */ \"(ssr)/./node_modules/ws/lib/event-target.js\");\nconst {\n  format,\n  parse\n} = __webpack_require__(/*! ./extension */ \"(ssr)/./node_modules/ws/lib/extension.js\");\nconst {\n  toBuffer\n} = __webpack_require__(/*! ./buffer-util */ \"(ssr)/./node_modules/ws/lib/buffer-util.js\");\nconst closeTimeout = 30 * 1000;\nconst kAborted = Symbol('kAborted');\nconst protocolVersions = [8, 13];\nconst readyStates = ['CONNECTING', 'OPEN', 'CLOSING', 'CLOSED'];\nconst subprotocolRegex = /^[!#$%&'*+\\-.0-9A-Z^_`|a-z~]+$/;\n\n/**\n * Class representing a WebSocket.\n *\n * @extends EventEmitter\n */\nclass WebSocket extends EventEmitter {\n  /**\n   * Create a new `WebSocket`.\n   *\n   * @param {(String|URL)} address The URL to which to connect\n   * @param {(String|String[])} [protocols] The subprotocols\n   * @param {Object} [options] Connection options\n   */\n  constructor(address, protocols, options) {\n    super();\n    this._binaryType = BINARY_TYPES[0];\n    this._closeCode = 1006;\n    this._closeFrameReceived = false;\n    this._closeFrameSent = false;\n    this._closeMessage = EMPTY_BUFFER;\n    this._closeTimer = null;\n    this._errorEmitted = false;\n    this._extensions = {};\n    this._paused = false;\n    this._protocol = '';\n    this._readyState = WebSocket.CONNECTING;\n    this._receiver = null;\n    this._sender = null;\n    this._socket = null;\n    if (address !== null) {\n      this._bufferedAmount = 0;\n      this._isServer = false;\n      this._redirects = 0;\n      if (protocols === undefined) {\n        protocols = [];\n      } else if (!Array.isArray(protocols)) {\n        if (typeof protocols === 'object' && protocols !== null) {\n          options = protocols;\n          protocols = [];\n        } else {\n          protocols = [protocols];\n        }\n      }\n      initAsClient(this, address, protocols, options);\n    } else {\n      this._autoPong = options.autoPong;\n      this._isServer = true;\n    }\n  }\n\n  /**\n   * For historical reasons, the custom \"nodebuffer\" type is used by the default\n   * instead of \"blob\".\n   *\n   * @type {String}\n   */\n  get binaryType() {\n    return this._binaryType;\n  }\n  set binaryType(type) {\n    if (!BINARY_TYPES.includes(type)) return;\n    this._binaryType = type;\n\n    //\n    // Allow to change `binaryType` on the fly.\n    //\n    if (this._receiver) this._receiver._binaryType = type;\n  }\n\n  /**\n   * @type {Number}\n   */\n  get bufferedAmount() {\n    if (!this._socket) return this._bufferedAmount;\n    return this._socket._writableState.length + this._sender._bufferedBytes;\n  }\n\n  /**\n   * @type {String}\n   */\n  get extensions() {\n    return Object.keys(this._extensions).join();\n  }\n\n  /**\n   * @type {Boolean}\n   */\n  get isPaused() {\n    return this._paused;\n  }\n\n  /**\n   * @type {Function}\n   */\n  /* istanbul ignore next */\n  get onclose() {\n    return null;\n  }\n\n  /**\n   * @type {Function}\n   */\n  /* istanbul ignore next */\n  get onerror() {\n    return null;\n  }\n\n  /**\n   * @type {Function}\n   */\n  /* istanbul ignore next */\n  get onopen() {\n    return null;\n  }\n\n  /**\n   * @type {Function}\n   */\n  /* istanbul ignore next */\n  get onmessage() {\n    return null;\n  }\n\n  /**\n   * @type {String}\n   */\n  get protocol() {\n    return this._protocol;\n  }\n\n  /**\n   * @type {Number}\n   */\n  get readyState() {\n    return this._readyState;\n  }\n\n  /**\n   * @type {String}\n   */\n  get url() {\n    return this._url;\n  }\n\n  /**\n   * Set up the socket and the internal resources.\n   *\n   * @param {Duplex} socket The network socket between the server and client\n   * @param {Buffer} head The first packet of the upgraded stream\n   * @param {Object} options Options object\n   * @param {Boolean} [options.allowSynchronousEvents=false] Specifies whether\n   *     any of the `'message'`, `'ping'`, and `'pong'` events can be emitted\n   *     multiple times in the same tick\n   * @param {Function} [options.generateMask] The function used to generate the\n   *     masking key\n   * @param {Number} [options.maxPayload=0] The maximum allowed message size\n   * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or\n   *     not to skip UTF-8 validation for text and close messages\n   * @private\n   */\n  setSocket(socket, head, options) {\n    const receiver = new Receiver({\n      allowSynchronousEvents: options.allowSynchronousEvents,\n      binaryType: this.binaryType,\n      extensions: this._extensions,\n      isServer: this._isServer,\n      maxPayload: options.maxPayload,\n      skipUTF8Validation: options.skipUTF8Validation\n    });\n    const sender = new Sender(socket, this._extensions, options.generateMask);\n    this._receiver = receiver;\n    this._sender = sender;\n    this._socket = socket;\n    receiver[kWebSocket] = this;\n    sender[kWebSocket] = this;\n    socket[kWebSocket] = this;\n    receiver.on('conclude', receiverOnConclude);\n    receiver.on('drain', receiverOnDrain);\n    receiver.on('error', receiverOnError);\n    receiver.on('message', receiverOnMessage);\n    receiver.on('ping', receiverOnPing);\n    receiver.on('pong', receiverOnPong);\n    sender.onerror = senderOnError;\n\n    //\n    // These methods may not be available if `socket` is just a `Duplex`.\n    //\n    if (socket.setTimeout) socket.setTimeout(0);\n    if (socket.setNoDelay) socket.setNoDelay();\n    if (head.length > 0) socket.unshift(head);\n    socket.on('close', socketOnClose);\n    socket.on('data', socketOnData);\n    socket.on('end', socketOnEnd);\n    socket.on('error', socketOnError);\n    this._readyState = WebSocket.OPEN;\n    this.emit('open');\n  }\n\n  /**\n   * Emit the `'close'` event.\n   *\n   * @private\n   */\n  emitClose() {\n    if (!this._socket) {\n      this._readyState = WebSocket.CLOSED;\n      this.emit('close', this._closeCode, this._closeMessage);\n      return;\n    }\n    if (this._extensions[PerMessageDeflate.extensionName]) {\n      this._extensions[PerMessageDeflate.extensionName].cleanup();\n    }\n    this._receiver.removeAllListeners();\n    this._readyState = WebSocket.CLOSED;\n    this.emit('close', this._closeCode, this._closeMessage);\n  }\n\n  /**\n   * Start a closing handshake.\n   *\n   *          +----------+   +-----------+   +----------+\n   *     - - -|ws.close()|-->|close frame|-->|ws.close()|- - -\n   *    |     +----------+   +-----------+   +----------+     |\n   *          +----------+   +-----------+         |\n   * CLOSING  |ws.close()|<--|close frame|<--+-----+       CLOSING\n   *          +----------+   +-----------+   |\n   *    |           |                        |   +---+        |\n   *                +------------------------+-->|fin| - - - -\n   *    |         +---+                      |   +---+\n   *     - - - - -|fin|<---------------------+\n   *              +---+\n   *\n   * @param {Number} [code] Status code explaining why the connection is closing\n   * @param {(String|Buffer)} [data] The reason why the connection is\n   *     closing\n   * @public\n   */\n  close(code, data) {\n    if (this.readyState === WebSocket.CLOSED) return;\n    if (this.readyState === WebSocket.CONNECTING) {\n      const msg = 'WebSocket was closed before the connection was established';\n      abortHandshake(this, this._req, msg);\n      return;\n    }\n    if (this.readyState === WebSocket.CLOSING) {\n      if (this._closeFrameSent && (this._closeFrameReceived || this._receiver._writableState.errorEmitted)) {\n        this._socket.end();\n      }\n      return;\n    }\n    this._readyState = WebSocket.CLOSING;\n    this._sender.close(code, data, !this._isServer, err => {\n      //\n      // This error is handled by the `'error'` listener on the socket. We only\n      // want to know if the close frame has been sent here.\n      //\n      if (err) return;\n      this._closeFrameSent = true;\n      if (this._closeFrameReceived || this._receiver._writableState.errorEmitted) {\n        this._socket.end();\n      }\n    });\n    setCloseTimer(this);\n  }\n\n  /**\n   * Pause the socket.\n   *\n   * @public\n   */\n  pause() {\n    if (this.readyState === WebSocket.CONNECTING || this.readyState === WebSocket.CLOSED) {\n      return;\n    }\n    this._paused = true;\n    this._socket.pause();\n  }\n\n  /**\n   * Send a ping.\n   *\n   * @param {*} [data] The data to send\n   * @param {Boolean} [mask] Indicates whether or not to mask `data`\n   * @param {Function} [cb] Callback which is executed when the ping is sent\n   * @public\n   */\n  ping(data, mask, cb) {\n    if (this.readyState === WebSocket.CONNECTING) {\n      throw new Error('WebSocket is not open: readyState 0 (CONNECTING)');\n    }\n    if (typeof data === 'function') {\n      cb = data;\n      data = mask = undefined;\n    } else if (typeof mask === 'function') {\n      cb = mask;\n      mask = undefined;\n    }\n    if (typeof data === 'number') data = data.toString();\n    if (this.readyState !== WebSocket.OPEN) {\n      sendAfterClose(this, data, cb);\n      return;\n    }\n    if (mask === undefined) mask = !this._isServer;\n    this._sender.ping(data || EMPTY_BUFFER, mask, cb);\n  }\n\n  /**\n   * Send a pong.\n   *\n   * @param {*} [data] The data to send\n   * @param {Boolean} [mask] Indicates whether or not to mask `data`\n   * @param {Function} [cb] Callback which is executed when the pong is sent\n   * @public\n   */\n  pong(data, mask, cb) {\n    if (this.readyState === WebSocket.CONNECTING) {\n      throw new Error('WebSocket is not open: readyState 0 (CONNECTING)');\n    }\n    if (typeof data === 'function') {\n      cb = data;\n      data = mask = undefined;\n    } else if (typeof mask === 'function') {\n      cb = mask;\n      mask = undefined;\n    }\n    if (typeof data === 'number') data = data.toString();\n    if (this.readyState !== WebSocket.OPEN) {\n      sendAfterClose(this, data, cb);\n      return;\n    }\n    if (mask === undefined) mask = !this._isServer;\n    this._sender.pong(data || EMPTY_BUFFER, mask, cb);\n  }\n\n  /**\n   * Resume the socket.\n   *\n   * @public\n   */\n  resume() {\n    if (this.readyState === WebSocket.CONNECTING || this.readyState === WebSocket.CLOSED) {\n      return;\n    }\n    this._paused = false;\n    if (!this._receiver._writableState.needDrain) this._socket.resume();\n  }\n\n  /**\n   * Send a data message.\n   *\n   * @param {*} data The message to send\n   * @param {Object} [options] Options object\n   * @param {Boolean} [options.binary] Specifies whether `data` is binary or\n   *     text\n   * @param {Boolean} [options.compress] Specifies whether or not to compress\n   *     `data`\n   * @param {Boolean} [options.fin=true] Specifies whether the fragment is the\n   *     last one\n   * @param {Boolean} [options.mask] Specifies whether or not to mask `data`\n   * @param {Function} [cb] Callback which is executed when data is written out\n   * @public\n   */\n  send(data, options, cb) {\n    if (this.readyState === WebSocket.CONNECTING) {\n      throw new Error('WebSocket is not open: readyState 0 (CONNECTING)');\n    }\n    if (typeof options === 'function') {\n      cb = options;\n      options = {};\n    }\n    if (typeof data === 'number') data = data.toString();\n    if (this.readyState !== WebSocket.OPEN) {\n      sendAfterClose(this, data, cb);\n      return;\n    }\n    const opts = _objectSpread({\n      binary: typeof data !== 'string',\n      mask: !this._isServer,\n      compress: true,\n      fin: true\n    }, options);\n    if (!this._extensions[PerMessageDeflate.extensionName]) {\n      opts.compress = false;\n    }\n    this._sender.send(data || EMPTY_BUFFER, opts, cb);\n  }\n\n  /**\n   * Forcibly close the connection.\n   *\n   * @public\n   */\n  terminate() {\n    if (this.readyState === WebSocket.CLOSED) return;\n    if (this.readyState === WebSocket.CONNECTING) {\n      const msg = 'WebSocket was closed before the connection was established';\n      abortHandshake(this, this._req, msg);\n      return;\n    }\n    if (this._socket) {\n      this._readyState = WebSocket.CLOSING;\n      this._socket.destroy();\n    }\n  }\n}\n\n/**\n * @constant {Number} CONNECTING\n * @memberof WebSocket\n */\nObject.defineProperty(WebSocket, 'CONNECTING', {\n  enumerable: true,\n  value: readyStates.indexOf('CONNECTING')\n});\n\n/**\n * @constant {Number} CONNECTING\n * @memberof WebSocket.prototype\n */\nObject.defineProperty(WebSocket.prototype, 'CONNECTING', {\n  enumerable: true,\n  value: readyStates.indexOf('CONNECTING')\n});\n\n/**\n * @constant {Number} OPEN\n * @memberof WebSocket\n */\nObject.defineProperty(WebSocket, 'OPEN', {\n  enumerable: true,\n  value: readyStates.indexOf('OPEN')\n});\n\n/**\n * @constant {Number} OPEN\n * @memberof WebSocket.prototype\n */\nObject.defineProperty(WebSocket.prototype, 'OPEN', {\n  enumerable: true,\n  value: readyStates.indexOf('OPEN')\n});\n\n/**\n * @constant {Number} CLOSING\n * @memberof WebSocket\n */\nObject.defineProperty(WebSocket, 'CLOSING', {\n  enumerable: true,\n  value: readyStates.indexOf('CLOSING')\n});\n\n/**\n * @constant {Number} CLOSING\n * @memberof WebSocket.prototype\n */\nObject.defineProperty(WebSocket.prototype, 'CLOSING', {\n  enumerable: true,\n  value: readyStates.indexOf('CLOSING')\n});\n\n/**\n * @constant {Number} CLOSED\n * @memberof WebSocket\n */\nObject.defineProperty(WebSocket, 'CLOSED', {\n  enumerable: true,\n  value: readyStates.indexOf('CLOSED')\n});\n\n/**\n * @constant {Number} CLOSED\n * @memberof WebSocket.prototype\n */\nObject.defineProperty(WebSocket.prototype, 'CLOSED', {\n  enumerable: true,\n  value: readyStates.indexOf('CLOSED')\n});\n['binaryType', 'bufferedAmount', 'extensions', 'isPaused', 'protocol', 'readyState', 'url'].forEach(property => {\n  Object.defineProperty(WebSocket.prototype, property, {\n    enumerable: true\n  });\n});\n\n//\n// Add the `onopen`, `onerror`, `onclose`, and `onmessage` attributes.\n// See https://html.spec.whatwg.org/multipage/comms.html#the-websocket-interface\n//\n['open', 'error', 'close', 'message'].forEach(method => {\n  Object.defineProperty(WebSocket.prototype, `on${method}`, {\n    enumerable: true,\n    get() {\n      for (const listener of this.listeners(method)) {\n        if (listener[kForOnEventAttribute]) return listener[kListener];\n      }\n      return null;\n    },\n    set(handler) {\n      for (const listener of this.listeners(method)) {\n        if (listener[kForOnEventAttribute]) {\n          this.removeListener(method, listener);\n          break;\n        }\n      }\n      if (typeof handler !== 'function') return;\n      this.addEventListener(method, handler, {\n        [kForOnEventAttribute]: true\n      });\n    }\n  });\n});\nWebSocket.prototype.addEventListener = addEventListener;\nWebSocket.prototype.removeEventListener = removeEventListener;\nmodule.exports = WebSocket;\n\n/**\n * Initialize a WebSocket client.\n *\n * @param {WebSocket} websocket The client to initialize\n * @param {(String|URL)} address The URL to which to connect\n * @param {Array} protocols The subprotocols\n * @param {Object} [options] Connection options\n * @param {Boolean} [options.allowSynchronousEvents=true] Specifies whether any\n *     of the `'message'`, `'ping'`, and `'pong'` events can be emitted multiple\n *     times in the same tick\n * @param {Boolean} [options.autoPong=true] Specifies whether or not to\n *     automatically send a pong in response to a ping\n * @param {Function} [options.finishRequest] A function which can be used to\n *     customize the headers of each http request before it is sent\n * @param {Boolean} [options.followRedirects=false] Whether or not to follow\n *     redirects\n * @param {Function} [options.generateMask] The function used to generate the\n *     masking key\n * @param {Number} [options.handshakeTimeout] Timeout in milliseconds for the\n *     handshake request\n * @param {Number} [options.maxPayload=104857600] The maximum allowed message\n *     size\n * @param {Number} [options.maxRedirects=10] The maximum number of redirects\n *     allowed\n * @param {String} [options.origin] Value of the `Origin` or\n *     `Sec-WebSocket-Origin` header\n * @param {(Boolean|Object)} [options.perMessageDeflate=true] Enable/disable\n *     permessage-deflate\n * @param {Number} [options.protocolVersion=13] Value of the\n *     `Sec-WebSocket-Version` header\n * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or\n *     not to skip UTF-8 validation for text and close messages\n * @private\n */\nfunction initAsClient(websocket, address, protocols, options) {\n  const opts = _objectSpread(_objectSpread({\n    allowSynchronousEvents: true,\n    autoPong: true,\n    protocolVersion: protocolVersions[1],\n    maxPayload: 100 * 1024 * 1024,\n    skipUTF8Validation: false,\n    perMessageDeflate: true,\n    followRedirects: false,\n    maxRedirects: 10\n  }, options), {}, {\n    socketPath: undefined,\n    hostname: undefined,\n    protocol: undefined,\n    timeout: undefined,\n    method: 'GET',\n    host: undefined,\n    path: undefined,\n    port: undefined\n  });\n  websocket._autoPong = opts.autoPong;\n  if (!protocolVersions.includes(opts.protocolVersion)) {\n    throw new RangeError(`Unsupported protocol version: ${opts.protocolVersion} ` + `(supported versions: ${protocolVersions.join(', ')})`);\n  }\n  let parsedUrl;\n  if (address instanceof URL) {\n    parsedUrl = address;\n  } else {\n    try {\n      parsedUrl = new URL(address);\n    } catch (e) {\n      throw new SyntaxError(`Invalid URL: ${address}`);\n    }\n  }\n  if (parsedUrl.protocol === 'http:') {\n    parsedUrl.protocol = 'ws:';\n  } else if (parsedUrl.protocol === 'https:') {\n    parsedUrl.protocol = 'wss:';\n  }\n  websocket._url = parsedUrl.href;\n  const isSecure = parsedUrl.protocol === 'wss:';\n  const isIpcUrl = parsedUrl.protocol === 'ws+unix:';\n  let invalidUrlMessage;\n  if (parsedUrl.protocol !== 'ws:' && !isSecure && !isIpcUrl) {\n    invalidUrlMessage = 'The URL\\'s protocol must be one of \"ws:\", \"wss:\", ' + '\"http:\", \"https:\", or \"ws+unix:\"';\n  } else if (isIpcUrl && !parsedUrl.pathname) {\n    invalidUrlMessage = \"The URL's pathname is empty\";\n  } else if (parsedUrl.hash) {\n    invalidUrlMessage = 'The URL contains a fragment identifier';\n  }\n  if (invalidUrlMessage) {\n    const err = new SyntaxError(invalidUrlMessage);\n    if (websocket._redirects === 0) {\n      throw err;\n    } else {\n      emitErrorAndClose(websocket, err);\n      return;\n    }\n  }\n  const defaultPort = isSecure ? 443 : 80;\n  const key = randomBytes(16).toString('base64');\n  const request = isSecure ? https.request : http.request;\n  const protocolSet = new Set();\n  let perMessageDeflate;\n  opts.createConnection = opts.createConnection || (isSecure ? tlsConnect : netConnect);\n  opts.defaultPort = opts.defaultPort || defaultPort;\n  opts.port = parsedUrl.port || defaultPort;\n  opts.host = parsedUrl.hostname.startsWith('[') ? parsedUrl.hostname.slice(1, -1) : parsedUrl.hostname;\n  opts.headers = _objectSpread(_objectSpread({}, opts.headers), {}, {\n    'Sec-WebSocket-Version': opts.protocolVersion,\n    'Sec-WebSocket-Key': key,\n    Connection: 'Upgrade',\n    Upgrade: 'websocket'\n  });\n  opts.path = parsedUrl.pathname + parsedUrl.search;\n  opts.timeout = opts.handshakeTimeout;\n  if (opts.perMessageDeflate) {\n    perMessageDeflate = new PerMessageDeflate(opts.perMessageDeflate !== true ? opts.perMessageDeflate : {}, false, opts.maxPayload);\n    opts.headers['Sec-WebSocket-Extensions'] = format({\n      [PerMessageDeflate.extensionName]: perMessageDeflate.offer()\n    });\n  }\n  if (protocols.length) {\n    for (const protocol of protocols) {\n      if (typeof protocol !== 'string' || !subprotocolRegex.test(protocol) || protocolSet.has(protocol)) {\n        throw new SyntaxError('An invalid or duplicated subprotocol was specified');\n      }\n      protocolSet.add(protocol);\n    }\n    opts.headers['Sec-WebSocket-Protocol'] = protocols.join(',');\n  }\n  if (opts.origin) {\n    if (opts.protocolVersion < 13) {\n      opts.headers['Sec-WebSocket-Origin'] = opts.origin;\n    } else {\n      opts.headers.Origin = opts.origin;\n    }\n  }\n  if (parsedUrl.username || parsedUrl.password) {\n    opts.auth = `${parsedUrl.username}:${parsedUrl.password}`;\n  }\n  if (isIpcUrl) {\n    const parts = opts.path.split(':');\n    opts.socketPath = parts[0];\n    opts.path = parts[1];\n  }\n  let req;\n  if (opts.followRedirects) {\n    if (websocket._redirects === 0) {\n      websocket._originalIpc = isIpcUrl;\n      websocket._originalSecure = isSecure;\n      websocket._originalHostOrSocketPath = isIpcUrl ? opts.socketPath : parsedUrl.host;\n      const headers = options && options.headers;\n\n      //\n      // Shallow copy the user provided options so that headers can be changed\n      // without mutating the original object.\n      //\n      options = _objectSpread(_objectSpread({}, options), {}, {\n        headers: {}\n      });\n      if (headers) {\n        for (const [key, value] of Object.entries(headers)) {\n          options.headers[key.toLowerCase()] = value;\n        }\n      }\n    } else if (websocket.listenerCount('redirect') === 0) {\n      const isSameHost = isIpcUrl ? websocket._originalIpc ? opts.socketPath === websocket._originalHostOrSocketPath : false : websocket._originalIpc ? false : parsedUrl.host === websocket._originalHostOrSocketPath;\n      if (!isSameHost || websocket._originalSecure && !isSecure) {\n        //\n        // Match curl 7.77.0 behavior and drop the following headers. These\n        // headers are also dropped when following a redirect to a subdomain.\n        //\n        delete opts.headers.authorization;\n        delete opts.headers.cookie;\n        if (!isSameHost) delete opts.headers.host;\n        opts.auth = undefined;\n      }\n    }\n\n    //\n    // Match curl 7.77.0 behavior and make the first `Authorization` header win.\n    // If the `Authorization` header is set, then there is nothing to do as it\n    // will take precedence.\n    //\n    if (opts.auth && !options.headers.authorization) {\n      options.headers.authorization = 'Basic ' + Buffer.from(opts.auth).toString('base64');\n    }\n    req = websocket._req = request(opts);\n    if (websocket._redirects) {\n      //\n      // Unlike what is done for the `'upgrade'` event, no early exit is\n      // triggered here if the user calls `websocket.close()` or\n      // `websocket.terminate()` from a listener of the `'redirect'` event. This\n      // is because the user can also call `request.destroy()` with an error\n      // before calling `websocket.close()` or `websocket.terminate()` and this\n      // would result in an error being emitted on the `request` object with no\n      // `'error'` event listeners attached.\n      //\n      websocket.emit('redirect', websocket.url, req);\n    }\n  } else {\n    req = websocket._req = request(opts);\n  }\n  if (opts.timeout) {\n    req.on('timeout', () => {\n      abortHandshake(websocket, req, 'Opening handshake has timed out');\n    });\n  }\n  req.on('error', err => {\n    if (req === null || req[kAborted]) return;\n    req = websocket._req = null;\n    emitErrorAndClose(websocket, err);\n  });\n  req.on('response', res => {\n    const location = res.headers.location;\n    const statusCode = res.statusCode;\n    if (location && opts.followRedirects && statusCode >= 300 && statusCode < 400) {\n      if (++websocket._redirects > opts.maxRedirects) {\n        abortHandshake(websocket, req, 'Maximum redirects exceeded');\n        return;\n      }\n      req.abort();\n      let addr;\n      try {\n        addr = new URL(location, address);\n      } catch (e) {\n        const err = new SyntaxError(`Invalid URL: ${location}`);\n        emitErrorAndClose(websocket, err);\n        return;\n      }\n      initAsClient(websocket, addr, protocols, options);\n    } else if (!websocket.emit('unexpected-response', req, res)) {\n      abortHandshake(websocket, req, `Unexpected server response: ${res.statusCode}`);\n    }\n  });\n  req.on('upgrade', (res, socket, head) => {\n    websocket.emit('upgrade', res);\n\n    //\n    // The user may have closed the connection from a listener of the\n    // `'upgrade'` event.\n    //\n    if (websocket.readyState !== WebSocket.CONNECTING) return;\n    req = websocket._req = null;\n    const upgrade = res.headers.upgrade;\n    if (upgrade === undefined || upgrade.toLowerCase() !== 'websocket') {\n      abortHandshake(websocket, socket, 'Invalid Upgrade header');\n      return;\n    }\n    const digest = createHash('sha1').update(key + GUID).digest('base64');\n    if (res.headers['sec-websocket-accept'] !== digest) {\n      abortHandshake(websocket, socket, 'Invalid Sec-WebSocket-Accept header');\n      return;\n    }\n    const serverProt = res.headers['sec-websocket-protocol'];\n    let protError;\n    if (serverProt !== undefined) {\n      if (!protocolSet.size) {\n        protError = 'Server sent a subprotocol but none was requested';\n      } else if (!protocolSet.has(serverProt)) {\n        protError = 'Server sent an invalid subprotocol';\n      }\n    } else if (protocolSet.size) {\n      protError = 'Server sent no subprotocol';\n    }\n    if (protError) {\n      abortHandshake(websocket, socket, protError);\n      return;\n    }\n    if (serverProt) websocket._protocol = serverProt;\n    const secWebSocketExtensions = res.headers['sec-websocket-extensions'];\n    if (secWebSocketExtensions !== undefined) {\n      if (!perMessageDeflate) {\n        const message = 'Server sent a Sec-WebSocket-Extensions header but no extension ' + 'was requested';\n        abortHandshake(websocket, socket, message);\n        return;\n      }\n      let extensions;\n      try {\n        extensions = parse(secWebSocketExtensions);\n      } catch (err) {\n        const message = 'Invalid Sec-WebSocket-Extensions header';\n        abortHandshake(websocket, socket, message);\n        return;\n      }\n      const extensionNames = Object.keys(extensions);\n      if (extensionNames.length !== 1 || extensionNames[0] !== PerMessageDeflate.extensionName) {\n        const message = 'Server indicated an extension that was not requested';\n        abortHandshake(websocket, socket, message);\n        return;\n      }\n      try {\n        perMessageDeflate.accept(extensions[PerMessageDeflate.extensionName]);\n      } catch (err) {\n        const message = 'Invalid Sec-WebSocket-Extensions header';\n        abortHandshake(websocket, socket, message);\n        return;\n      }\n      websocket._extensions[PerMessageDeflate.extensionName] = perMessageDeflate;\n    }\n    websocket.setSocket(socket, head, {\n      allowSynchronousEvents: opts.allowSynchronousEvents,\n      generateMask: opts.generateMask,\n      maxPayload: opts.maxPayload,\n      skipUTF8Validation: opts.skipUTF8Validation\n    });\n  });\n  if (opts.finishRequest) {\n    opts.finishRequest(req, websocket);\n  } else {\n    req.end();\n  }\n}\n\n/**\n * Emit the `'error'` and `'close'` events.\n *\n * @param {WebSocket} websocket The WebSocket instance\n * @param {Error} The error to emit\n * @private\n */\nfunction emitErrorAndClose(websocket, err) {\n  websocket._readyState = WebSocket.CLOSING;\n  //\n  // The following assignment is practically useless and is done only for\n  // consistency.\n  //\n  websocket._errorEmitted = true;\n  websocket.emit('error', err);\n  websocket.emitClose();\n}\n\n/**\n * Create a `net.Socket` and initiate a connection.\n *\n * @param {Object} options Connection options\n * @return {net.Socket} The newly created socket used to start the connection\n * @private\n */\nfunction netConnect(options) {\n  options.path = options.socketPath;\n  return net.connect(options);\n}\n\n/**\n * Create a `tls.TLSSocket` and initiate a connection.\n *\n * @param {Object} options Connection options\n * @return {tls.TLSSocket} The newly created socket used to start the connection\n * @private\n */\nfunction tlsConnect(options) {\n  options.path = undefined;\n  if (!options.servername && options.servername !== '') {\n    options.servername = net.isIP(options.host) ? '' : options.host;\n  }\n  return tls.connect(options);\n}\n\n/**\n * Abort the handshake and emit an error.\n *\n * @param {WebSocket} websocket The WebSocket instance\n * @param {(http.ClientRequest|net.Socket|tls.Socket)} stream The request to\n *     abort or the socket to destroy\n * @param {String} message The error message\n * @private\n */\nfunction abortHandshake(websocket, stream, message) {\n  websocket._readyState = WebSocket.CLOSING;\n  const err = new Error(message);\n  Error.captureStackTrace(err, abortHandshake);\n  if (stream.setHeader) {\n    stream[kAborted] = true;\n    stream.abort();\n    if (stream.socket && !stream.socket.destroyed) {\n      //\n      // On Node.js >= 14.3.0 `request.abort()` does not destroy the socket if\n      // called after the request completed. See\n      // https://github.com/websockets/ws/issues/1869.\n      //\n      stream.socket.destroy();\n    }\n    process.nextTick(emitErrorAndClose, websocket, err);\n  } else {\n    stream.destroy(err);\n    stream.once('error', websocket.emit.bind(websocket, 'error'));\n    stream.once('close', websocket.emitClose.bind(websocket));\n  }\n}\n\n/**\n * Handle cases where the `ping()`, `pong()`, or `send()` methods are called\n * when the `readyState` attribute is `CLOSING` or `CLOSED`.\n *\n * @param {WebSocket} websocket The WebSocket instance\n * @param {*} [data] The data to send\n * @param {Function} [cb] Callback\n * @private\n */\nfunction sendAfterClose(websocket, data, cb) {\n  if (data) {\n    const length = isBlob(data) ? data.size : toBuffer(data).length;\n\n    //\n    // The `_bufferedAmount` property is used only when the peer is a client and\n    // the opening handshake fails. Under these circumstances, in fact, the\n    // `setSocket()` method is not called, so the `_socket` and `_sender`\n    // properties are set to `null`.\n    //\n    if (websocket._socket) websocket._sender._bufferedBytes += length;else websocket._bufferedAmount += length;\n  }\n  if (cb) {\n    const err = new Error(`WebSocket is not open: readyState ${websocket.readyState} ` + `(${readyStates[websocket.readyState]})`);\n    process.nextTick(cb, err);\n  }\n}\n\n/**\n * The listener of the `Receiver` `'conclude'` event.\n *\n * @param {Number} code The status code\n * @param {Buffer} reason The reason for closing\n * @private\n */\nfunction receiverOnConclude(code, reason) {\n  const websocket = this[kWebSocket];\n  websocket._closeFrameReceived = true;\n  websocket._closeMessage = reason;\n  websocket._closeCode = code;\n  if (websocket._socket[kWebSocket] === undefined) return;\n  websocket._socket.removeListener('data', socketOnData);\n  process.nextTick(resume, websocket._socket);\n  if (code === 1005) websocket.close();else websocket.close(code, reason);\n}\n\n/**\n * The listener of the `Receiver` `'drain'` event.\n *\n * @private\n */\nfunction receiverOnDrain() {\n  const websocket = this[kWebSocket];\n  if (!websocket.isPaused) websocket._socket.resume();\n}\n\n/**\n * The listener of the `Receiver` `'error'` event.\n *\n * @param {(RangeError|Error)} err The emitted error\n * @private\n */\nfunction receiverOnError(err) {\n  const websocket = this[kWebSocket];\n  if (websocket._socket[kWebSocket] !== undefined) {\n    websocket._socket.removeListener('data', socketOnData);\n\n    //\n    // On Node.js < 14.0.0 the `'error'` event is emitted synchronously. See\n    // https://github.com/websockets/ws/issues/1940.\n    //\n    process.nextTick(resume, websocket._socket);\n    websocket.close(err[kStatusCode]);\n  }\n  if (!websocket._errorEmitted) {\n    websocket._errorEmitted = true;\n    websocket.emit('error', err);\n  }\n}\n\n/**\n * The listener of the `Receiver` `'finish'` event.\n *\n * @private\n */\nfunction receiverOnFinish() {\n  this[kWebSocket].emitClose();\n}\n\n/**\n * The listener of the `Receiver` `'message'` event.\n *\n * @param {Buffer|ArrayBuffer|Buffer[])} data The message\n * @param {Boolean} isBinary Specifies whether the message is binary or not\n * @private\n */\nfunction receiverOnMessage(data, isBinary) {\n  this[kWebSocket].emit('message', data, isBinary);\n}\n\n/**\n * The listener of the `Receiver` `'ping'` event.\n *\n * @param {Buffer} data The data included in the ping frame\n * @private\n */\nfunction receiverOnPing(data) {\n  const websocket = this[kWebSocket];\n  if (websocket._autoPong) websocket.pong(data, !this._isServer, NOOP);\n  websocket.emit('ping', data);\n}\n\n/**\n * The listener of the `Receiver` `'pong'` event.\n *\n * @param {Buffer} data The data included in the pong frame\n * @private\n */\nfunction receiverOnPong(data) {\n  this[kWebSocket].emit('pong', data);\n}\n\n/**\n * Resume a readable stream\n *\n * @param {Readable} stream The readable stream\n * @private\n */\nfunction resume(stream) {\n  stream.resume();\n}\n\n/**\n * The `Sender` error event handler.\n *\n * @param {Error} The error\n * @private\n */\nfunction senderOnError(err) {\n  const websocket = this[kWebSocket];\n  if (websocket.readyState === WebSocket.CLOSED) return;\n  if (websocket.readyState === WebSocket.OPEN) {\n    websocket._readyState = WebSocket.CLOSING;\n    setCloseTimer(websocket);\n  }\n\n  //\n  // `socket.end()` is used instead of `socket.destroy()` to allow the other\n  // peer to finish sending queued data. There is no need to set a timer here\n  // because `CLOSING` means that it is already set or not needed.\n  //\n  this._socket.end();\n  if (!websocket._errorEmitted) {\n    websocket._errorEmitted = true;\n    websocket.emit('error', err);\n  }\n}\n\n/**\n * Set a timer to destroy the underlying raw socket of a WebSocket.\n *\n * @param {WebSocket} websocket The WebSocket instance\n * @private\n */\nfunction setCloseTimer(websocket) {\n  websocket._closeTimer = setTimeout(websocket._socket.destroy.bind(websocket._socket), closeTimeout);\n}\n\n/**\n * The listener of the socket `'close'` event.\n *\n * @private\n */\nfunction socketOnClose() {\n  const websocket = this[kWebSocket];\n  this.removeListener('close', socketOnClose);\n  this.removeListener('data', socketOnData);\n  this.removeListener('end', socketOnEnd);\n  websocket._readyState = WebSocket.CLOSING;\n  let chunk;\n\n  //\n  // The close frame might not have been received or the `'end'` event emitted,\n  // for example, if the socket was destroyed due to an error. Ensure that the\n  // `receiver` stream is closed after writing any remaining buffered data to\n  // it. If the readable side of the socket is in flowing mode then there is no\n  // buffered data as everything has been already written and `readable.read()`\n  // will return `null`. If instead, the socket is paused, any possible buffered\n  // data will be read as a single chunk.\n  //\n  if (!this._readableState.endEmitted && !websocket._closeFrameReceived && !websocket._receiver._writableState.errorEmitted && (chunk = websocket._socket.read()) !== null) {\n    websocket._receiver.write(chunk);\n  }\n  websocket._receiver.end();\n  this[kWebSocket] = undefined;\n  clearTimeout(websocket._closeTimer);\n  if (websocket._receiver._writableState.finished || websocket._receiver._writableState.errorEmitted) {\n    websocket.emitClose();\n  } else {\n    websocket._receiver.on('error', receiverOnFinish);\n    websocket._receiver.on('finish', receiverOnFinish);\n  }\n}\n\n/**\n * The listener of the socket `'data'` event.\n *\n * @param {Buffer} chunk A chunk of data\n * @private\n */\nfunction socketOnData(chunk) {\n  if (!this[kWebSocket]._receiver.write(chunk)) {\n    this.pause();\n  }\n}\n\n/**\n * The listener of the socket `'end'` event.\n *\n * @private\n */\nfunction socketOnEnd() {\n  const websocket = this[kWebSocket];\n  websocket._readyState = WebSocket.CLOSING;\n  websocket._receiver.end();\n  this.end();\n}\n\n/**\n * The listener of the socket `'error'` event.\n *\n * @private\n */\nfunction socketOnError() {\n  const websocket = this[kWebSocket];\n  this.removeListener('error', socketOnError);\n  this.on('error', NOOP);\n  if (websocket) {\n    websocket._readyState = WebSocket.CLOSING;\n    this.destroy();\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/websocket.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/wrapper.mjs":
/*!*************************************!*\
  !*** ./node_modules/ws/wrapper.mjs ***!
  \*************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Receiver: () => (/* reexport default export from named module */ _lib_receiver_js__WEBPACK_IMPORTED_MODULE_1__),\n/* harmony export */   Sender: () => (/* reexport default export from named module */ _lib_sender_js__WEBPACK_IMPORTED_MODULE_2__),\n/* harmony export */   WebSocket: () => (/* reexport default export from named module */ _lib_websocket_js__WEBPACK_IMPORTED_MODULE_3__),\n/* harmony export */   WebSocketServer: () => (/* reexport default export from named module */ _lib_websocket_server_js__WEBPACK_IMPORTED_MODULE_4__),\n/* harmony export */   createWebSocketStream: () => (/* reexport default export from named module */ _lib_stream_js__WEBPACK_IMPORTED_MODULE_0__),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _lib_stream_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/stream.js */ \"(ssr)/./node_modules/ws/lib/stream.js\");\n/* harmony import */ var _lib_receiver_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/receiver.js */ \"(ssr)/./node_modules/ws/lib/receiver.js\");\n/* harmony import */ var _lib_sender_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/sender.js */ \"(ssr)/./node_modules/ws/lib/sender.js\");\n/* harmony import */ var _lib_websocket_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/websocket.js */ \"(ssr)/./node_modules/ws/lib/websocket.js\");\n/* harmony import */ var _lib_websocket_server_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/websocket-server.js */ \"(ssr)/./node_modules/ws/lib/websocket-server.js\");\n\n\n\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_lib_websocket_js__WEBPACK_IMPORTED_MODULE_3__);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd3Mvd3JhcHBlci5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBb0Q7QUFDWDtBQUNKO0FBQ007QUFDYTtBQUV1QjtBQUMvRSxpRUFBZUcsOENBQVMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbmFhdGFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcT3Bvc0lcXHYxNlxcbm9kZV9tb2R1bGVzXFx3c1xcd3JhcHBlci5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZVdlYlNvY2tldFN0cmVhbSBmcm9tICcuL2xpYi9zdHJlYW0uanMnO1xuaW1wb3J0IFJlY2VpdmVyIGZyb20gJy4vbGliL3JlY2VpdmVyLmpzJztcbmltcG9ydCBTZW5kZXIgZnJvbSAnLi9saWIvc2VuZGVyLmpzJztcbmltcG9ydCBXZWJTb2NrZXQgZnJvbSAnLi9saWIvd2Vic29ja2V0LmpzJztcbmltcG9ydCBXZWJTb2NrZXRTZXJ2ZXIgZnJvbSAnLi9saWIvd2Vic29ja2V0LXNlcnZlci5qcyc7XG5cbmV4cG9ydCB7IGNyZWF0ZVdlYlNvY2tldFN0cmVhbSwgUmVjZWl2ZXIsIFNlbmRlciwgV2ViU29ja2V0LCBXZWJTb2NrZXRTZXJ2ZXIgfTtcbmV4cG9ydCBkZWZhdWx0IFdlYlNvY2tldDtcbiJdLCJuYW1lcyI6WyJjcmVhdGVXZWJTb2NrZXRTdHJlYW0iLCJSZWNlaXZlciIsIlNlbmRlciIsIldlYlNvY2tldCIsIldlYlNvY2tldFNlcnZlciJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/wrapper.mjs\n");

/***/ })

};
;