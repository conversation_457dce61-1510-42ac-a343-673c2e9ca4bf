"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_lib_supabase_admin_ts";
exports.ids = ["_ssr_src_lib_supabase_admin_ts"];
exports.modules = {

/***/ "(ssr)/./src/lib/supabase/admin.ts":
/*!***********************************!*\
  !*** ./src/lib/supabase/admin.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupabaseAdminService: () => (/* binding */ SupabaseAdminService),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            _defineProperty(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\nfunction _defineProperty(obj, key, value) {\n    key = _toPropertyKey(key);\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _toPropertyKey(arg) {\n    var key = _toPrimitive(arg, \"string\");\n    return typeof key === \"symbol\" ? key : String(key);\n}\nfunction _toPrimitive(input, hint) {\n    if (typeof input !== \"object\" || input === null) return input;\n    var prim = input[Symbol.toPrimitive];\n    if (prim !== undefined) {\n        var res = prim.call(input, hint || \"default\");\n        if (typeof res !== \"object\") return res;\n        throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (hint === \"string\" ? String : Number)(input);\n}\n// src/lib/supabase/admin.ts\n// Cliente administrativo de Supabase para operaciones del servidor\n\n// Cliente admin con privilegios elevados\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\"https://fxnhpxjijinfuxxxplzj.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\n// Tipos para las nuevas tablas\n// Funciones de utilidad para operaciones administrativas\nclass SupabaseAdminService {\n    // Crear transacción de Stripe\n    static async createStripeTransaction(transaction) {\n        const { data, error } = await supabaseAdmin.from('stripe_transactions').insert([\n            transaction\n        ]).select().single();\n        if (error) {\n            console.error('Error creating stripe transaction:', error);\n            throw new Error(`Failed to create transaction: ${error.message}`);\n        }\n        return data;\n    }\n    // Obtener transacción por session ID\n    static async getTransactionBySessionId(sessionId) {\n        const { data, error } = await supabaseAdmin.from('stripe_transactions').select('*').eq('stripe_session_id', sessionId).single();\n        if (error && error.code !== 'PGRST116') {\n            console.error('Error fetching transaction:', error);\n            throw new Error(`Failed to fetch transaction: ${error.message}`);\n        }\n        return data;\n    }\n    // Crear usuario con invitación\n    static async createUserWithInvitation(email, userData) {\n        console.log('🔄 [SUPABASE_ADMIN] Creating user invitation:', {\n            email,\n            userData,\n            redirectTo: `${\"http://localhost:3000\"}/auth/callback`,\n            timestamp: new Date().toISOString()\n        });\n        const { data, error } = await supabaseAdmin.auth.admin.inviteUserByEmail(email, {\n            data: userData,\n            redirectTo: `${\"http://localhost:3000\"}/auth/callback`\n        });\n        console.log('📊 [SUPABASE_ADMIN] Invitation result:', {\n            hasData: !!data,\n            hasUser: !!data?.user,\n            userId: data?.user?.id,\n            userEmail: data?.user?.email,\n            userAud: data?.user?.aud,\n            userRole: data?.user?.role,\n            emailConfirmed: data?.user?.email_confirmed_at,\n            userMetadata: data?.user?.user_metadata,\n            appMetadata: data?.user?.app_metadata,\n            error: error?.message,\n            errorCode: error?.status,\n            fullError: error\n        });\n        if (error) {\n            console.error('❌ [SUPABASE_ADMIN] Error creating user invitation:', {\n                message: error.message,\n                status: error.status,\n                details: error\n            });\n            throw new Error(`Failed to create user invitation: ${error.message}`);\n        }\n        return data;\n    }\n    // Crear usuario con contraseña específica y opcionalmente enviar email de confirmación\n    static async createUserWithPassword(email, password, userData, sendConfirmationEmail = true) {\n        console.log('🔄 [SUPABASE_ADMIN] Creating user with password:', {\n            email,\n            userData,\n            sendConfirmationEmail,\n            timestamp: new Date().toISOString()\n        });\n        const { data, error } = await supabaseAdmin.auth.admin.createUser({\n            email,\n            password,\n            user_metadata: userData,\n            email_confirm: false // No confirmar automáticamente\n        });\n        console.log('📊 [SUPABASE_ADMIN] User creation result:', {\n            hasData: !!data,\n            hasUser: !!data?.user,\n            userId: data?.user?.id,\n            userEmail: data?.user?.email,\n            emailConfirmed: data?.user?.email_confirmed_at,\n            userMetadata: data?.user?.user_metadata,\n            error: error?.message,\n            errorCode: error?.status\n        });\n        if (error) {\n            console.error('❌ [SUPABASE_ADMIN] Error creating user with password:', {\n                message: error.message,\n                status: error.status,\n                details: error\n            });\n            return {\n                data: null,\n                error\n            };\n        }\n        // Enviar email de confirmación solo si se solicita\n        if (data?.user && sendConfirmationEmail) {\n            console.log('📧 Enviando email de confirmación...');\n            const { error: emailError } = await supabaseAdmin.auth.admin.generateLink({\n                type: 'signup',\n                email: email,\n                password: password,\n                // Requerido para generateLink\n                options: {\n                    redirectTo: `${\"http://localhost:3000\"}/auth/confirmed`\n                }\n            });\n            if (emailError) {\n                console.error('⚠️ Error enviando email de confirmación:', emailError);\n            // No fallar completamente, el usuario puede confirmar manualmente\n            } else {\n                console.log('✅ Email de confirmación enviado exitosamente');\n            }\n        } else if (data?.user && !sendConfirmationEmail) {\n            console.log('📧 Email de confirmación omitido (se enviará después del pago)');\n        }\n        return {\n            data,\n            error: null\n        };\n    }\n    // Enviar email de confirmación para usuario existente\n    static async sendConfirmationEmailForUser(userId) {\n        console.log('📧 [SUPABASE_ADMIN] Enviando email de confirmación para usuario:', userId);\n        try {\n            // Obtener datos del usuario\n            const { data: userData, error: userError } = await supabaseAdmin.auth.admin.getUserById(userId);\n            if (userError || !userData?.user) {\n                console.error('Error obteniendo datos del usuario:', userError);\n                return {\n                    success: false,\n                    error: 'Usuario no encontrado'\n                };\n            }\n            const user = userData.user;\n            // Para usuarios pre-registrados, actualizar el estado de confirmación directamente\n            // ya que el pago exitoso confirma la intención del usuario\n            const { error: updateError } = await supabaseAdmin.auth.admin.updateUserById(user.id, {\n                email_confirm: true,\n                user_metadata: _objectSpread(_objectSpread({}, user.user_metadata), {}, {\n                    payment_verified: true,\n                    email_confirmed_via_payment: true,\n                    confirmed_at: new Date().toISOString()\n                })\n            });\n            if (updateError) {\n                console.error('⚠️ Error confirmando email del usuario:', updateError);\n                return {\n                    success: false,\n                    error: updateError.message\n                };\n            }\n            console.log('✅ Usuario confirmado automáticamente después del pago exitoso');\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error('Error en sendConfirmationEmailForUser:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Error desconocido'\n            };\n        }\n    }\n    // Enviar email de confirmación para usuario existente (método legacy)\n    static async sendConfirmationEmail(email, password) {\n        console.log('📧 [SUPABASE_ADMIN] Enviando email de confirmación para:', email);\n        const { error: emailError } = await supabaseAdmin.auth.admin.generateLink({\n            type: 'signup',\n            email: email,\n            password: password,\n            options: {\n                redirectTo: `${\"http://localhost:3000\"}/auth/confirmed`\n            }\n        });\n        if (emailError) {\n            console.error('⚠️ Error enviando email de confirmación:', emailError);\n            return {\n                success: false,\n                error: emailError.message\n            };\n        } else {\n            console.log('✅ Email de confirmación enviado exitosamente');\n            return {\n                success: true\n            };\n        }\n    }\n    // Crear perfil de usuario\n    static async createUserProfile(profile) {\n        const { data, error } = await supabaseAdmin.from('user_profiles').insert([\n            profile\n        ]).select().single();\n        if (error) {\n            console.error('Error creating user profile:', error);\n            throw new Error(`Failed to create user profile: ${error.message}`);\n        }\n        return data;\n    }\n    // Crear o actualizar perfil de usuario\n    static async upsertUserProfile(profile) {\n        const { data, error } = await supabaseAdmin.from('user_profiles').upsert([\n            profile\n        ], {\n            onConflict: 'user_id'\n        }).select().single();\n        if (error) {\n            console.error('Error upserting user profile:', error);\n            throw new Error(`Failed to upsert user profile: ${error.message}`);\n        }\n        return data;\n    }\n    // Registrar cambio de plan\n    static async logPlanChange(planChange) {\n        const { data, error } = await supabaseAdmin.from('user_plan_history').insert([\n            planChange\n        ]).select().single();\n        if (error) {\n            console.error('Error logging plan change:', error);\n            throw new Error(`Failed to log plan change: ${error.message}`);\n        }\n        return data;\n    }\n    // Registrar acceso a característica\n    static async logFeatureAccess(accessLog) {\n        const { data, error } = await supabaseAdmin.from('feature_access_log').insert([\n            accessLog\n        ]).select().single();\n        if (error) {\n            console.error('Error logging feature access:', error);\n            throw new Error(`Failed to log feature access: ${error.message}`);\n        }\n        return data;\n    }\n    // Obtener perfil de usuario por ID\n    static async getUserProfile(userId) {\n        const { data, error } = await supabaseAdmin.from('user_profiles').select('*').eq('user_id', userId).single();\n        if (error && error.code !== 'PGRST116') {\n            console.error('Error fetching user profile:', error);\n            throw new Error(`Failed to fetch user profile: ${error.message}`);\n        }\n        return data;\n    }\n    // Actualizar transacción con user_id\n    static async updateTransactionWithUser(transactionId, userId) {\n        const { error } = await supabaseAdmin.from('stripe_transactions').update({\n            user_id: userId,\n            updated_at: new Date().toISOString()\n        }).eq('id', transactionId);\n        if (error) {\n            console.error('Error updating transaction with user_id:', error);\n            throw new Error(`Failed to update transaction: ${error.message}`);\n        }\n    }\n    // Activar transacción (marcar como activada)\n    static async activateTransaction(transactionId) {\n        const { error } = await supabaseAdmin.from('stripe_transactions').update({\n            activated_at: new Date().toISOString()\n        }).eq('id', transactionId);\n        if (error) {\n            console.error('Error activating transaction:', error);\n            throw new Error(`Failed to activate transaction: ${error.message}`);\n        }\n    }\n    // Obtener conteo de documentos del usuario\n    static async getDocumentsCount(userId) {\n        const { count, error } = await supabaseAdmin.from('documentos').select('*', {\n            count: 'exact',\n            head: true\n        }).eq('user_id', userId);\n        if (error) {\n            console.error('Error getting documents count:', error);\n            return 0; // Retornar 0 en caso de error en lugar de lanzar excepción\n        }\n        return count || 0;\n    }\n    // Obtener usuario por email desde Supabase Auth\n    static async getUserByEmail(email) {\n        try {\n            const { data: { users }, error } = await supabaseAdmin.auth.admin.listUsers();\n            if (error) {\n                console.error('Error getting user by email:', error);\n                throw new Error(`Failed to get user by email: ${error.message}`);\n            }\n            if (!users || users.length === 0) {\n                return null;\n            }\n            // Filtrar por email ya que la API no permite filtro directo\n            const user = users.find((u)=>u.email === email);\n            if (!user) {\n                return null;\n            }\n            return {\n                id: user.id,\n                email: user.email,\n                email_confirmed_at: user.email_confirmed_at\n            };\n        } catch (error) {\n            console.error('Error in getUserByEmail:', error);\n            throw error;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase/admin.ts\n");

/***/ })

};
;