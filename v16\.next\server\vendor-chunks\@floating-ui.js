"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@floating-ui";
exports.ids = ["vendor-chunks/@floating-ui"];
exports.modules = {

/***/ "(ssr)/./node_modules/@floating-ui/core/dist/floating-ui.core.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@floating-ui/core/dist/floating-ui.core.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   arrow: () => (/* binding */ arrow),\n/* harmony export */   autoPlacement: () => (/* binding */ autoPlacement),\n/* harmony export */   computePosition: () => (/* binding */ computePosition),\n/* harmony export */   detectOverflow: () => (/* binding */ detectOverflow),\n/* harmony export */   flip: () => (/* binding */ flip),\n/* harmony export */   hide: () => (/* binding */ hide),\n/* harmony export */   inline: () => (/* binding */ inline),\n/* harmony export */   limitShift: () => (/* binding */ limitShift),\n/* harmony export */   offset: () => (/* binding */ offset),\n/* harmony export */   rectToClientRect: () => (/* reexport safe */ _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.rectToClientRect),\n/* harmony export */   shift: () => (/* binding */ shift),\n/* harmony export */   size: () => (/* binding */ size)\n/* harmony export */ });\n/* harmony import */ var _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @floating-ui/utils */ \"(ssr)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs\");\nconst _excluded = [\"crossAxis\", \"alignment\", \"allowedPlacements\", \"autoAlignment\"],\n  _excluded2 = [\"mainAxis\", \"crossAxis\", \"fallbackPlacements\", \"fallbackStrategy\", \"fallbackAxisSideDirection\", \"flipAlignment\"],\n  _excluded3 = [\"strategy\"],\n  _excluded4 = [\"mainAxis\", \"crossAxis\", \"limiter\"],\n  _excluded5 = [\"apply\"];\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return typeof key === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (typeof input !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (typeof res !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\n\n\nfunction computeCoordsFromPlacement(_ref, placement, rtl) {\n  let {\n    reference,\n    floating\n  } = _ref;\n  const sideAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(placement);\n  const alignmentAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignmentAxis)(placement);\n  const alignLength = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAxisLength)(alignmentAxis);\n  const side = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement);\n  const isVertical = sideAxis === 'y';\n  const commonX = reference.x + reference.width / 2 - floating.width / 2;\n  const commonY = reference.y + reference.height / 2 - floating.height / 2;\n  const commonAlign = reference[alignLength] / 2 - floating[alignLength] / 2;\n  let coords;\n  switch (side) {\n    case 'top':\n      coords = {\n        x: commonX,\n        y: reference.y - floating.height\n      };\n      break;\n    case 'bottom':\n      coords = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n    case 'right':\n      coords = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n    case 'left':\n      coords = {\n        x: reference.x - floating.width,\n        y: commonY\n      };\n      break;\n    default:\n      coords = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n  switch ((0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement)) {\n    case 'start':\n      coords[alignmentAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n    case 'end':\n      coords[alignmentAxis] += commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n  }\n  return coords;\n}\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n *\n * This export does not have any `platform` interface logic. You will need to\n * write one for the platform you are using Floating UI with.\n */\nconst computePosition = async (reference, floating, config) => {\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform\n  } = config;\n  const validMiddleware = middleware.filter(Boolean);\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(floating));\n  let rects = await platform.getElementRects({\n    reference,\n    floating,\n    strategy\n  });\n  let {\n    x,\n    y\n  } = computeCoordsFromPlacement(rects, placement, rtl);\n  let statefulPlacement = placement;\n  let middlewareData = {};\n  let resetCount = 0;\n  for (let i = 0; i < validMiddleware.length; i++) {\n    const {\n      name,\n      fn\n    } = validMiddleware[i];\n    const {\n      x: nextX,\n      y: nextY,\n      data,\n      reset\n    } = await fn({\n      x,\n      y,\n      initialPlacement: placement,\n      placement: statefulPlacement,\n      strategy,\n      middlewareData,\n      rects,\n      platform,\n      elements: {\n        reference,\n        floating\n      }\n    });\n    x = nextX != null ? nextX : x;\n    y = nextY != null ? nextY : y;\n    middlewareData = _objectSpread(_objectSpread({}, middlewareData), {}, {\n      [name]: _objectSpread(_objectSpread({}, middlewareData[name]), data)\n    });\n    if (reset && resetCount <= 50) {\n      resetCount++;\n      if (typeof reset === 'object') {\n        if (reset.placement) {\n          statefulPlacement = reset.placement;\n        }\n        if (reset.rects) {\n          rects = reset.rects === true ? await platform.getElementRects({\n            reference,\n            floating,\n            strategy\n          }) : reset.rects;\n        }\n        ({\n          x,\n          y\n        } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));\n      }\n      i = -1;\n    }\n  }\n  return {\n    x,\n    y,\n    placement: statefulPlacement,\n    strategy,\n    middlewareData\n  };\n};\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nasync function detectOverflow(state, options) {\n  var _await$platform$isEle;\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    x,\n    y,\n    platform,\n    rects,\n    elements,\n    strategy\n  } = state;\n  const {\n    boundary = 'clippingAncestors',\n    rootBoundary = 'viewport',\n    elementContext = 'floating',\n    altBoundary = false,\n    padding = 0\n  } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n  const paddingObject = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getPaddingObject)(padding);\n  const altContext = elementContext === 'floating' ? 'reference' : 'floating';\n  const element = elements[altBoundary ? altContext : elementContext];\n  const clippingClientRect = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.rectToClientRect)(await platform.getClippingRect({\n    element: ((_await$platform$isEle = await (platform.isElement == null ? void 0 : platform.isElement(element))) != null ? _await$platform$isEle : true) ? element : element.contextElement || (await (platform.getDocumentElement == null ? void 0 : platform.getDocumentElement(elements.floating))),\n    boundary,\n    rootBoundary,\n    strategy\n  }));\n  const rect = elementContext === 'floating' ? {\n    x,\n    y,\n    width: rects.floating.width,\n    height: rects.floating.height\n  } : rects.reference;\n  const offsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(elements.floating));\n  const offsetScale = (await (platform.isElement == null ? void 0 : platform.isElement(offsetParent))) ? (await (platform.getScale == null ? void 0 : platform.getScale(offsetParent))) || {\n    x: 1,\n    y: 1\n  } : {\n    x: 1,\n    y: 1\n  };\n  const elementClientRect = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.rectToClientRect)(platform.convertOffsetParentRelativeRectToViewportRelativeRect ? await platform.convertOffsetParentRelativeRectToViewportRelativeRect({\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  }) : rect);\n  return {\n    top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,\n    bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,\n    left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,\n    right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x\n  };\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = options => ({\n  name: 'arrow',\n  options,\n  async fn(state) {\n    const {\n      x,\n      y,\n      placement,\n      rects,\n      platform,\n      elements,\n      middlewareData\n    } = state;\n    // Since `element` is required, we don't Partial<> the type.\n    const {\n      element,\n      padding = 0\n    } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state) || {};\n    if (element == null) {\n      return {};\n    }\n    const paddingObject = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getPaddingObject)(padding);\n    const coords = {\n      x,\n      y\n    };\n    const axis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignmentAxis)(placement);\n    const length = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAxisLength)(axis);\n    const arrowDimensions = await platform.getDimensions(element);\n    const isYAxis = axis === 'y';\n    const minProp = isYAxis ? 'top' : 'left';\n    const maxProp = isYAxis ? 'bottom' : 'right';\n    const clientProp = isYAxis ? 'clientHeight' : 'clientWidth';\n    const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];\n    const startDiff = coords[axis] - rects.reference[axis];\n    const arrowOffsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(element));\n    let clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0;\n\n    // DOM platform can return `window` as the `offsetParent`.\n    if (!clientSize || !(await (platform.isElement == null ? void 0 : platform.isElement(arrowOffsetParent)))) {\n      clientSize = elements.floating[clientProp] || rects.floating[length];\n    }\n    const centerToReference = endDiff / 2 - startDiff / 2;\n\n    // If the padding is large enough that it causes the arrow to no longer be\n    // centered, modify the padding so that it is centered.\n    const largestPossiblePadding = clientSize / 2 - arrowDimensions[length] / 2 - 1;\n    const minPadding = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(paddingObject[minProp], largestPossiblePadding);\n    const maxPadding = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(paddingObject[maxProp], largestPossiblePadding);\n\n    // Make sure the arrow doesn't overflow the floating element if the center\n    // point is outside the floating element's bounds.\n    const min$1 = minPadding;\n    const max = clientSize - arrowDimensions[length] - maxPadding;\n    const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;\n    const offset = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.clamp)(min$1, center, max);\n\n    // If the reference is small enough that the arrow's padding causes it to\n    // to point to nothing for an aligned placement, adjust the offset of the\n    // floating element itself. To ensure `shift()` continues to take action,\n    // a single reset is performed when this is true.\n    const shouldAddOffset = !middlewareData.arrow && (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement) != null && center !== offset && rects.reference[length] / 2 - (center < min$1 ? minPadding : maxPadding) - arrowDimensions[length] / 2 < 0;\n    const alignmentOffset = shouldAddOffset ? center < min$1 ? center - min$1 : center - max : 0;\n    return {\n      [axis]: coords[axis] + alignmentOffset,\n      data: _objectSpread({\n        [axis]: offset,\n        centerOffset: center - offset - alignmentOffset\n      }, shouldAddOffset && {\n        alignmentOffset\n      }),\n      reset: shouldAddOffset\n    };\n  }\n});\nfunction getPlacementList(alignment, autoAlignment, allowedPlacements) {\n  const allowedPlacementsSortedByAlignment = alignment ? [...allowedPlacements.filter(placement => (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement) === alignment), ...allowedPlacements.filter(placement => (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement) !== alignment)] : allowedPlacements.filter(placement => (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement) === placement);\n  return allowedPlacementsSortedByAlignment.filter(placement => {\n    if (alignment) {\n      return (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement) === alignment || (autoAlignment ? (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getOppositeAlignmentPlacement)(placement) !== placement : false);\n    }\n    return true;\n  });\n}\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'autoPlacement',\n    options,\n    async fn(state) {\n      var _middlewareData$autoP, _middlewareData$autoP2, _placementsThatFitOnE;\n      const {\n        rects,\n        middlewareData,\n        placement,\n        platform,\n        elements\n      } = state;\n      const _evaluate = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state),\n        {\n          crossAxis = false,\n          alignment,\n          allowedPlacements = _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.placements,\n          autoAlignment = true\n        } = _evaluate,\n        detectOverflowOptions = _objectWithoutProperties(_evaluate, _excluded);\n      const placements$1 = alignment !== undefined || allowedPlacements === _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.placements ? getPlacementList(alignment || null, autoAlignment, allowedPlacements) : allowedPlacements;\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const currentIndex = ((_middlewareData$autoP = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP.index) || 0;\n      const currentPlacement = placements$1[currentIndex];\n      if (currentPlacement == null) {\n        return {};\n      }\n      const alignmentSides = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignmentSides)(currentPlacement, rects, await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating)));\n\n      // Make `computeCoords` start from the right place.\n      if (placement !== currentPlacement) {\n        return {\n          reset: {\n            placement: placements$1[0]\n          }\n        };\n      }\n      const currentOverflows = [overflow[(0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(currentPlacement)], overflow[alignmentSides[0]], overflow[alignmentSides[1]]];\n      const allOverflows = [...(((_middlewareData$autoP2 = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP2.overflows) || []), {\n        placement: currentPlacement,\n        overflows: currentOverflows\n      }];\n      const nextPlacement = placements$1[currentIndex + 1];\n\n      // There are more placements to check.\n      if (nextPlacement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: nextPlacement\n          }\n        };\n      }\n      const placementsSortedByMostSpace = allOverflows.map(d => {\n        const alignment = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(d.placement);\n        return [d.placement, alignment && crossAxis ?\n        // Check along the mainAxis and main crossAxis side.\n        d.overflows.slice(0, 2).reduce((acc, v) => acc + v, 0) :\n        // Check only the mainAxis.\n        d.overflows[0], d.overflows];\n      }).sort((a, b) => a[1] - b[1]);\n      const placementsThatFitOnEachSide = placementsSortedByMostSpace.filter(d => d[2].slice(0,\n      // Aligned placements should not check their opposite crossAxis\n      // side.\n      (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(d[0]) ? 2 : 3).every(v => v <= 0));\n      const resetPlacement = ((_placementsThatFitOnE = placementsThatFitOnEachSide[0]) == null ? void 0 : _placementsThatFitOnE[0]) || placementsSortedByMostSpace[0][0];\n      if (resetPlacement !== placement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: resetPlacement\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'flip',\n    options,\n    async fn(state) {\n      var _middlewareData$arrow, _middlewareData$flip;\n      const {\n        placement,\n        middlewareData,\n        rects,\n        initialPlacement,\n        platform,\n        elements\n      } = state;\n      const _evaluate2 = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state),\n        {\n          mainAxis: checkMainAxis = true,\n          crossAxis: checkCrossAxis = true,\n          fallbackPlacements: specifiedFallbackPlacements,\n          fallbackStrategy = 'bestFit',\n          fallbackAxisSideDirection = 'none',\n          flipAlignment = true\n        } = _evaluate2,\n        detectOverflowOptions = _objectWithoutProperties(_evaluate2, _excluded2);\n\n      // If a reset by the arrow was caused due to an alignment offset being\n      // added, we should skip any logic now since `flip()` has already done its\n      // work.\n      // https://github.com/floating-ui/floating-ui/issues/2549#issuecomment-1719601643\n      if ((_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      const side = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement);\n      const initialSideAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(initialPlacement);\n      const isBasePlacement = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(initialPlacement) === initialPlacement;\n      const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n      const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [(0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getOppositePlacement)(initialPlacement)] : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getExpandedPlacements)(initialPlacement));\n      const hasFallbackAxisSideDirection = fallbackAxisSideDirection !== 'none';\n      if (!specifiedFallbackPlacements && hasFallbackAxisSideDirection) {\n        fallbackPlacements.push(...(0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getOppositeAxisPlacements)(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl));\n      }\n      const placements = [initialPlacement, ...fallbackPlacements];\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const overflows = [];\n      let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];\n      if (checkMainAxis) {\n        overflows.push(overflow[side]);\n      }\n      if (checkCrossAxis) {\n        const sides = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignmentSides)(placement, rects, rtl);\n        overflows.push(overflow[sides[0]], overflow[sides[1]]);\n      }\n      overflowsData = [...overflowsData, {\n        placement,\n        overflows\n      }];\n\n      // One or more sides is overflowing.\n      if (!overflows.every(side => side <= 0)) {\n        var _middlewareData$flip2, _overflowsData$filter;\n        const nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;\n        const nextPlacement = placements[nextIndex];\n        if (nextPlacement) {\n          var _overflowsData$;\n          const ignoreCrossAxisOverflow = checkCrossAxis === 'alignment' ? initialSideAxis !== (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(nextPlacement) : false;\n          const hasInitialMainAxisOverflow = ((_overflowsData$ = overflowsData[0]) == null ? void 0 : _overflowsData$.overflows[0]) > 0;\n          if (!ignoreCrossAxisOverflow || hasInitialMainAxisOverflow) {\n            // Try next placement and re-run the lifecycle.\n            return {\n              data: {\n                index: nextIndex,\n                overflows: overflowsData\n              },\n              reset: {\n                placement: nextPlacement\n              }\n            };\n          }\n        }\n\n        // First, find the candidates that fit on the mainAxis side of overflow,\n        // then find the placement that fits the best on the main crossAxis side.\n        let resetPlacement = (_overflowsData$filter = overflowsData.filter(d => d.overflows[0] <= 0).sort((a, b) => a.overflows[1] - b.overflows[1])[0]) == null ? void 0 : _overflowsData$filter.placement;\n\n        // Otherwise fallback.\n        if (!resetPlacement) {\n          switch (fallbackStrategy) {\n            case 'bestFit':\n              {\n                var _overflowsData$filter2;\n                const placement = (_overflowsData$filter2 = overflowsData.filter(d => {\n                  if (hasFallbackAxisSideDirection) {\n                    const currentSideAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(d.placement);\n                    return currentSideAxis === initialSideAxis ||\n                    // Create a bias to the `y` side axis due to horizontal\n                    // reading directions favoring greater width.\n                    currentSideAxis === 'y';\n                  }\n                  return true;\n                }).map(d => [d.placement, d.overflows.filter(overflow => overflow > 0).reduce((acc, overflow) => acc + overflow, 0)]).sort((a, b) => a[1] - b[1])[0]) == null ? void 0 : _overflowsData$filter2[0];\n                if (placement) {\n                  resetPlacement = placement;\n                }\n                break;\n              }\n            case 'initialPlacement':\n              resetPlacement = initialPlacement;\n              break;\n          }\n        }\n        if (placement !== resetPlacement) {\n          return {\n            reset: {\n              placement: resetPlacement\n            }\n          };\n        }\n      }\n      return {};\n    }\n  };\n};\nfunction getSideOffsets(overflow, rect) {\n  return {\n    top: overflow.top - rect.height,\n    right: overflow.right - rect.width,\n    bottom: overflow.bottom - rect.height,\n    left: overflow.left - rect.width\n  };\n}\nfunction isAnySideFullyClipped(overflow) {\n  return _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.sides.some(side => overflow[side] >= 0);\n}\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'hide',\n    options,\n    async fn(state) {\n      const {\n        rects\n      } = state;\n      const _evaluate3 = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state),\n        {\n          strategy = 'referenceHidden'\n        } = _evaluate3,\n        detectOverflowOptions = _objectWithoutProperties(_evaluate3, _excluded3);\n      switch (strategy) {\n        case 'referenceHidden':\n          {\n            const overflow = await detectOverflow(state, _objectSpread(_objectSpread({}, detectOverflowOptions), {}, {\n              elementContext: 'reference'\n            }));\n            const offsets = getSideOffsets(overflow, rects.reference);\n            return {\n              data: {\n                referenceHiddenOffsets: offsets,\n                referenceHidden: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        case 'escaped':\n          {\n            const overflow = await detectOverflow(state, _objectSpread(_objectSpread({}, detectOverflowOptions), {}, {\n              altBoundary: true\n            }));\n            const offsets = getSideOffsets(overflow, rects.floating);\n            return {\n              data: {\n                escapedOffsets: offsets,\n                escaped: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        default:\n          {\n            return {};\n          }\n      }\n    }\n  };\n};\nfunction getBoundingRect(rects) {\n  const minX = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(...rects.map(rect => rect.left));\n  const minY = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(...rects.map(rect => rect.top));\n  const maxX = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(...rects.map(rect => rect.right));\n  const maxY = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(...rects.map(rect => rect.bottom));\n  return {\n    x: minX,\n    y: minY,\n    width: maxX - minX,\n    height: maxY - minY\n  };\n}\nfunction getRectsByLine(rects) {\n  const sortedRects = rects.slice().sort((a, b) => a.y - b.y);\n  const groups = [];\n  let prevRect = null;\n  for (let i = 0; i < sortedRects.length; i++) {\n    const rect = sortedRects[i];\n    if (!prevRect || rect.y - prevRect.y > prevRect.height / 2) {\n      groups.push([rect]);\n    } else {\n      groups[groups.length - 1].push(rect);\n    }\n    prevRect = rect;\n  }\n  return groups.map(rect => (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.rectToClientRect)(getBoundingRect(rect)));\n}\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'inline',\n    options,\n    async fn(state) {\n      const {\n        placement,\n        elements,\n        rects,\n        platform,\n        strategy\n      } = state;\n      // A MouseEvent's client{X,Y} coords can be up to 2 pixels off a\n      // ClientRect's bounds, despite the event listener being triggered. A\n      // padding of 2 seems to handle this issue.\n      const {\n        padding = 2,\n        x,\n        y\n      } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n      const nativeClientRects = Array.from((await (platform.getClientRects == null ? void 0 : platform.getClientRects(elements.reference))) || []);\n      const clientRects = getRectsByLine(nativeClientRects);\n      const fallback = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.rectToClientRect)(getBoundingRect(nativeClientRects));\n      const paddingObject = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getPaddingObject)(padding);\n      function getBoundingClientRect() {\n        // There are two rects and they are disjoined.\n        if (clientRects.length === 2 && clientRects[0].left > clientRects[1].right && x != null && y != null) {\n          // Find the first rect in which the point is fully inside.\n          return clientRects.find(rect => x > rect.left - paddingObject.left && x < rect.right + paddingObject.right && y > rect.top - paddingObject.top && y < rect.bottom + paddingObject.bottom) || fallback;\n        }\n\n        // There are 2 or more connected rects.\n        if (clientRects.length >= 2) {\n          if ((0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(placement) === 'y') {\n            const firstRect = clientRects[0];\n            const lastRect = clientRects[clientRects.length - 1];\n            const isTop = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement) === 'top';\n            const top = firstRect.top;\n            const bottom = lastRect.bottom;\n            const left = isTop ? firstRect.left : lastRect.left;\n            const right = isTop ? firstRect.right : lastRect.right;\n            const width = right - left;\n            const height = bottom - top;\n            return {\n              top,\n              bottom,\n              left,\n              right,\n              width,\n              height,\n              x: left,\n              y: top\n            };\n          }\n          const isLeftSide = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement) === 'left';\n          const maxRight = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(...clientRects.map(rect => rect.right));\n          const minLeft = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(...clientRects.map(rect => rect.left));\n          const measureRects = clientRects.filter(rect => isLeftSide ? rect.left === minLeft : rect.right === maxRight);\n          const top = measureRects[0].top;\n          const bottom = measureRects[measureRects.length - 1].bottom;\n          const left = minLeft;\n          const right = maxRight;\n          const width = right - left;\n          const height = bottom - top;\n          return {\n            top,\n            bottom,\n            left,\n            right,\n            width,\n            height,\n            x: left,\n            y: top\n          };\n        }\n        return fallback;\n      }\n      const resetRects = await platform.getElementRects({\n        reference: {\n          getBoundingClientRect\n        },\n        floating: elements.floating,\n        strategy\n      });\n      if (rects.reference.x !== resetRects.reference.x || rects.reference.y !== resetRects.reference.y || rects.reference.width !== resetRects.reference.width || rects.reference.height !== resetRects.reference.height) {\n        return {\n          reset: {\n            rects: resetRects\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n// For type backwards-compatibility, the `OffsetOptions` type was also\n// Derivable.\n\nasync function convertValueToCoords(state, options) {\n  const {\n    placement,\n    platform,\n    elements\n  } = state;\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n  const side = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement);\n  const alignment = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement);\n  const isVertical = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(placement) === 'y';\n  const mainAxisMulti = ['left', 'top'].includes(side) ? -1 : 1;\n  const crossAxisMulti = rtl && isVertical ? -1 : 1;\n  const rawValue = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n\n  // eslint-disable-next-line prefer-const\n  let {\n    mainAxis,\n    crossAxis,\n    alignmentAxis\n  } = typeof rawValue === 'number' ? {\n    mainAxis: rawValue,\n    crossAxis: 0,\n    alignmentAxis: null\n  } : {\n    mainAxis: rawValue.mainAxis || 0,\n    crossAxis: rawValue.crossAxis || 0,\n    alignmentAxis: rawValue.alignmentAxis\n  };\n  if (alignment && typeof alignmentAxis === 'number') {\n    crossAxis = alignment === 'end' ? alignmentAxis * -1 : alignmentAxis;\n  }\n  return isVertical ? {\n    x: crossAxis * crossAxisMulti,\n    y: mainAxis * mainAxisMulti\n  } : {\n    x: mainAxis * mainAxisMulti,\n    y: crossAxis * crossAxisMulti\n  };\n}\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = function (options) {\n  if (options === void 0) {\n    options = 0;\n  }\n  return {\n    name: 'offset',\n    options,\n    async fn(state) {\n      var _middlewareData$offse, _middlewareData$arrow;\n      const {\n        x,\n        y,\n        placement,\n        middlewareData\n      } = state;\n      const diffCoords = await convertValueToCoords(state, options);\n\n      // If the placement is the same and the arrow caused an alignment offset\n      // then we don't need to change the positioning coordinates.\n      if (placement === ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse.placement) && (_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      return {\n        x: x + diffCoords.x,\n        y: y + diffCoords.y,\n        data: _objectSpread(_objectSpread({}, diffCoords), {}, {\n          placement\n        })\n      };\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'shift',\n    options,\n    async fn(state) {\n      const {\n        x,\n        y,\n        placement\n      } = state;\n      const _evaluate4 = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state),\n        {\n          mainAxis: checkMainAxis = true,\n          crossAxis: checkCrossAxis = false,\n          limiter = {\n            fn: _ref => {\n              let {\n                x,\n                y\n              } = _ref;\n              return {\n                x,\n                y\n              };\n            }\n          }\n        } = _evaluate4,\n        detectOverflowOptions = _objectWithoutProperties(_evaluate4, _excluded4);\n      const coords = {\n        x,\n        y\n      };\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const crossAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)((0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement));\n      const mainAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getOppositeAxis)(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      if (checkMainAxis) {\n        const minSide = mainAxis === 'y' ? 'top' : 'left';\n        const maxSide = mainAxis === 'y' ? 'bottom' : 'right';\n        const min = mainAxisCoord + overflow[minSide];\n        const max = mainAxisCoord - overflow[maxSide];\n        mainAxisCoord = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.clamp)(min, mainAxisCoord, max);\n      }\n      if (checkCrossAxis) {\n        const minSide = crossAxis === 'y' ? 'top' : 'left';\n        const maxSide = crossAxis === 'y' ? 'bottom' : 'right';\n        const min = crossAxisCoord + overflow[minSide];\n        const max = crossAxisCoord - overflow[maxSide];\n        crossAxisCoord = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.clamp)(min, crossAxisCoord, max);\n      }\n      const limitedCoords = limiter.fn(_objectSpread(_objectSpread({}, state), {}, {\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      }));\n      return _objectSpread(_objectSpread({}, limitedCoords), {}, {\n        data: {\n          x: limitedCoords.x - x,\n          y: limitedCoords.y - y,\n          enabled: {\n            [mainAxis]: checkMainAxis,\n            [crossAxis]: checkCrossAxis\n          }\n        }\n      });\n    }\n  };\n};\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    options,\n    fn(state) {\n      const {\n        x,\n        y,\n        placement,\n        rects,\n        middlewareData\n      } = state;\n      const {\n        offset = 0,\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true\n      } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const crossAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(placement);\n      const mainAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getOppositeAxis)(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      const rawOffset = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(offset, state);\n      const computedOffset = typeof rawOffset === 'number' ? {\n        mainAxis: rawOffset,\n        crossAxis: 0\n      } : _objectSpread({\n        mainAxis: 0,\n        crossAxis: 0\n      }, rawOffset);\n      if (checkMainAxis) {\n        const len = mainAxis === 'y' ? 'height' : 'width';\n        const limitMin = rects.reference[mainAxis] - rects.floating[len] + computedOffset.mainAxis;\n        const limitMax = rects.reference[mainAxis] + rects.reference[len] - computedOffset.mainAxis;\n        if (mainAxisCoord < limitMin) {\n          mainAxisCoord = limitMin;\n        } else if (mainAxisCoord > limitMax) {\n          mainAxisCoord = limitMax;\n        }\n      }\n      if (checkCrossAxis) {\n        var _middlewareData$offse, _middlewareData$offse2;\n        const len = mainAxis === 'y' ? 'width' : 'height';\n        const isOriginSide = ['top', 'left'].includes((0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement));\n        const limitMin = rects.reference[crossAxis] - rects.floating[len] + (isOriginSide ? ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse[crossAxis]) || 0 : 0) + (isOriginSide ? 0 : computedOffset.crossAxis);\n        const limitMax = rects.reference[crossAxis] + rects.reference[len] + (isOriginSide ? 0 : ((_middlewareData$offse2 = middlewareData.offset) == null ? void 0 : _middlewareData$offse2[crossAxis]) || 0) - (isOriginSide ? computedOffset.crossAxis : 0);\n        if (crossAxisCoord < limitMin) {\n          crossAxisCoord = limitMin;\n        } else if (crossAxisCoord > limitMax) {\n          crossAxisCoord = limitMax;\n        }\n      }\n      return {\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      };\n    }\n  };\n};\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'size',\n    options,\n    async fn(state) {\n      var _state$middlewareData, _state$middlewareData2;\n      const {\n        placement,\n        rects,\n        platform,\n        elements\n      } = state;\n      const _evaluate5 = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state),\n        {\n          apply = () => {}\n        } = _evaluate5,\n        detectOverflowOptions = _objectWithoutProperties(_evaluate5, _excluded5);\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const side = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement);\n      const alignment = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement);\n      const isYAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(placement) === 'y';\n      const {\n        width,\n        height\n      } = rects.floating;\n      let heightSide;\n      let widthSide;\n      if (side === 'top' || side === 'bottom') {\n        heightSide = side;\n        widthSide = alignment === ((await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating))) ? 'start' : 'end') ? 'left' : 'right';\n      } else {\n        widthSide = side;\n        heightSide = alignment === 'end' ? 'top' : 'bottom';\n      }\n      const maximumClippingHeight = height - overflow.top - overflow.bottom;\n      const maximumClippingWidth = width - overflow.left - overflow.right;\n      const overflowAvailableHeight = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(height - overflow[heightSide], maximumClippingHeight);\n      const overflowAvailableWidth = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(width - overflow[widthSide], maximumClippingWidth);\n      const noShift = !state.middlewareData.shift;\n      let availableHeight = overflowAvailableHeight;\n      let availableWidth = overflowAvailableWidth;\n      if ((_state$middlewareData = state.middlewareData.shift) != null && _state$middlewareData.enabled.x) {\n        availableWidth = maximumClippingWidth;\n      }\n      if ((_state$middlewareData2 = state.middlewareData.shift) != null && _state$middlewareData2.enabled.y) {\n        availableHeight = maximumClippingHeight;\n      }\n      if (noShift && !alignment) {\n        const xMin = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.left, 0);\n        const xMax = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.right, 0);\n        const yMin = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.top, 0);\n        const yMax = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.bottom, 0);\n        if (isYAxis) {\n          availableWidth = width - 2 * (xMin !== 0 || xMax !== 0 ? xMin + xMax : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.left, overflow.right));\n        } else {\n          availableHeight = height - 2 * (yMin !== 0 || yMax !== 0 ? yMin + yMax : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.top, overflow.bottom));\n        }\n      }\n      await apply(_objectSpread(_objectSpread({}, state), {}, {\n        availableWidth,\n        availableHeight\n      }));\n      const nextDimensions = await platform.getDimensions(elements.floating);\n      if (width !== nextDimensions.width || height !== nextDimensions.height) {\n        return {\n          reset: {\n            rects: true\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@floating-ui/core/dist/floating-ui.core.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   arrow: () => (/* binding */ arrow),\n/* harmony export */   autoPlacement: () => (/* binding */ autoPlacement),\n/* harmony export */   autoUpdate: () => (/* binding */ autoUpdate),\n/* harmony export */   computePosition: () => (/* binding */ computePosition),\n/* harmony export */   detectOverflow: () => (/* binding */ detectOverflow),\n/* harmony export */   flip: () => (/* binding */ flip),\n/* harmony export */   getOverflowAncestors: () => (/* reexport safe */ _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getOverflowAncestors),\n/* harmony export */   hide: () => (/* binding */ hide),\n/* harmony export */   inline: () => (/* binding */ inline),\n/* harmony export */   limitShift: () => (/* binding */ limitShift),\n/* harmony export */   offset: () => (/* binding */ offset),\n/* harmony export */   platform: () => (/* binding */ platform),\n/* harmony export */   shift: () => (/* binding */ shift),\n/* harmony export */   size: () => (/* binding */ size)\n/* harmony export */ });\n/* harmony import */ var _floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @floating-ui/utils */ \"(ssr)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs\");\n/* harmony import */ var _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @floating-ui/core */ \"(ssr)/./node_modules/@floating-ui/core/dist/floating-ui.core.mjs\");\n/* harmony import */ var _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @floating-ui/utils/dom */ \"(ssr)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs\");\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return typeof key === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (typeof input !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (typeof res !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\n\n\n\n\nfunction getCssDimensions(element) {\n  const css = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(element);\n  // In testing environments, the `width` and `height` properties are empty\n  // strings for SVG elements, returning NaN. Fallback to `0` in this case.\n  let width = parseFloat(css.width) || 0;\n  let height = parseFloat(css.height) || 0;\n  const hasOffset = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element);\n  const offsetWidth = hasOffset ? element.offsetWidth : width;\n  const offsetHeight = hasOffset ? element.offsetHeight : height;\n  const shouldFallback = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.round)(width) !== offsetWidth || (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.round)(height) !== offsetHeight;\n  if (shouldFallback) {\n    width = offsetWidth;\n    height = offsetHeight;\n  }\n  return {\n    width,\n    height,\n    $: shouldFallback\n  };\n}\nfunction unwrapElement(element) {\n  return !(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(element) ? element.contextElement : element;\n}\nfunction getScale(element) {\n  const domElement = unwrapElement(element);\n  if (!(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(domElement)) {\n    return (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(1);\n  }\n  const rect = domElement.getBoundingClientRect();\n  const {\n    width,\n    height,\n    $\n  } = getCssDimensions(domElement);\n  let x = ($ ? (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.round)(rect.width) : rect.width) / width;\n  let y = ($ ? (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.round)(rect.height) : rect.height) / height;\n\n  // 0, NaN, or Infinity should always fallback to 1.\n\n  if (!x || !Number.isFinite(x)) {\n    x = 1;\n  }\n  if (!y || !Number.isFinite(y)) {\n    y = 1;\n  }\n  return {\n    x,\n    y\n  };\n}\nconst noOffsets = /*#__PURE__*/(0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(0);\nfunction getVisualOffsets(element) {\n  const win = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(element);\n  if (!(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isWebKit)() || !win.visualViewport) {\n    return noOffsets;\n  }\n  return {\n    x: win.visualViewport.offsetLeft,\n    y: win.visualViewport.offsetTop\n  };\n}\nfunction shouldAddVisualOffsets(element, isFixed, floatingOffsetParent) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n  if (!floatingOffsetParent || isFixed && floatingOffsetParent !== (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(element)) {\n    return false;\n  }\n  return isFixed;\n}\nfunction getBoundingClientRect(element, includeScale, isFixedStrategy, offsetParent) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n  const clientRect = element.getBoundingClientRect();\n  const domElement = unwrapElement(element);\n  let scale = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(1);\n  if (includeScale) {\n    if (offsetParent) {\n      if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(offsetParent)) {\n        scale = getScale(offsetParent);\n      }\n    } else {\n      scale = getScale(element);\n    }\n  }\n  const visualOffsets = shouldAddVisualOffsets(domElement, isFixedStrategy, offsetParent) ? getVisualOffsets(domElement) : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(0);\n  let x = (clientRect.left + visualOffsets.x) / scale.x;\n  let y = (clientRect.top + visualOffsets.y) / scale.y;\n  let width = clientRect.width / scale.x;\n  let height = clientRect.height / scale.y;\n  if (domElement) {\n    const win = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(domElement);\n    const offsetWin = offsetParent && (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(offsetParent) ? (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(offsetParent) : offsetParent;\n    let currentWin = win;\n    let currentIFrame = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getFrameElement)(currentWin);\n    while (currentIFrame && offsetParent && offsetWin !== currentWin) {\n      const iframeScale = getScale(currentIFrame);\n      const iframeRect = currentIFrame.getBoundingClientRect();\n      const css = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(currentIFrame);\n      const left = iframeRect.left + (currentIFrame.clientLeft + parseFloat(css.paddingLeft)) * iframeScale.x;\n      const top = iframeRect.top + (currentIFrame.clientTop + parseFloat(css.paddingTop)) * iframeScale.y;\n      x *= iframeScale.x;\n      y *= iframeScale.y;\n      width *= iframeScale.x;\n      height *= iframeScale.y;\n      x += left;\n      y += top;\n      currentWin = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(currentIFrame);\n      currentIFrame = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getFrameElement)(currentWin);\n    }\n  }\n  return (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.rectToClientRect)({\n    width,\n    height,\n    x,\n    y\n  });\n}\n\n// If <html> has a CSS width greater than the viewport, then this will be\n// incorrect for RTL.\nfunction getWindowScrollBarX(element, rect) {\n  const leftScroll = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeScroll)(element).scrollLeft;\n  if (!rect) {\n    return getBoundingClientRect((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(element)).left + leftScroll;\n  }\n  return rect.left + leftScroll;\n}\nfunction getHTMLOffset(documentElement, scroll, ignoreScrollbarX) {\n  if (ignoreScrollbarX === void 0) {\n    ignoreScrollbarX = false;\n  }\n  const htmlRect = documentElement.getBoundingClientRect();\n  const x = htmlRect.left + scroll.scrollLeft - (ignoreScrollbarX ? 0 :\n  // RTL <body> scrollbar.\n  getWindowScrollBarX(documentElement, htmlRect));\n  const y = htmlRect.top + scroll.scrollTop;\n  return {\n    x,\n    y\n  };\n}\nfunction convertOffsetParentRelativeRectToViewportRelativeRect(_ref) {\n  let {\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  } = _ref;\n  const isFixed = strategy === 'fixed';\n  const documentElement = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(offsetParent);\n  const topLayer = elements ? (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isTopLayer)(elements.floating) : false;\n  if (offsetParent === documentElement || topLayer && isFixed) {\n    return rect;\n  }\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  let scale = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(1);\n  const offsets = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(0);\n  const isOffsetParentAnElement = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(offsetParent);\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeName)(offsetParent) !== 'body' || (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isOverflowElement)(documentElement)) {\n      scroll = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeScroll)(offsetParent);\n    }\n    if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(offsetParent)) {\n      const offsetRect = getBoundingClientRect(offsetParent);\n      scale = getScale(offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    }\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll, true) : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(0);\n  return {\n    width: rect.width * scale.x,\n    height: rect.height * scale.y,\n    x: rect.x * scale.x - scroll.scrollLeft * scale.x + offsets.x + htmlOffset.x,\n    y: rect.y * scale.y - scroll.scrollTop * scale.y + offsets.y + htmlOffset.y\n  };\n}\nfunction getClientRects(element) {\n  return Array.from(element.getClientRects());\n}\n\n// Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable.\nfunction getDocumentRect(element) {\n  const html = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(element);\n  const scroll = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeScroll)(element);\n  const body = element.ownerDocument.body;\n  const width = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.max)(html.scrollWidth, html.clientWidth, body.scrollWidth, body.clientWidth);\n  const height = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.max)(html.scrollHeight, html.clientHeight, body.scrollHeight, body.clientHeight);\n  let x = -scroll.scrollLeft + getWindowScrollBarX(element);\n  const y = -scroll.scrollTop;\n  if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(body).direction === 'rtl') {\n    x += (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.max)(html.clientWidth, body.clientWidth) - width;\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\nfunction getViewportRect(element, strategy) {\n  const win = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(element);\n  const html = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(element);\n  const visualViewport = win.visualViewport;\n  let width = html.clientWidth;\n  let height = html.clientHeight;\n  let x = 0;\n  let y = 0;\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    const visualViewportBased = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isWebKit)();\n    if (!visualViewportBased || visualViewportBased && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\n// Returns the inner client rect, subtracting scrollbars if present.\nfunction getInnerBoundingClientRect(element, strategy) {\n  const clientRect = getBoundingClientRect(element, true, strategy === 'fixed');\n  const top = clientRect.top + element.clientTop;\n  const left = clientRect.left + element.clientLeft;\n  const scale = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element) ? getScale(element) : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(1);\n  const width = element.clientWidth * scale.x;\n  const height = element.clientHeight * scale.y;\n  const x = left * scale.x;\n  const y = top * scale.y;\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\nfunction getClientRectFromClippingAncestor(element, clippingAncestor, strategy) {\n  let rect;\n  if (clippingAncestor === 'viewport') {\n    rect = getViewportRect(element, strategy);\n  } else if (clippingAncestor === 'document') {\n    rect = getDocumentRect((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(element));\n  } else if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(clippingAncestor)) {\n    rect = getInnerBoundingClientRect(clippingAncestor, strategy);\n  } else {\n    const visualOffsets = getVisualOffsets(element);\n    rect = {\n      x: clippingAncestor.x - visualOffsets.x,\n      y: clippingAncestor.y - visualOffsets.y,\n      width: clippingAncestor.width,\n      height: clippingAncestor.height\n    };\n  }\n  return (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.rectToClientRect)(rect);\n}\nfunction hasFixedPositionAncestor(element, stopNode) {\n  const parentNode = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getParentNode)(element);\n  if (parentNode === stopNode || !(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(parentNode) || (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isLastTraversableNode)(parentNode)) {\n    return false;\n  }\n  return (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(parentNode).position === 'fixed' || hasFixedPositionAncestor(parentNode, stopNode);\n}\n\n// A \"clipping ancestor\" is an `overflow` element with the characteristic of\n// clipping (or hiding) child elements. This returns all clipping ancestors\n// of the given element up the tree.\nfunction getClippingElementAncestors(element, cache) {\n  const cachedResult = cache.get(element);\n  if (cachedResult) {\n    return cachedResult;\n  }\n  let result = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getOverflowAncestors)(element, [], false).filter(el => (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(el) && (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeName)(el) !== 'body');\n  let currentContainingBlockComputedStyle = null;\n  const elementIsFixed = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(element).position === 'fixed';\n  let currentNode = elementIsFixed ? (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getParentNode)(element) : element;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  while ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(currentNode) && !(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isLastTraversableNode)(currentNode)) {\n    const computedStyle = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(currentNode);\n    const currentNodeIsContaining = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isContainingBlock)(currentNode);\n    if (!currentNodeIsContaining && computedStyle.position === 'fixed') {\n      currentContainingBlockComputedStyle = null;\n    }\n    const shouldDropCurrentNode = elementIsFixed ? !currentNodeIsContaining && !currentContainingBlockComputedStyle : !currentNodeIsContaining && computedStyle.position === 'static' && !!currentContainingBlockComputedStyle && ['absolute', 'fixed'].includes(currentContainingBlockComputedStyle.position) || (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isOverflowElement)(currentNode) && !currentNodeIsContaining && hasFixedPositionAncestor(element, currentNode);\n    if (shouldDropCurrentNode) {\n      // Drop non-containing blocks.\n      result = result.filter(ancestor => ancestor !== currentNode);\n    } else {\n      // Record last containing block for next iteration.\n      currentContainingBlockComputedStyle = computedStyle;\n    }\n    currentNode = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getParentNode)(currentNode);\n  }\n  cache.set(element, result);\n  return result;\n}\n\n// Gets the maximum area that the element is visible in due to any number of\n// clipping ancestors.\nfunction getClippingRect(_ref) {\n  let {\n    element,\n    boundary,\n    rootBoundary,\n    strategy\n  } = _ref;\n  const elementClippingAncestors = boundary === 'clippingAncestors' ? (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isTopLayer)(element) ? [] : getClippingElementAncestors(element, this._c) : [].concat(boundary);\n  const clippingAncestors = [...elementClippingAncestors, rootBoundary];\n  const firstClippingAncestor = clippingAncestors[0];\n  const clippingRect = clippingAncestors.reduce((accRect, clippingAncestor) => {\n    const rect = getClientRectFromClippingAncestor(element, clippingAncestor, strategy);\n    accRect.top = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.max)(rect.top, accRect.top);\n    accRect.right = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.min)(rect.right, accRect.right);\n    accRect.bottom = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.min)(rect.bottom, accRect.bottom);\n    accRect.left = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.max)(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromClippingAncestor(element, firstClippingAncestor, strategy));\n  return {\n    width: clippingRect.right - clippingRect.left,\n    height: clippingRect.bottom - clippingRect.top,\n    x: clippingRect.left,\n    y: clippingRect.top\n  };\n}\nfunction getDimensions(element) {\n  const {\n    width,\n    height\n  } = getCssDimensions(element);\n  return {\n    width,\n    height\n  };\n}\nfunction getRectRelativeToOffsetParent(element, offsetParent, strategy) {\n  const isOffsetParentAnElement = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(offsetParent);\n  const documentElement = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(offsetParent);\n  const isFixed = strategy === 'fixed';\n  const rect = getBoundingClientRect(element, true, isFixed, offsetParent);\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  const offsets = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(0);\n\n  // If the <body> scrollbar appears on the left (e.g. RTL systems). Use\n  // Firefox with layout.scrollbar.side = 3 in about:config to test this.\n  function setLeftRTLScrollbarOffset() {\n    offsets.x = getWindowScrollBarX(documentElement);\n  }\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeName)(offsetParent) !== 'body' || (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isOverflowElement)(documentElement)) {\n      scroll = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeScroll)(offsetParent);\n    }\n    if (isOffsetParentAnElement) {\n      const offsetRect = getBoundingClientRect(offsetParent, true, isFixed, offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    } else if (documentElement) {\n      setLeftRTLScrollbarOffset();\n    }\n  }\n  if (isFixed && !isOffsetParentAnElement && documentElement) {\n    setLeftRTLScrollbarOffset();\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll) : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(0);\n  const x = rect.left + scroll.scrollLeft - offsets.x - htmlOffset.x;\n  const y = rect.top + scroll.scrollTop - offsets.y - htmlOffset.y;\n  return {\n    x,\n    y,\n    width: rect.width,\n    height: rect.height\n  };\n}\nfunction isStaticPositioned(element) {\n  return (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(element).position === 'static';\n}\nfunction getTrueOffsetParent(element, polyfill) {\n  if (!(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element) || (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(element).position === 'fixed') {\n    return null;\n  }\n  if (polyfill) {\n    return polyfill(element);\n  }\n  let rawOffsetParent = element.offsetParent;\n\n  // Firefox returns the <html> element as the offsetParent if it's non-static,\n  // while Chrome and Safari return the <body> element. The <body> element must\n  // be used to perform the correct calculations even if the <html> element is\n  // non-static.\n  if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(element) === rawOffsetParent) {\n    rawOffsetParent = rawOffsetParent.ownerDocument.body;\n  }\n  return rawOffsetParent;\n}\n\n// Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\nfunction getOffsetParent(element, polyfill) {\n  const win = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(element);\n  if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isTopLayer)(element)) {\n    return win;\n  }\n  if (!(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element)) {\n    let svgOffsetParent = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getParentNode)(element);\n    while (svgOffsetParent && !(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isLastTraversableNode)(svgOffsetParent)) {\n      if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(svgOffsetParent) && !isStaticPositioned(svgOffsetParent)) {\n        return svgOffsetParent;\n      }\n      svgOffsetParent = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getParentNode)(svgOffsetParent);\n    }\n    return win;\n  }\n  let offsetParent = getTrueOffsetParent(element, polyfill);\n  while (offsetParent && (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isTableElement)(offsetParent) && isStaticPositioned(offsetParent)) {\n    offsetParent = getTrueOffsetParent(offsetParent, polyfill);\n  }\n  if (offsetParent && (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isLastTraversableNode)(offsetParent) && isStaticPositioned(offsetParent) && !(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isContainingBlock)(offsetParent)) {\n    return win;\n  }\n  return offsetParent || (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getContainingBlock)(element) || win;\n}\nconst getElementRects = async function (data) {\n  const getOffsetParentFn = this.getOffsetParent || getOffsetParent;\n  const getDimensionsFn = this.getDimensions;\n  const floatingDimensions = await getDimensionsFn(data.floating);\n  return {\n    reference: getRectRelativeToOffsetParent(data.reference, await getOffsetParentFn(data.floating), data.strategy),\n    floating: {\n      x: 0,\n      y: 0,\n      width: floatingDimensions.width,\n      height: floatingDimensions.height\n    }\n  };\n};\nfunction isRTL(element) {\n  return (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(element).direction === 'rtl';\n}\nconst platform = {\n  convertOffsetParentRelativeRectToViewportRelativeRect,\n  getDocumentElement: _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement,\n  getClippingRect,\n  getOffsetParent,\n  getElementRects,\n  getClientRects,\n  getDimensions,\n  getScale,\n  isElement: _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement,\n  isRTL\n};\nfunction rectsAreEqual(a, b) {\n  return a.x === b.x && a.y === b.y && a.width === b.width && a.height === b.height;\n}\n\n// https://samthor.au/2021/observing-dom/\nfunction observeMove(element, onMove) {\n  let io = null;\n  let timeoutId;\n  const root = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(element);\n  function cleanup() {\n    var _io;\n    clearTimeout(timeoutId);\n    (_io = io) == null || _io.disconnect();\n    io = null;\n  }\n  function refresh(skip, threshold) {\n    if (skip === void 0) {\n      skip = false;\n    }\n    if (threshold === void 0) {\n      threshold = 1;\n    }\n    cleanup();\n    const elementRectForRootMargin = element.getBoundingClientRect();\n    const {\n      left,\n      top,\n      width,\n      height\n    } = elementRectForRootMargin;\n    if (!skip) {\n      onMove();\n    }\n    if (!width || !height) {\n      return;\n    }\n    const insetTop = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.floor)(top);\n    const insetRight = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.floor)(root.clientWidth - (left + width));\n    const insetBottom = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.floor)(root.clientHeight - (top + height));\n    const insetLeft = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.floor)(left);\n    const rootMargin = -insetTop + \"px \" + -insetRight + \"px \" + -insetBottom + \"px \" + -insetLeft + \"px\";\n    const options = {\n      rootMargin,\n      threshold: (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.max)(0, (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.min)(1, threshold)) || 1\n    };\n    let isFirstUpdate = true;\n    function handleObserve(entries) {\n      const ratio = entries[0].intersectionRatio;\n      if (ratio !== threshold) {\n        if (!isFirstUpdate) {\n          return refresh();\n        }\n        if (!ratio) {\n          // If the reference is clipped, the ratio is 0. Throttle the refresh\n          // to prevent an infinite loop of updates.\n          timeoutId = setTimeout(() => {\n            refresh(false, 1e-7);\n          }, 1000);\n        } else {\n          refresh(false, ratio);\n        }\n      }\n      if (ratio === 1 && !rectsAreEqual(elementRectForRootMargin, element.getBoundingClientRect())) {\n        // It's possible that even though the ratio is reported as 1, the\n        // element is not actually fully within the IntersectionObserver's root\n        // area anymore. This can happen under performance constraints. This may\n        // be a bug in the browser's IntersectionObserver implementation. To\n        // work around this, we compare the element's bounding rect now with\n        // what it was at the time we created the IntersectionObserver. If they\n        // are not equal then the element moved, so we refresh.\n        refresh();\n      }\n      isFirstUpdate = false;\n    }\n\n    // Older browsers don't support a `document` as the root and will throw an\n    // error.\n    try {\n      io = new IntersectionObserver(handleObserve, _objectSpread(_objectSpread({}, options), {}, {\n        // Handle <iframe>s\n        root: root.ownerDocument\n      }));\n    } catch (_e) {\n      io = new IntersectionObserver(handleObserve, options);\n    }\n    io.observe(element);\n  }\n  refresh(true);\n  return cleanup;\n}\n\n/**\n * Automatically updates the position of the floating element when necessary.\n * Should only be called when the floating element is mounted on the DOM or\n * visible on the screen.\n * @returns cleanup function that should be invoked when the floating element is\n * removed from the DOM or hidden from the screen.\n * @see https://floating-ui.com/docs/autoUpdate\n */\nfunction autoUpdate(reference, floating, update, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    ancestorScroll = true,\n    ancestorResize = true,\n    elementResize = typeof ResizeObserver === 'function',\n    layoutShift = typeof IntersectionObserver === 'function',\n    animationFrame = false\n  } = options;\n  const referenceEl = unwrapElement(reference);\n  const ancestors = ancestorScroll || ancestorResize ? [...(referenceEl ? (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getOverflowAncestors)(referenceEl) : []), ...(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getOverflowAncestors)(floating)] : [];\n  ancestors.forEach(ancestor => {\n    ancestorScroll && ancestor.addEventListener('scroll', update, {\n      passive: true\n    });\n    ancestorResize && ancestor.addEventListener('resize', update);\n  });\n  const cleanupIo = referenceEl && layoutShift ? observeMove(referenceEl, update) : null;\n  let reobserveFrame = -1;\n  let resizeObserver = null;\n  if (elementResize) {\n    resizeObserver = new ResizeObserver(_ref => {\n      let [firstEntry] = _ref;\n      if (firstEntry && firstEntry.target === referenceEl && resizeObserver) {\n        // Prevent update loops when using the `size` middleware.\n        // https://github.com/floating-ui/floating-ui/issues/1740\n        resizeObserver.unobserve(floating);\n        cancelAnimationFrame(reobserveFrame);\n        reobserveFrame = requestAnimationFrame(() => {\n          var _resizeObserver;\n          (_resizeObserver = resizeObserver) == null || _resizeObserver.observe(floating);\n        });\n      }\n      update();\n    });\n    if (referenceEl && !animationFrame) {\n      resizeObserver.observe(referenceEl);\n    }\n    resizeObserver.observe(floating);\n  }\n  let frameId;\n  let prevRefRect = animationFrame ? getBoundingClientRect(reference) : null;\n  if (animationFrame) {\n    frameLoop();\n  }\n  function frameLoop() {\n    const nextRefRect = getBoundingClientRect(reference);\n    if (prevRefRect && !rectsAreEqual(prevRefRect, nextRefRect)) {\n      update();\n    }\n    prevRefRect = nextRefRect;\n    frameId = requestAnimationFrame(frameLoop);\n  }\n  update();\n  return () => {\n    var _resizeObserver2;\n    ancestors.forEach(ancestor => {\n      ancestorScroll && ancestor.removeEventListener('scroll', update);\n      ancestorResize && ancestor.removeEventListener('resize', update);\n    });\n    cleanupIo == null || cleanupIo();\n    (_resizeObserver2 = resizeObserver) == null || _resizeObserver2.disconnect();\n    resizeObserver = null;\n    if (animationFrame) {\n      cancelAnimationFrame(frameId);\n    }\n  };\n}\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nconst detectOverflow = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.detectOverflow;\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.offset;\n\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.autoPlacement;\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.shift;\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.flip;\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.size;\n\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.hide;\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.arrow;\n\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.inline;\n\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.limitShift;\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n */\nconst computePosition = (reference, floating, options) => {\n  // This caches the expensive `getClippingElementAncestors` function so that\n  // multiple lifecycle resets re-use the same result. It only lives for a\n  // single call. If other functions become expensive, we can add them as well.\n  const cache = new Map();\n  const mergedOptions = _objectSpread({\n    platform\n  }, options);\n  const platformWithCache = _objectSpread(_objectSpread({}, mergedOptions.platform), {}, {\n    _c: cache\n  });\n  return (0,_floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.computePosition)(reference, floating, _objectSpread(_objectSpread({}, mergedOptions), {}, {\n    platform: platformWithCache\n  }));\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getComputedStyle: () => (/* binding */ getComputedStyle),\n/* harmony export */   getContainingBlock: () => (/* binding */ getContainingBlock),\n/* harmony export */   getDocumentElement: () => (/* binding */ getDocumentElement),\n/* harmony export */   getFrameElement: () => (/* binding */ getFrameElement),\n/* harmony export */   getNearestOverflowAncestor: () => (/* binding */ getNearestOverflowAncestor),\n/* harmony export */   getNodeName: () => (/* binding */ getNodeName),\n/* harmony export */   getNodeScroll: () => (/* binding */ getNodeScroll),\n/* harmony export */   getOverflowAncestors: () => (/* binding */ getOverflowAncestors),\n/* harmony export */   getParentNode: () => (/* binding */ getParentNode),\n/* harmony export */   getWindow: () => (/* binding */ getWindow),\n/* harmony export */   isContainingBlock: () => (/* binding */ isContainingBlock),\n/* harmony export */   isElement: () => (/* binding */ isElement),\n/* harmony export */   isHTMLElement: () => (/* binding */ isHTMLElement),\n/* harmony export */   isLastTraversableNode: () => (/* binding */ isLastTraversableNode),\n/* harmony export */   isNode: () => (/* binding */ isNode),\n/* harmony export */   isOverflowElement: () => (/* binding */ isOverflowElement),\n/* harmony export */   isShadowRoot: () => (/* binding */ isShadowRoot),\n/* harmony export */   isTableElement: () => (/* binding */ isTableElement),\n/* harmony export */   isTopLayer: () => (/* binding */ isTopLayer),\n/* harmony export */   isWebKit: () => (/* binding */ isWebKit)\n/* harmony export */ });\nfunction hasWindow() {\n  return false;\n}\nfunction getNodeName(node) {\n  if (isNode(node)) {\n    return (node.nodeName || '').toLowerCase();\n  }\n  // Mocked nodes in testing environments may not be instances of Node. By\n  // returning `#document` an infinite loop won't occur.\n  // https://github.com/floating-ui/floating-ui/issues/2317\n  return '#document';\n}\nfunction getWindow(node) {\n  var _node$ownerDocument;\n  return (node == null || (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;\n}\nfunction getDocumentElement(node) {\n  var _ref;\n  return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;\n}\nfunction isNode(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Node || value instanceof getWindow(value).Node;\n}\nfunction isElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Element || value instanceof getWindow(value).Element;\n}\nfunction isHTMLElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;\n}\nfunction isShadowRoot(value) {\n  if (!hasWindow() || typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;\n}\nfunction isOverflowElement(element) {\n  const {\n    overflow,\n    overflowX,\n    overflowY,\n    display\n  } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !['inline', 'contents'].includes(display);\n}\nfunction isTableElement(element) {\n  return ['table', 'td', 'th'].includes(getNodeName(element));\n}\nfunction isTopLayer(element) {\n  return [':popover-open', ':modal'].some(selector => {\n    try {\n      return element.matches(selector);\n    } catch (e) {\n      return false;\n    }\n  });\n}\nfunction isContainingBlock(elementOrCss) {\n  const webkit = isWebKit();\n  const css = isElement(elementOrCss) ? getComputedStyle(elementOrCss) : elementOrCss;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  // https://drafts.csswg.org/css-transforms-2/#individual-transforms\n  return ['transform', 'translate', 'scale', 'rotate', 'perspective'].some(value => css[value] ? css[value] !== 'none' : false) || (css.containerType ? css.containerType !== 'normal' : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== 'none' : false) || !webkit && (css.filter ? css.filter !== 'none' : false) || ['transform', 'translate', 'scale', 'rotate', 'perspective', 'filter'].some(value => (css.willChange || '').includes(value)) || ['paint', 'layout', 'strict', 'content'].some(value => (css.contain || '').includes(value));\n}\nfunction getContainingBlock(element) {\n  let currentNode = getParentNode(element);\n  while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    if (isContainingBlock(currentNode)) {\n      return currentNode;\n    } else if (isTopLayer(currentNode)) {\n      return null;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  return null;\n}\nfunction isWebKit() {\n  if (typeof CSS === 'undefined' || !CSS.supports) return false;\n  return CSS.supports('-webkit-backdrop-filter', 'none');\n}\nfunction isLastTraversableNode(node) {\n  return ['html', 'body', '#document'].includes(getNodeName(node));\n}\nfunction getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}\nfunction getNodeScroll(element) {\n  if (isElement(element)) {\n    return {\n      scrollLeft: element.scrollLeft,\n      scrollTop: element.scrollTop\n    };\n  }\n  return {\n    scrollLeft: element.scrollX,\n    scrollTop: element.scrollY\n  };\n}\nfunction getParentNode(node) {\n  if (getNodeName(node) === 'html') {\n    return node;\n  }\n  const result =\n  // Step into the shadow DOM of the parent of a slotted node.\n  node.assignedSlot ||\n  // DOM Element detected.\n  node.parentNode ||\n  // ShadowRoot detected.\n  isShadowRoot(node) && node.host ||\n  // Fallback.\n  getDocumentElement(node);\n  return isShadowRoot(result) ? result.host : result;\n}\nfunction getNearestOverflowAncestor(node) {\n  const parentNode = getParentNode(node);\n  if (isLastTraversableNode(parentNode)) {\n    return node.ownerDocument ? node.ownerDocument.body : node.body;\n  }\n  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {\n    return parentNode;\n  }\n  return getNearestOverflowAncestor(parentNode);\n}\nfunction getOverflowAncestors(node, list, traverseIframes) {\n  var _node$ownerDocument2;\n  if (list === void 0) {\n    list = [];\n  }\n  if (traverseIframes === void 0) {\n    traverseIframes = true;\n  }\n  const scrollableAncestor = getNearestOverflowAncestor(node);\n  const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);\n  const win = getWindow(scrollableAncestor);\n  if (isBody) {\n    const frameElement = getFrameElement(win);\n    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], frameElement && traverseIframes ? getOverflowAncestors(frameElement) : []);\n  }\n  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, [], traverseIframes));\n}\nfunction getFrameElement(win) {\n  return win.parent && Object.getPrototypeOf(win.parent) ? win.frameElement : null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs":
/*!********************************************************************!*\
  !*** ./node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alignments: () => (/* binding */ alignments),\n/* harmony export */   clamp: () => (/* binding */ clamp),\n/* harmony export */   createCoords: () => (/* binding */ createCoords),\n/* harmony export */   evaluate: () => (/* binding */ evaluate),\n/* harmony export */   expandPaddingObject: () => (/* binding */ expandPaddingObject),\n/* harmony export */   floor: () => (/* binding */ floor),\n/* harmony export */   getAlignment: () => (/* binding */ getAlignment),\n/* harmony export */   getAlignmentAxis: () => (/* binding */ getAlignmentAxis),\n/* harmony export */   getAlignmentSides: () => (/* binding */ getAlignmentSides),\n/* harmony export */   getAxisLength: () => (/* binding */ getAxisLength),\n/* harmony export */   getExpandedPlacements: () => (/* binding */ getExpandedPlacements),\n/* harmony export */   getOppositeAlignmentPlacement: () => (/* binding */ getOppositeAlignmentPlacement),\n/* harmony export */   getOppositeAxis: () => (/* binding */ getOppositeAxis),\n/* harmony export */   getOppositeAxisPlacements: () => (/* binding */ getOppositeAxisPlacements),\n/* harmony export */   getOppositePlacement: () => (/* binding */ getOppositePlacement),\n/* harmony export */   getPaddingObject: () => (/* binding */ getPaddingObject),\n/* harmony export */   getSide: () => (/* binding */ getSide),\n/* harmony export */   getSideAxis: () => (/* binding */ getSideAxis),\n/* harmony export */   max: () => (/* binding */ max),\n/* harmony export */   min: () => (/* binding */ min),\n/* harmony export */   placements: () => (/* binding */ placements),\n/* harmony export */   rectToClientRect: () => (/* binding */ rectToClientRect),\n/* harmony export */   round: () => (/* binding */ round),\n/* harmony export */   sides: () => (/* binding */ sides)\n/* harmony export */ });\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return typeof key === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (typeof input !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (typeof res !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\n/**\n * Custom positioning reference element.\n * @see https://floating-ui.com/docs/virtual-elements\n */\n\nconst sides = ['top', 'right', 'bottom', 'left'];\nconst alignments = ['start', 'end'];\nconst placements = /*#__PURE__*/sides.reduce((acc, side) => acc.concat(side, side + \"-\" + alignments[0], side + \"-\" + alignments[1]), []);\nconst min = Math.min;\nconst max = Math.max;\nconst round = Math.round;\nconst floor = Math.floor;\nconst createCoords = v => ({\n  x: v,\n  y: v\n});\nconst oppositeSideMap = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nconst oppositeAlignmentMap = {\n  start: 'end',\n  end: 'start'\n};\nfunction clamp(start, value, end) {\n  return max(start, min(value, end));\n}\nfunction evaluate(value, param) {\n  return typeof value === 'function' ? value(param) : value;\n}\nfunction getSide(placement) {\n  return placement.split('-')[0];\n}\nfunction getAlignment(placement) {\n  return placement.split('-')[1];\n}\nfunction getOppositeAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}\nfunction getAxisLength(axis) {\n  return axis === 'y' ? 'height' : 'width';\n}\nfunction getSideAxis(placement) {\n  return ['top', 'bottom'].includes(getSide(placement)) ? 'y' : 'x';\n}\nfunction getAlignmentAxis(placement) {\n  return getOppositeAxis(getSideAxis(placement));\n}\nfunction getAlignmentSides(placement, rects, rtl) {\n  if (rtl === void 0) {\n    rtl = false;\n  }\n  const alignment = getAlignment(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const length = getAxisLength(alignmentAxis);\n  let mainAlignmentSide = alignmentAxis === 'x' ? alignment === (rtl ? 'end' : 'start') ? 'right' : 'left' : alignment === 'start' ? 'bottom' : 'top';\n  if (rects.reference[length] > rects.floating[length]) {\n    mainAlignmentSide = getOppositePlacement(mainAlignmentSide);\n  }\n  return [mainAlignmentSide, getOppositePlacement(mainAlignmentSide)];\n}\nfunction getExpandedPlacements(placement) {\n  const oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeAlignmentPlacement(placement), oppositePlacement, getOppositeAlignmentPlacement(oppositePlacement)];\n}\nfunction getOppositeAlignmentPlacement(placement) {\n  return placement.replace(/start|end/g, alignment => oppositeAlignmentMap[alignment]);\n}\nfunction getSideList(side, isStart, rtl) {\n  const lr = ['left', 'right'];\n  const rl = ['right', 'left'];\n  const tb = ['top', 'bottom'];\n  const bt = ['bottom', 'top'];\n  switch (side) {\n    case 'top':\n    case 'bottom':\n      if (rtl) return isStart ? rl : lr;\n      return isStart ? lr : rl;\n    case 'left':\n    case 'right':\n      return isStart ? tb : bt;\n    default:\n      return [];\n  }\n}\nfunction getOppositeAxisPlacements(placement, flipAlignment, direction, rtl) {\n  const alignment = getAlignment(placement);\n  let list = getSideList(getSide(placement), direction === 'start', rtl);\n  if (alignment) {\n    list = list.map(side => side + \"-\" + alignment);\n    if (flipAlignment) {\n      list = list.concat(list.map(getOppositeAlignmentPlacement));\n    }\n  }\n  return list;\n}\nfunction getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, side => oppositeSideMap[side]);\n}\nfunction expandPaddingObject(padding) {\n  return _objectSpread({\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  }, padding);\n}\nfunction getPaddingObject(padding) {\n  return typeof padding !== 'number' ? expandPaddingObject(padding) : {\n    top: padding,\n    right: padding,\n    bottom: padding,\n    left: padding\n  };\n}\nfunction rectToClientRect(rect) {\n  const {\n    x,\n    y,\n    width,\n    height\n  } = rect;\n  return {\n    width,\n    height,\n    top: y,\n    left: x,\n    right: x + width,\n    bottom: y + height,\n    x,\n    y\n  };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs\n");

/***/ })

};
;