"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_lib_supabase_admin_ts"],{

/***/ "(app-pages-browser)/./src/lib/supabase/admin.ts":
/*!***********************************!*\
  !*** ./src/lib/supabase/admin.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupabaseAdminService: () => (/* binding */ SupabaseAdminService),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_classCallCheck_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/classCallCheck.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/classCallCheck.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_createClass_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/createClass.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/createClass.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./node_modules/@babel/runtime/regenerator/index.js */ \"(app-pages-browser)/./node_modules/@babel/runtime/regenerator/index.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @supabase/supabase-js */ \"(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n\n\n\n\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\n\n// src/lib/supabase/admin.ts\n// Cliente administrativo de Supabase para operaciones del servidor\n\n// Cliente admin con privilegios elevados\nvar supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_5__.createClient)(\"https://fxnhpxjijinfuxxxplzj.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\n// Tipos para las nuevas tablas\n// Funciones de utilidad para operaciones administrativas\nvar SupabaseAdminService = /*#__PURE__*/ function() {\n    function SupabaseAdminService() {\n        (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_classCallCheck_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, SupabaseAdminService);\n    }\n    (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_createClass_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(SupabaseAdminService, null, [\n        {\n            key: \"createStripeTransaction\",\n            value: function() {\n                var _createStripeTransaction = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee(transaction) {\n                    var _yield$supabaseAdmin$, data, error;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee$(_context) {\n                        while(1)switch(_context.prev = _context.next){\n                            case 0:\n                                _context.next = 2;\n                                return supabaseAdmin.from('stripe_transactions').insert([\n                                    transaction\n                                ]).select().single();\n                            case 2:\n                                _yield$supabaseAdmin$ = _context.sent;\n                                data = _yield$supabaseAdmin$.data;\n                                error = _yield$supabaseAdmin$.error;\n                                if (!error) {\n                                    _context.next = 8;\n                                    break;\n                                }\n                                console.error('Error creating stripe transaction:', error);\n                                throw new Error(\"Failed to create transaction: \".concat(error.message));\n                            case 8:\n                                return _context.abrupt(\"return\", data);\n                            case 9:\n                            case \"end\":\n                                return _context.stop();\n                        }\n                    }, _callee);\n                }));\n                function createStripeTransaction(_x) {\n                    return _createStripeTransaction.apply(this, arguments);\n                }\n                return createStripeTransaction;\n            }() // Obtener transacción por session ID\n        },\n        {\n            key: \"getTransactionBySessionId\",\n            value: function() {\n                var _getTransactionBySessionId = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee2(sessionId) {\n                    var _yield$supabaseAdmin$2, data, error;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee2$(_context2) {\n                        while(1)switch(_context2.prev = _context2.next){\n                            case 0:\n                                _context2.next = 2;\n                                return supabaseAdmin.from('stripe_transactions').select('*').eq('stripe_session_id', sessionId).single();\n                            case 2:\n                                _yield$supabaseAdmin$2 = _context2.sent;\n                                data = _yield$supabaseAdmin$2.data;\n                                error = _yield$supabaseAdmin$2.error;\n                                if (!(error && error.code !== 'PGRST116')) {\n                                    _context2.next = 8;\n                                    break;\n                                }\n                                console.error('Error fetching transaction:', error);\n                                throw new Error(\"Failed to fetch transaction: \".concat(error.message));\n                            case 8:\n                                return _context2.abrupt(\"return\", data);\n                            case 9:\n                            case \"end\":\n                                return _context2.stop();\n                        }\n                    }, _callee2);\n                }));\n                function getTransactionBySessionId(_x2) {\n                    return _getTransactionBySessionId.apply(this, arguments);\n                }\n                return getTransactionBySessionId;\n            }() // Crear usuario con invitación\n        },\n        {\n            key: \"createUserWithInvitation\",\n            value: function() {\n                var _createUserWithInvitation = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee3(email, userData) {\n                    var _data$user, _data$user2, _data$user3, _data$user4, _data$user5, _data$user6, _data$user7;\n                    var _yield$supabaseAdmin$3, data, error;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee3$(_context3) {\n                        while(1)switch(_context3.prev = _context3.next){\n                            case 0:\n                                console.log('🔄 [SUPABASE_ADMIN] Creating user invitation:', {\n                                    email: email,\n                                    userData: userData,\n                                    redirectTo: \"\".concat(\"http://localhost:3000\", \"/auth/callback\"),\n                                    timestamp: new Date().toISOString()\n                                });\n                                _context3.next = 3;\n                                return supabaseAdmin.auth.admin.inviteUserByEmail(email, {\n                                    data: userData,\n                                    redirectTo: \"\".concat(\"http://localhost:3000\", \"/auth/callback\")\n                                });\n                            case 3:\n                                _yield$supabaseAdmin$3 = _context3.sent;\n                                data = _yield$supabaseAdmin$3.data;\n                                error = _yield$supabaseAdmin$3.error;\n                                console.log('📊 [SUPABASE_ADMIN] Invitation result:', {\n                                    hasData: !!data,\n                                    hasUser: !!(data !== null && data !== void 0 && data.user),\n                                    userId: data === null || data === void 0 || (_data$user = data.user) === null || _data$user === void 0 ? void 0 : _data$user.id,\n                                    userEmail: data === null || data === void 0 || (_data$user2 = data.user) === null || _data$user2 === void 0 ? void 0 : _data$user2.email,\n                                    userAud: data === null || data === void 0 || (_data$user3 = data.user) === null || _data$user3 === void 0 ? void 0 : _data$user3.aud,\n                                    userRole: data === null || data === void 0 || (_data$user4 = data.user) === null || _data$user4 === void 0 ? void 0 : _data$user4.role,\n                                    emailConfirmed: data === null || data === void 0 || (_data$user5 = data.user) === null || _data$user5 === void 0 ? void 0 : _data$user5.email_confirmed_at,\n                                    userMetadata: data === null || data === void 0 || (_data$user6 = data.user) === null || _data$user6 === void 0 ? void 0 : _data$user6.user_metadata,\n                                    appMetadata: data === null || data === void 0 || (_data$user7 = data.user) === null || _data$user7 === void 0 ? void 0 : _data$user7.app_metadata,\n                                    error: error === null || error === void 0 ? void 0 : error.message,\n                                    errorCode: error === null || error === void 0 ? void 0 : error.status,\n                                    fullError: error\n                                });\n                                if (!error) {\n                                    _context3.next = 10;\n                                    break;\n                                }\n                                console.error('❌ [SUPABASE_ADMIN] Error creating user invitation:', {\n                                    message: error.message,\n                                    status: error.status,\n                                    details: error\n                                });\n                                throw new Error(\"Failed to create user invitation: \".concat(error.message));\n                            case 10:\n                                return _context3.abrupt(\"return\", data);\n                            case 11:\n                            case \"end\":\n                                return _context3.stop();\n                        }\n                    }, _callee3);\n                }));\n                function createUserWithInvitation(_x3, _x4) {\n                    return _createUserWithInvitation.apply(this, arguments);\n                }\n                return createUserWithInvitation;\n            }() // Crear usuario con contraseña específica y opcionalmente enviar email de confirmación\n        },\n        {\n            key: \"createUserWithPassword\",\n            value: function() {\n                var _createUserWithPassword = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee4(email, password, userData) {\n                    var _data$user8, _data$user9, _data$user10, _data$user11;\n                    var sendConfirmationEmail, _yield$supabaseAdmin$4, data, error, _yield$supabaseAdmin$5, emailError, _args4 = arguments;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee4$(_context4) {\n                        while(1)switch(_context4.prev = _context4.next){\n                            case 0:\n                                sendConfirmationEmail = _args4.length > 3 && _args4[3] !== undefined ? _args4[3] : true;\n                                console.log('🔄 [SUPABASE_ADMIN] Creating user with password:', {\n                                    email: email,\n                                    userData: userData,\n                                    sendConfirmationEmail: sendConfirmationEmail,\n                                    timestamp: new Date().toISOString()\n                                });\n                                _context4.next = 4;\n                                return supabaseAdmin.auth.admin.createUser({\n                                    email: email,\n                                    password: password,\n                                    user_metadata: userData,\n                                    email_confirm: false // No confirmar automáticamente\n                                });\n                            case 4:\n                                _yield$supabaseAdmin$4 = _context4.sent;\n                                data = _yield$supabaseAdmin$4.data;\n                                error = _yield$supabaseAdmin$4.error;\n                                console.log('📊 [SUPABASE_ADMIN] User creation result:', {\n                                    hasData: !!data,\n                                    hasUser: !!(data !== null && data !== void 0 && data.user),\n                                    userId: data === null || data === void 0 || (_data$user8 = data.user) === null || _data$user8 === void 0 ? void 0 : _data$user8.id,\n                                    userEmail: data === null || data === void 0 || (_data$user9 = data.user) === null || _data$user9 === void 0 ? void 0 : _data$user9.email,\n                                    emailConfirmed: data === null || data === void 0 || (_data$user10 = data.user) === null || _data$user10 === void 0 ? void 0 : _data$user10.email_confirmed_at,\n                                    userMetadata: data === null || data === void 0 || (_data$user11 = data.user) === null || _data$user11 === void 0 ? void 0 : _data$user11.user_metadata,\n                                    error: error === null || error === void 0 ? void 0 : error.message,\n                                    errorCode: error === null || error === void 0 ? void 0 : error.status\n                                });\n                                if (!error) {\n                                    _context4.next = 11;\n                                    break;\n                                }\n                                console.error('❌ [SUPABASE_ADMIN] Error creating user with password:', {\n                                    message: error.message,\n                                    status: error.status,\n                                    details: error\n                                });\n                                return _context4.abrupt(\"return\", {\n                                    data: null,\n                                    error: error\n                                });\n                            case 11:\n                                if (!(data !== null && data !== void 0 && data.user && sendConfirmationEmail)) {\n                                    _context4.next = 20;\n                                    break;\n                                }\n                                console.log('📧 Enviando email de confirmación...');\n                                _context4.next = 15;\n                                return supabaseAdmin.auth.admin.generateLink({\n                                    type: 'signup',\n                                    email: email,\n                                    password: password,\n                                    // Requerido para generateLink\n                                    options: {\n                                        redirectTo: \"\".concat(\"http://localhost:3000\", \"/auth/confirmed\")\n                                    }\n                                });\n                            case 15:\n                                _yield$supabaseAdmin$5 = _context4.sent;\n                                emailError = _yield$supabaseAdmin$5.error;\n                                if (emailError) {\n                                    console.error('⚠️ Error enviando email de confirmación:', emailError);\n                                // No fallar completamente, el usuario puede confirmar manualmente\n                                } else {\n                                    console.log('✅ Email de confirmación enviado exitosamente');\n                                }\n                                _context4.next = 21;\n                                break;\n                            case 20:\n                                if (data !== null && data !== void 0 && data.user && !sendConfirmationEmail) {\n                                    console.log('📧 Email de confirmación omitido (se enviará después del pago)');\n                                }\n                            case 21:\n                                return _context4.abrupt(\"return\", {\n                                    data: data,\n                                    error: null\n                                });\n                            case 22:\n                            case \"end\":\n                                return _context4.stop();\n                        }\n                    }, _callee4);\n                }));\n                function createUserWithPassword(_x5, _x6, _x7) {\n                    return _createUserWithPassword.apply(this, arguments);\n                }\n                return createUserWithPassword;\n            }() // Enviar email de confirmación para usuario existente\n        },\n        {\n            key: \"sendConfirmationEmailForUser\",\n            value: function() {\n                var _sendConfirmationEmailForUser = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee5(userId) {\n                    var _yield$supabaseAdmin$6, userData, userError, user, _yield$supabaseAdmin$7, updateError;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee5$(_context5) {\n                        while(1)switch(_context5.prev = _context5.next){\n                            case 0:\n                                console.log('📧 [SUPABASE_ADMIN] Enviando email de confirmación para usuario:', userId);\n                                _context5.prev = 1;\n                                _context5.next = 4;\n                                return supabaseAdmin.auth.admin.getUserById(userId);\n                            case 4:\n                                _yield$supabaseAdmin$6 = _context5.sent;\n                                userData = _yield$supabaseAdmin$6.data;\n                                userError = _yield$supabaseAdmin$6.error;\n                                if (!(userError || !(userData !== null && userData !== void 0 && userData.user))) {\n                                    _context5.next = 10;\n                                    break;\n                                }\n                                console.error('Error obteniendo datos del usuario:', userError);\n                                return _context5.abrupt(\"return\", {\n                                    success: false,\n                                    error: 'Usuario no encontrado'\n                                });\n                            case 10:\n                                user = userData.user; // Para usuarios pre-registrados, actualizar el estado de confirmación directamente\n                                // ya que el pago exitoso confirma la intención del usuario\n                                _context5.next = 13;\n                                return supabaseAdmin.auth.admin.updateUserById(user.id, {\n                                    email_confirm: true,\n                                    user_metadata: _objectSpread(_objectSpread({}, user.user_metadata), {}, {\n                                        payment_verified: true,\n                                        email_confirmed_via_payment: true,\n                                        confirmed_at: new Date().toISOString()\n                                    })\n                                });\n                            case 13:\n                                _yield$supabaseAdmin$7 = _context5.sent;\n                                updateError = _yield$supabaseAdmin$7.error;\n                                if (!updateError) {\n                                    _context5.next = 18;\n                                    break;\n                                }\n                                console.error('⚠️ Error confirmando email del usuario:', updateError);\n                                return _context5.abrupt(\"return\", {\n                                    success: false,\n                                    error: updateError.message\n                                });\n                            case 18:\n                                console.log('✅ Usuario confirmado automáticamente después del pago exitoso');\n                                return _context5.abrupt(\"return\", {\n                                    success: true\n                                });\n                            case 22:\n                                _context5.prev = 22;\n                                _context5.t0 = _context5[\"catch\"](1);\n                                console.error('Error en sendConfirmationEmailForUser:', _context5.t0);\n                                return _context5.abrupt(\"return\", {\n                                    success: false,\n                                    error: _context5.t0 instanceof Error ? _context5.t0.message : 'Error desconocido'\n                                });\n                            case 26:\n                            case \"end\":\n                                return _context5.stop();\n                        }\n                    }, _callee5, null, [\n                        [\n                            1,\n                            22\n                        ]\n                    ]);\n                }));\n                function sendConfirmationEmailForUser(_x8) {\n                    return _sendConfirmationEmailForUser.apply(this, arguments);\n                }\n                return sendConfirmationEmailForUser;\n            }() // Enviar email de confirmación para usuario existente (método legacy)\n        },\n        {\n            key: \"sendConfirmationEmail\",\n            value: function() {\n                var _sendConfirmationEmail = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee6(email, password) {\n                    var _yield$supabaseAdmin$8, emailError;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee6$(_context6) {\n                        while(1)switch(_context6.prev = _context6.next){\n                            case 0:\n                                console.log('📧 [SUPABASE_ADMIN] Enviando email de confirmación para:', email);\n                                _context6.next = 3;\n                                return supabaseAdmin.auth.admin.generateLink({\n                                    type: 'signup',\n                                    email: email,\n                                    password: password,\n                                    options: {\n                                        redirectTo: \"\".concat(\"http://localhost:3000\", \"/auth/confirmed\")\n                                    }\n                                });\n                            case 3:\n                                _yield$supabaseAdmin$8 = _context6.sent;\n                                emailError = _yield$supabaseAdmin$8.error;\n                                if (!emailError) {\n                                    _context6.next = 10;\n                                    break;\n                                }\n                                console.error('⚠️ Error enviando email de confirmación:', emailError);\n                                return _context6.abrupt(\"return\", {\n                                    success: false,\n                                    error: emailError.message\n                                });\n                            case 10:\n                                console.log('✅ Email de confirmación enviado exitosamente');\n                                return _context6.abrupt(\"return\", {\n                                    success: true\n                                });\n                            case 12:\n                            case \"end\":\n                                return _context6.stop();\n                        }\n                    }, _callee6);\n                }));\n                function sendConfirmationEmail(_x9, _x10) {\n                    return _sendConfirmationEmail.apply(this, arguments);\n                }\n                return sendConfirmationEmail;\n            }() // Crear perfil de usuario\n        },\n        {\n            key: \"createUserProfile\",\n            value: function() {\n                var _createUserProfile = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee7(profile) {\n                    var _yield$supabaseAdmin$9, data, error;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee7$(_context7) {\n                        while(1)switch(_context7.prev = _context7.next){\n                            case 0:\n                                _context7.next = 2;\n                                return supabaseAdmin.from('user_profiles').insert([\n                                    profile\n                                ]).select().single();\n                            case 2:\n                                _yield$supabaseAdmin$9 = _context7.sent;\n                                data = _yield$supabaseAdmin$9.data;\n                                error = _yield$supabaseAdmin$9.error;\n                                if (!error) {\n                                    _context7.next = 8;\n                                    break;\n                                }\n                                console.error('Error creating user profile:', error);\n                                throw new Error(\"Failed to create user profile: \".concat(error.message));\n                            case 8:\n                                return _context7.abrupt(\"return\", data);\n                            case 9:\n                            case \"end\":\n                                return _context7.stop();\n                        }\n                    }, _callee7);\n                }));\n                function createUserProfile(_x11) {\n                    return _createUserProfile.apply(this, arguments);\n                }\n                return createUserProfile;\n            }() // Crear o actualizar perfil de usuario\n        },\n        {\n            key: \"upsertUserProfile\",\n            value: function() {\n                var _upsertUserProfile = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee8(profile) {\n                    var _yield$supabaseAdmin$10, data, error;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee8$(_context8) {\n                        while(1)switch(_context8.prev = _context8.next){\n                            case 0:\n                                _context8.next = 2;\n                                return supabaseAdmin.from('user_profiles').upsert([\n                                    profile\n                                ], {\n                                    onConflict: 'user_id'\n                                }).select().single();\n                            case 2:\n                                _yield$supabaseAdmin$10 = _context8.sent;\n                                data = _yield$supabaseAdmin$10.data;\n                                error = _yield$supabaseAdmin$10.error;\n                                if (!error) {\n                                    _context8.next = 8;\n                                    break;\n                                }\n                                console.error('Error upserting user profile:', error);\n                                throw new Error(\"Failed to upsert user profile: \".concat(error.message));\n                            case 8:\n                                return _context8.abrupt(\"return\", data);\n                            case 9:\n                            case \"end\":\n                                return _context8.stop();\n                        }\n                    }, _callee8);\n                }));\n                function upsertUserProfile(_x12) {\n                    return _upsertUserProfile.apply(this, arguments);\n                }\n                return upsertUserProfile;\n            }() // Registrar cambio de plan\n        },\n        {\n            key: \"logPlanChange\",\n            value: function() {\n                var _logPlanChange = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee9(planChange) {\n                    var _yield$supabaseAdmin$11, data, error;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee9$(_context9) {\n                        while(1)switch(_context9.prev = _context9.next){\n                            case 0:\n                                _context9.next = 2;\n                                return supabaseAdmin.from('user_plan_history').insert([\n                                    planChange\n                                ]).select().single();\n                            case 2:\n                                _yield$supabaseAdmin$11 = _context9.sent;\n                                data = _yield$supabaseAdmin$11.data;\n                                error = _yield$supabaseAdmin$11.error;\n                                if (!error) {\n                                    _context9.next = 8;\n                                    break;\n                                }\n                                console.error('Error logging plan change:', error);\n                                throw new Error(\"Failed to log plan change: \".concat(error.message));\n                            case 8:\n                                return _context9.abrupt(\"return\", data);\n                            case 9:\n                            case \"end\":\n                                return _context9.stop();\n                        }\n                    }, _callee9);\n                }));\n                function logPlanChange(_x13) {\n                    return _logPlanChange.apply(this, arguments);\n                }\n                return logPlanChange;\n            }() // Registrar acceso a característica\n        },\n        {\n            key: \"logFeatureAccess\",\n            value: function() {\n                var _logFeatureAccess = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee10(accessLog) {\n                    var _yield$supabaseAdmin$12, data, error;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee10$(_context10) {\n                        while(1)switch(_context10.prev = _context10.next){\n                            case 0:\n                                _context10.next = 2;\n                                return supabaseAdmin.from('feature_access_log').insert([\n                                    accessLog\n                                ]).select().single();\n                            case 2:\n                                _yield$supabaseAdmin$12 = _context10.sent;\n                                data = _yield$supabaseAdmin$12.data;\n                                error = _yield$supabaseAdmin$12.error;\n                                if (!error) {\n                                    _context10.next = 8;\n                                    break;\n                                }\n                                console.error('Error logging feature access:', error);\n                                throw new Error(\"Failed to log feature access: \".concat(error.message));\n                            case 8:\n                                return _context10.abrupt(\"return\", data);\n                            case 9:\n                            case \"end\":\n                                return _context10.stop();\n                        }\n                    }, _callee10);\n                }));\n                function logFeatureAccess(_x14) {\n                    return _logFeatureAccess.apply(this, arguments);\n                }\n                return logFeatureAccess;\n            }() // Obtener perfil de usuario por ID\n        },\n        {\n            key: \"getUserProfile\",\n            value: function() {\n                var _getUserProfile = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee11(userId) {\n                    var _yield$supabaseAdmin$13, data, error;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee11$(_context11) {\n                        while(1)switch(_context11.prev = _context11.next){\n                            case 0:\n                                _context11.next = 2;\n                                return supabaseAdmin.from('user_profiles').select('*').eq('user_id', userId).single();\n                            case 2:\n                                _yield$supabaseAdmin$13 = _context11.sent;\n                                data = _yield$supabaseAdmin$13.data;\n                                error = _yield$supabaseAdmin$13.error;\n                                if (!(error && error.code !== 'PGRST116')) {\n                                    _context11.next = 8;\n                                    break;\n                                }\n                                console.error('Error fetching user profile:', error);\n                                throw new Error(\"Failed to fetch user profile: \".concat(error.message));\n                            case 8:\n                                return _context11.abrupt(\"return\", data);\n                            case 9:\n                            case \"end\":\n                                return _context11.stop();\n                        }\n                    }, _callee11);\n                }));\n                function getUserProfile(_x15) {\n                    return _getUserProfile.apply(this, arguments);\n                }\n                return getUserProfile;\n            }() // Actualizar transacción con user_id\n        },\n        {\n            key: \"updateTransactionWithUser\",\n            value: function() {\n                var _updateTransactionWithUser = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee12(transactionId, userId) {\n                    var _yield$supabaseAdmin$14, error;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee12$(_context12) {\n                        while(1)switch(_context12.prev = _context12.next){\n                            case 0:\n                                _context12.next = 2;\n                                return supabaseAdmin.from('stripe_transactions').update({\n                                    user_id: userId,\n                                    updated_at: new Date().toISOString()\n                                }).eq('id', transactionId);\n                            case 2:\n                                _yield$supabaseAdmin$14 = _context12.sent;\n                                error = _yield$supabaseAdmin$14.error;\n                                if (!error) {\n                                    _context12.next = 7;\n                                    break;\n                                }\n                                console.error('Error updating transaction with user_id:', error);\n                                throw new Error(\"Failed to update transaction: \".concat(error.message));\n                            case 7:\n                            case \"end\":\n                                return _context12.stop();\n                        }\n                    }, _callee12);\n                }));\n                function updateTransactionWithUser(_x16, _x17) {\n                    return _updateTransactionWithUser.apply(this, arguments);\n                }\n                return updateTransactionWithUser;\n            }() // Activar transacción (marcar como activada)\n        },\n        {\n            key: \"activateTransaction\",\n            value: function() {\n                var _activateTransaction = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee13(transactionId) {\n                    var _yield$supabaseAdmin$15, error;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee13$(_context13) {\n                        while(1)switch(_context13.prev = _context13.next){\n                            case 0:\n                                _context13.next = 2;\n                                return supabaseAdmin.from('stripe_transactions').update({\n                                    activated_at: new Date().toISOString()\n                                }).eq('id', transactionId);\n                            case 2:\n                                _yield$supabaseAdmin$15 = _context13.sent;\n                                error = _yield$supabaseAdmin$15.error;\n                                if (!error) {\n                                    _context13.next = 7;\n                                    break;\n                                }\n                                console.error('Error activating transaction:', error);\n                                throw new Error(\"Failed to activate transaction: \".concat(error.message));\n                            case 7:\n                            case \"end\":\n                                return _context13.stop();\n                        }\n                    }, _callee13);\n                }));\n                function activateTransaction(_x18) {\n                    return _activateTransaction.apply(this, arguments);\n                }\n                return activateTransaction;\n            }() // Obtener conteo de documentos del usuario\n        },\n        {\n            key: \"getDocumentsCount\",\n            value: function() {\n                var _getDocumentsCount = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee14(userId) {\n                    var _yield$supabaseAdmin$16, count, error;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee14$(_context14) {\n                        while(1)switch(_context14.prev = _context14.next){\n                            case 0:\n                                _context14.next = 2;\n                                return supabaseAdmin.from('documentos').select('*', {\n                                    count: 'exact',\n                                    head: true\n                                }).eq('user_id', userId);\n                            case 2:\n                                _yield$supabaseAdmin$16 = _context14.sent;\n                                count = _yield$supabaseAdmin$16.count;\n                                error = _yield$supabaseAdmin$16.error;\n                                if (!error) {\n                                    _context14.next = 8;\n                                    break;\n                                }\n                                console.error('Error getting documents count:', error);\n                                return _context14.abrupt(\"return\", 0);\n                            case 8:\n                                return _context14.abrupt(\"return\", count || 0);\n                            case 9:\n                            case \"end\":\n                                return _context14.stop();\n                        }\n                    }, _callee14);\n                }));\n                function getDocumentsCount(_x19) {\n                    return _getDocumentsCount.apply(this, arguments);\n                }\n                return getDocumentsCount;\n            }() // Obtener usuario por email desde Supabase Auth\n        },\n        {\n            key: \"getUserByEmail\",\n            value: function() {\n                var _getUserByEmail = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee15(email) {\n                    var _yield$supabaseAdmin$17, users, error, user;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee15$(_context15) {\n                        while(1)switch(_context15.prev = _context15.next){\n                            case 0:\n                                _context15.prev = 0;\n                                _context15.next = 3;\n                                return supabaseAdmin.auth.admin.listUsers();\n                            case 3:\n                                _yield$supabaseAdmin$17 = _context15.sent;\n                                users = _yield$supabaseAdmin$17.data.users;\n                                error = _yield$supabaseAdmin$17.error;\n                                if (!error) {\n                                    _context15.next = 9;\n                                    break;\n                                }\n                                console.error('Error getting user by email:', error);\n                                throw new Error(\"Failed to get user by email: \".concat(error.message));\n                            case 9:\n                                if (!(!users || users.length === 0)) {\n                                    _context15.next = 11;\n                                    break;\n                                }\n                                return _context15.abrupt(\"return\", null);\n                            case 11:\n                                // Filtrar por email ya que la API no permite filtro directo\n                                user = users.find(function(u) {\n                                    return u.email === email;\n                                });\n                                if (user) {\n                                    _context15.next = 14;\n                                    break;\n                                }\n                                return _context15.abrupt(\"return\", null);\n                            case 14:\n                                return _context15.abrupt(\"return\", {\n                                    id: user.id,\n                                    email: user.email,\n                                    email_confirmed_at: user.email_confirmed_at\n                                });\n                            case 17:\n                                _context15.prev = 17;\n                                _context15.t0 = _context15[\"catch\"](0);\n                                console.error('Error in getUserByEmail:', _context15.t0);\n                                throw _context15.t0;\n                            case 21:\n                            case \"end\":\n                                return _context15.stop();\n                        }\n                    }, _callee15, null, [\n                        [\n                            0,\n                            17\n                        ]\n                    ]);\n                }));\n                function getUserByEmail(_x20) {\n                    return _getUserByEmail.apply(this, arguments);\n                }\n                return getUserByEmail;\n            }()\n        }\n    ]);\n    return SupabaseAdminService;\n}();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase/admin.ts\n"));

/***/ })

}]);