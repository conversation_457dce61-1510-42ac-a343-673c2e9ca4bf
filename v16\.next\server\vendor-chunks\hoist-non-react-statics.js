"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hoist-non-react-statics";
exports.ids = ["vendor-chunks/hoist-non-react-statics"];
exports.modules = {

/***/ "(ssr)/./node_modules/hoist-non-react-statics/dist/hoist-non-react-statics.cjs.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/hoist-non-react-statics/dist/hoist-non-react-statics.cjs.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar reactIs = __webpack_require__(/*! react-is */ \"(ssr)/./node_modules/react-is/index.js\");\n\n/**\n * Copyright 2015, Yahoo! Inc.\n * Copyrights licensed under the New BSD License. See the accompanying LICENSE file for terms.\n */\nvar REACT_STATICS = {\n  childContextTypes: true,\n  contextType: true,\n  contextTypes: true,\n  defaultProps: true,\n  displayName: true,\n  getDefaultProps: true,\n  getDerivedStateFromError: true,\n  getDerivedStateFromProps: true,\n  mixins: true,\n  propTypes: true,\n  type: true\n};\nvar KNOWN_STATICS = {\n  name: true,\n  length: true,\n  prototype: true,\n  caller: true,\n  callee: true,\n  arguments: true,\n  arity: true\n};\nvar FORWARD_REF_STATICS = {\n  '$$typeof': true,\n  render: true,\n  defaultProps: true,\n  displayName: true,\n  propTypes: true\n};\nvar MEMO_STATICS = {\n  '$$typeof': true,\n  compare: true,\n  defaultProps: true,\n  displayName: true,\n  propTypes: true,\n  type: true\n};\nvar TYPE_STATICS = {};\nTYPE_STATICS[reactIs.ForwardRef] = FORWARD_REF_STATICS;\nTYPE_STATICS[reactIs.Memo] = MEMO_STATICS;\nfunction getStatics(component) {\n  // React v16.11 and below\n  if (reactIs.isMemo(component)) {\n    return MEMO_STATICS;\n  } // React v16.12 and above\n\n  return TYPE_STATICS[component['$$typeof']] || REACT_STATICS;\n}\nvar defineProperty = Object.defineProperty;\nvar getOwnPropertyNames = Object.getOwnPropertyNames;\nvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\nvar getPrototypeOf = Object.getPrototypeOf;\nvar objectPrototype = Object.prototype;\nfunction hoistNonReactStatics(targetComponent, sourceComponent, blacklist) {\n  if (typeof sourceComponent !== 'string') {\n    // don't hoist over string (html) components\n    if (objectPrototype) {\n      var inheritedComponent = getPrototypeOf(sourceComponent);\n      if (inheritedComponent && inheritedComponent !== objectPrototype) {\n        hoistNonReactStatics(targetComponent, inheritedComponent, blacklist);\n      }\n    }\n    var keys = getOwnPropertyNames(sourceComponent);\n    if (getOwnPropertySymbols) {\n      keys = keys.concat(getOwnPropertySymbols(sourceComponent));\n    }\n    var targetStatics = getStatics(targetComponent);\n    var sourceStatics = getStatics(sourceComponent);\n    for (var i = 0; i < keys.length; ++i) {\n      var key = keys[i];\n      if (!KNOWN_STATICS[key] && !(blacklist && blacklist[key]) && !(sourceStatics && sourceStatics[key]) && !(targetStatics && targetStatics[key])) {\n        var descriptor = getOwnPropertyDescriptor(sourceComponent, key);\n        try {\n          // Avoid failures from read-only properties\n          defineProperty(targetComponent, key, descriptor);\n        } catch (e) {}\n      }\n    }\n  }\n  return targetComponent;\n}\nmodule.exports = hoistNonReactStatics;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hoist-non-react-statics/dist/hoist-non-react-statics.cjs.js\n");

/***/ })

};
;